import type {
  ActionFunctionArgs,
  TypedResponse,
} from "@remix-run/node";
import {
  unstable_createMemoryUpload<PERSON>and<PERSON> as createMemoryUploadHandler,
  unstable_parseMultipartFormData as parseMultipartFormData,
} from "@remix-run/node";
import { redirect } from "@remix-run/react";
import { middleware } from "~/lib/middlewares.server";
import { createDocument } from "~/services/api-generated";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { getDocumentType } from "~/lib/utilities/files";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function action({ request }: ActionFunctionArgs): Promise<string | TypedResponse<never>> {
  const session = await getSession(request.headers.get("Cookie"));
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const maxPartSize = 5 * 1024 * 1024; // 5MB in bytes
  const uploadHandler = createMemoryUploadHandler({
    maxPartSize,
  });
  const formData = await parseMultipartFormData(request, uploadHandler);
  const file = formData.get("file") as File
  const location = formData.get("location") as string || "/"
  const { data, error } = await createDocument({ headers: await authHeaders(request), body: { File: file, Type: getDocumentType(file) } });

  if (error) {
    session.flash("notification", { title: "Error!", message: error.exceptionMessage, variant: "error" });

    return redirect(location, {
      headers: { "Set-Cookie": await commitSession(session) },
    });
  }

  return data
}
