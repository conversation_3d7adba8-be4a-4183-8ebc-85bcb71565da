import { Badge } from "@netpro/design-system";
import type { JSX } from "react";
import { formatDate } from "~/lib/utilities/format";
import type {
  BusinessActivityType,
} from "~/lib/simplified-tax-return/types/business-activity/2019-2024/business-activity-schema";
import {
  BusinessActivity,
} from "~/lib/simplified-tax-return/types/business-activity/2019-2024/business-activity-schema";

type BusinessActivityRowProps = {
  businessActivity: BusinessActivityType & { formArrayId: string }
  onClick: (businessActivity: BusinessActivityType, index: number) => void
  index: number
}

export function BusinessActivityRow({ businessActivity, onClick, index }: BusinessActivityRowProps): JSX.Element {
  return (
    <div
      key={businessActivity.formArrayId}
      className="flex px-3 space-x-8 items-center transition-all duration-150 ring-2 ring-transparent
      hover:ring-primary ring-inset group cursor-pointer
      first-of-type:rounded-t-md last-of-type:rounded-b-md"
      onClick={() => onClick(businessActivity, index)}
    >
      <div className="p-3 flex items-center justify-between">
        <div className="flex flex-col gap-1">
          <div className="inline-flex items-center gap-3">
            <span className="font-semibold text-nowrap group-hover:text-primary transition-colors duration-150">
              {businessActivity.activity === BusinessActivity.OTHER ? businessActivity.otherActivity : businessActivity.activity}
            </span>
            <Badge variant={businessActivity.type === "Primary" ? "primary" : "secondary"} className="border-white">
              {businessActivity.type}
            </Badge>
          </div>
          <span className="text-sm text-gray-700 text-nowrap">
            {`From ${formatDate(businessActivity.from!)} to ${formatDate(businessActivity.to!)}`}
          </span>
        </div>
      </div>
    </div>
  );
}
