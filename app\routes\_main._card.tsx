import type { JS<PERSON> } from "react";
import { Outlet, useLoaderData } from "@remix-run/react";
import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import { PageHeading } from "~/components/layout/headings/PageHeading";
import { getSessionData } from "~/lib/auth/utils/session.server";
import type { SessionData } from "~/lib/auth/types/session-type";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<{
  currentCompany: SessionData["currentCompany"]
}>> {
  const { currentCompany } = await getSessionData(request);

  return json({
    currentCompany,
  })
}

export default function CardLayout(): JSX.Element {
  const { currentCompany } = useLoaderData<typeof loader>();

  return (
    <main className="flex flex-col min-h-screen py-9 px-10 lg:pl-[290px] bg-gray-50">
      <header className="flex w-full 1 pb-7">
        <PageHeading headerTitle={currentCompany?.companyName} />
      </header>
      <section className="flex grow w-full rounded border border-gray-200 py-4 px-5 gap-2.5 bg-white">
        <Outlet />
      </section>
    </main>
  );
}
