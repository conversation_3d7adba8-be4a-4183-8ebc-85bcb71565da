import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Form } from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import type { ReactNode } from "react";
import { useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { EmployeesSection } from "../sections/EmployeesSection";
import { ExpenditureSection } from "../sections/ExpenditureSection";
import { IncomeSection } from "../sections/IncomeSection";
import { LawsRegulationsSection } from "../sections/LawsRegulationsSection";
import { PremisesSection } from "../sections/PremisesSection";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { getEmployeesDefaultValues } from "~/lib/economic-substance/types/bahamas/employee-schema";
import { getExpenditureDefaultValues } from "~/lib/economic-substance/types/bahamas/expenditure-schema";
import { type HoldingBusinessSchemaType, holdingBusinessSchema } from "~/lib/economic-substance/types/bahamas/holding-business-schema";
import { getIncomeDefaultValues } from "~/lib/economic-substance/types/bahamas/income-schema";
import { getLawsRegulationsDefaultValues } from "~/lib/economic-substance/types/bahamas/laws-regulations-schema";
import { getPremisesDefaultValues } from "~/lib/economic-substance/types/bahamas/premises-schema";
import { ECONOMIC_SUBSTANCE_FORM_ID } from "~/lib/economic-substance/utilities/constants";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { decodeFields, encodeFields } from "~/lib/utilities/hashPrefix";

export function HoldingBusiness(): ReactNode {
  const { submissionData } = useSubmission();
  const fetcher = useFetcher()
  const isSubmitting = fetcher.state === "submitting" || fetcher.state === "loading"
  const data = useMemo(() => {
    const rawData = submissionData[Pages.HOLDING_BUSINESS] as HoldingBusinessSchemaType;
    const premisesArray = Array.isArray(rawData?.premises) ? rawData.premises : [];
    const decodedPremises = premisesArray.map((premise) => {
      return decodeFields(premise, ["addressLine1", "addressLine2"]);
    });

    return {
      ...rawData,
      premises: decodedPremises,
    };
  }, [submissionData]);
  const form = useForm<HoldingBusinessSchemaType>({
    resolver: zodResolver(holdingBusinessSchema),
    shouldFocusError: false,
    defaultValues: {
      ...getIncomeDefaultValues(data),
      ...getExpenditureDefaultValues(data),
      ...getEmployeesDefaultValues(data),
      ...getPremisesDefaultValues(data),
      ...getLawsRegulationsDefaultValues(data),
    },

  });
  const { formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  function onSubmit(data: HoldingBusinessSchemaType): void {
    const encodedPremises = data.premises?.map((premise) => {
      return encodeFields(premise, ["addressLine1", "addressLine2"]);
    });
    const encodedData: HoldingBusinessSchemaType = {
      ...data,
      premises: encodedPremises,
    };
    fetcher.submit({ data: JSON.stringify(encodedData) }, {
      method: "post",
    });
  }

  return (
    <div className="relative">
      <FormProvider {...form}>
        <Form {...form}>
          <RemixForm
            onSubmit={form.handleSubmit(onSubmit, onError)}
            noValidate
            id={ECONOMIC_SUBSTANCE_FORM_ID}
            className="space-y-5"
          >
            <div className="flex-col space-y-7">
              <IncomeSection />
              <ExpenditureSection />
              <EmployeesSection />
              <PremisesSection />
              <LawsRegulationsSection />
            </div>
          </RemixForm>
        </Form>
      </FormProvider>
      <LoadingState
        isLoading={isSubmitting}
        message="Saving..."
      />
    </div>
  )
}
