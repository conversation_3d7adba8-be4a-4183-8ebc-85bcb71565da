import { Button, FormControl, FormField, FormItem, FormLabel, FormMessage, Input, RadioGroup, RadioGroupItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { useNavigation } from "@remix-run/react";
import { useFieldArray, useForm, useFormContext } from "react-hook-form";
import { Info, Plus } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { DeletePropertyDialog } from "../../dialogs/DeletePropertyDialog";
import { DirectorsTable } from "../../tables/sections/DirectorsTable";
import { DirectorDialog } from "../../dialogs/sections/DirectorDialog";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { type DirectionManagementSchemaType, type DirectorSchemaType, directorSchema } from "~/lib/economic-substance/types/bahamas/direction-management-schema";

export type ArrayFieldName = keyof Pick<DirectionManagementSchemaType, "directors">;
export function DirectionManagementSection() {
  const form = useFormContext<DirectionManagementSchemaType>()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  // Director table logic
  const [index, setIndex] = useState<number | undefined>();
  const [arrayFieldName, setArrayFieldName] = useState<ArrayFieldName | undefined>()
  const [openDialog, setOpenDialog] = useState(false)
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const directorForm = useForm<DirectorSchemaType>({
    resolver: zodResolver(directorSchema),
    defaultValues: {
      name: "",
      isResidentInBahamas: undefined,
      relationToEntity: "",
      meetingsAttended: undefined,
      meetingNumber: undefined,
      physicallyPresentInBahamas: undefined,
      qualification: "",
      yearsOfExperience: "",
    },
  });
  const directorArray = useFieldArray({
    control: form.control,
    name: "directors",
    keyName: "formArrayId",
  });
  function addDirector(fieldName: ArrayFieldName): void {
    directorForm.reset();
    setArrayFieldName(fieldName)
    setIndex(undefined);
    setOpenDialog(true);
  }

  function onSelect(fieldName: ArrayFieldName, director: DirectorSchemaType, index: number): void {
    setArrayFieldName(fieldName)
    directorForm.reset(director, { keepDefaultValues: true });
    setIndex(index);
    setOpenDialog(true);
  }

  function onDelete(): void {
    if (arrayFieldName === "directors") {
      directorArray.remove(index);
    }

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(fieldName: ArrayFieldName, index: number): void {
    setArrayFieldName(fieldName)
    setIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  function onSubmitDirector(data: DirectorSchemaType): void {
    if (arrayFieldName === "directors") {
      if (index !== undefined) {
        directorArray.update(index, data);
      } else {
        directorArray.append(data);
      }
    }

    setOpenDialog(false);
  }

  return (
    <>
      {arrayFieldName && (
        <DirectorDialog
          open={openDialog}
          setOpen={setOpenDialog}
          form={directorForm}
          onSubmit={onSubmitDirector}
        />
      )}
      <DeletePropertyDialog
        open={openDeletedConfirmation}
        onOpenChange={setOpenDeleteConfirmation}
        onCloseDeleteConfirmation={onCloseDeleteConfirmation}
        onDelete={onDelete}
        isSubmitting={isSubmitting}
      />
      <p className="text-md font-bold">Direction And Management</p>
      <FormField
        control={form.control}
        name="isDirectedAndManagedInBahamas"
        render={({ field, fieldState }) => (
          <FormItem>
            <Tooltip delayDuration={0}>
              <FormLabel>
                <p className="flex gap-1">
                  Is the activity directed and managed in the Bahamas?*
                  <TooltipTrigger asChild>
                    <Info className="flex shrink-0 size-4" />
                  </TooltipTrigger>
                </p>
              </FormLabel>
              <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                <p>
                  For a legal entity to be directed and managed from the Bahamas,
                  it must conduct an adequate number of board meetings in the Bahamas
                  (i.e., the quorum of directors must be physically present in the territory
                  for the board meetings)
                </p>
              </TooltipContent>
            </Tooltip>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                value={field.value}
                invalid={!!fieldState.error}
                className="flex flex-row space-x-2"
                disabled={isSubmitting}
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="true" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Yes
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="false" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    No
                  </FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="numberOfMeetings"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Number of board meetings the entity held during the financial period with relation to this activity.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <Input
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
                placeholder="0"
                min={0}
                max={999}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="numberOfMeetingsInBahamas"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Number of board meetings the entity held during the financial period with relation to this activity in the Bahamas*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <Input
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
                placeholder="0"
                min={0}
                max={999}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="quorumDirectors"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Quorum for board meetings.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <Input
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
                placeholder="0"
                min={0}
                max={999}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="quorumPhysicallyPresent"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Of these board meetings held in the Bahamas, was the quorum of directors physically present in the Bahamas?*
              </p>
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                value={field.value}
                invalid={!!fieldState.error}
                className="flex flex-row space-x-2"
                disabled={isSubmitting}
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="true" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Yes
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="false" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    No
                  </FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="areMinutesKeptInBahamas"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Are the minutes of all board meetings kept in the Bahamas?*
              </p>
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                value={field.value}
                invalid={!!fieldState.error}
                className="flex flex-row space-x-2"
                disabled={isSubmitting}
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="true" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Yes
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="false" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    No
                  </FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="directors"
        control={form.control}
        render={({ fieldState }) => (
          <FormItem>
            {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
            <FormLabel>Directors' Details</FormLabel>
            <FormControl>
              <DirectorsTable
                disabled={isSubmitting}
                directors={directorArray.fields}
                onSelect={(income, index) => onSelect("directors", income, index)}
                onDelete={index => onOpenDeleteConfirmation("directors", index)}
              />
            </FormControl>
          </FormItem>
        )}
      />
      <div className="flex justify-end">
        <Button size="sm" onClick={() => addDirector("directors")} type="button" disabled={isSubmitting}>
          <Plus className="mr-2 size-4 text-white" />
          Add Director
        </Button>
      </div>
    </>
  )
}
