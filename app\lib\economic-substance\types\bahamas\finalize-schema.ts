import { z } from "zod";
import { phoneSchema } from "~/lib/types/phone-schema";
import { nonEmptyString, stringBoolean } from "~/lib/utilities/zod-validators";

export enum EntityRelation {
  DIRECTOR = "Director",
  SOLE_DIRECTOR = "Sole Director",
  ALTERNATE_DIRECTOR = "Alternate Director",
  SECRETARY = "Secretary",
  TAX_ADVISOR = "Tax Advisor",
  LEGAL_ADVISOR = "Legal Advisor",
  BANKER = "Banker",
  AUTHORIZED_AGENT = "Authorized Agent",
  AUTHORIZED_REPRESENTATIVE = "Authorized Representative",
  ACCOUNTANT = "Accountant",
  OTHER = "Other",
} ;

export const finalizeSchema = z.object({
  // switches
  confirmationDeclaration: stringBoolean(),
  authorityToActOnBehalf: stringBoolean(),
  legitimateInterestForProcessing: stringBoolean(),
  acknowledgeSubmissionFee: stringBoolean(),
  entityRelation: z.enum(Object.values(EntityRelation) as [string, ...string[]], {
    message: "Please select a relation to <PERSON><PERSON><PERSON>",
  }),
  otherEntityRelation: z.string({
    required_error: "Please specify the other relation to entity",
  }).optional(),
  declarantName: nonEmptyString("Declarant name"),
  telephone: phoneSchema({ required: true }),
})
  .refine(data => data.confirmationDeclaration === "true", {
    message: "Required.",
    path: ["confirmationDeclaration"],
  })
  .refine(data => data.authorityToActOnBehalf === "true", {
    message: "Required.",
    path: ["authorityToActOnBehalf"],
  })
  .refine(data => data.legitimateInterestForProcessing === "true", {
    message: "Required.",
    path: ["legitimateInterestForProcessing"],
  })
  .refine(data => data.acknowledgeSubmissionFee === "true", {
    message: "Required.",
    path: ["acknowledgeSubmissionFee"],
  })
  .refine(data => !(data.entityRelation === EntityRelation.OTHER && !data.otherEntityRelation), {
    message: "Please specify the other relation to entity.",
    path: ["otherEntityRelation"],
  });

export type FinalizeSchemaType = z.infer<typeof finalizeSchema>;
