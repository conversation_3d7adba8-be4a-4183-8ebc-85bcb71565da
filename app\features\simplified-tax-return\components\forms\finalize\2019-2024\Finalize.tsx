import { zodResolver } from "@hookform/resolvers/zod";
import { Checkbox, Combobox, DatePicker, Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import { type JSX, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { type FinalizeType, finalizeSchema } from "~/lib/simplified-tax-return/types/finalize/2019-2024/finalize-schema";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { getCountryOptions } from "~/lib/utilities/countries";
import { formatDate, formatDateForAPI } from "~/lib/utilities/format";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";

const ReturnMadeAs = {
  onMyOwnBehalf: {
    name: "On My Own Behalf",
    description: "If you are the Shareholder of the Company you are submitting the STR for",
  },
  asOfficer: {
    name: "As Officer of a Corporate Body namely",
    description: "Name of Company you are President, Corporate Secretary, Treasurer, or other Officer of",
  },
  asAttorney: {
    name: "As Attorney, Agent, Accountant, Manager for",
    description: "Name of Company you are Attorney, Agent, Accountant, Manager or Director of",
  },
  asTrustee: {
    name: "As Trustee, Executor, Administrator for",
    description: "Name of Entity you act as Trustee, Executor, or Administrator of",
  },
} as const;

export function Finalize(): JSX.Element {
  const { submissionData } = useSubmission();
  const data = useMemo(() => submissionData[Pages.FINALIZE] as FinalizeType, [submissionData]);
  const form = useForm<FinalizeType>({
    resolver: zodResolver(finalizeSchema),
    shouldFocusError: false,
    defaultValues: {
      confirmationTrueInformation: "false",
      confirmationUnderstand: "false",
      confirmationAwarePerjury: "false",
      addressOfPersonDeclaring: "",
      addressOfPersonDeclaring2: "",
      zipCode: "",
      city: "",
      country: "",
      nameOfPersonDeclaring: "",
      onMyOwnBehalf: "",
      asOfficer: "",
      asAttorney: "",
      asTrustee: "",
    },
  });
  const { reset, formState, setValue, resetField } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
    // Since date pickers work with local time, we need to convert the UTC 00:00:00Z hour to local time
    setValue("dateOfSignature", new Date(formatDate(data.dateOfSignature, { formatStr: "yyyy-MM-dd 00:00:00" })));
    setValue("confirmationTrueInformation", "false");
    setValue("confirmationUnderstand", "false");
    setValue("confirmationAwarePerjury", "false");
    if (data?.onMyOwnBehalf) {
      setValue("returnMadeAs", "onMyOwnBehalf");
    } else if (data?.asOfficer) {
      setValue("returnMadeAs", "asOfficer");
    } else if (data?.asAttorney) {
      setValue("returnMadeAs", "asAttorney");
    } else if (data?.asTrustee) {
      setValue("returnMadeAs", "asTrustee");
    } else {
      resetField("returnMadeAs");
    }
  }, [data, reset, setValue, resetField]);

  const countryOptions = useMemo(() => getCountryOptions(), []);
  const fetcher = useFetcher();

  function onSubmit(data: FinalizeType): void {
    fetcher.submit({ data: JSON.stringify({
      ...data,
      // Set signature date with API format
      dateOfSignature: formatDateForAPI(data.dateOfSignature),
    }) }, {
      method: "post",
    });
  }

  const returnMadeAs = form.watch("returnMadeAs");

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id="str-form">
        <p>Declaration and certificate</p>
        <div className="flex flex-col space-y-5 lg:w-1/2 sm:w-full py-5">
          <FormField
            control={form.control}
            name="confirmationTrueInformation"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="flex flex-row items-top space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={value => field.onChange(String(value))}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    I hereby declare that the information given on this form is to the best of my knowledge and belief,
                    true and correct and that I have the authority to disclose the information provided. *
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmationUnderstand"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="flex flex-row items-top space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={value => field.onChange(String(value))}
                      value="true"
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    I understand that the Inland Revenue Department reserves the right to review this return and
                    I can be held responsible for (i) understating, overstating or omitting data and (ii) the
                    payment of any fees, fines and penalties associated with these actions, as defined under the
                    Income Tax Act, the Tax Administration and Procedures Act and the Perjury Act of the Revised
                    Laws of St. Kitts and Nevis. *
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmationAwarePerjury"
            render={({ field, fieldState }) => (
              <FormItem>
                <div className="flex flex-row items-top space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      invalid={!!fieldState.error}
                      onCheckedChange={value => field.onChange(String(value))}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Be aware that a person who makes a false declaration commits an offence under Section 5 of the
                    Perjury Act, of the Revised Laws of St. Kitts and Nevis and that person can upon conviction, be
                    liable to imprisonment for a term of not less than seven years and not more than ten years, or
                    to a fine of not less than EC$30,000.00 and not more than EC$50,000.00, or both. *
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex-col space-y-2">
          <FormField
            control={form.control}
            name="dateOfSignature"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Date of signature *</FormLabel>
                <FormControl>
                  <DatePicker
                    date={field.value}
                    onChange={field.onChange}
                    invalid={!!fieldState.error}
                    disabledDates={{
                      after: new Date(),
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="addressOfPersonDeclaring"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Address of person making the declaration *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="addressOfPersonDeclaring2"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Address #2</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="city"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>City *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="zipCode"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Zip code *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="country"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Country*</FormLabel>
                <FormControl>
                  <Combobox
                    placeholder="Select a country"
                    searchText="Search..."
                    noResultsText="No countries found."
                    items={countryOptions}
                    onChange={field.onChange}
                    value={field.value}
                    invalid={!!fieldState.error}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="nameOfPersonDeclaring"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Name of the person stating the declaration *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex-col space-y-2">
            <FormField
              control={form.control}
              name="returnMadeAs"
              render={({ field, fieldState }) => (
                <FormItem className="mt-3">
                  <FormLabel>
                    <p>
                      Return made: *
                    </p>
                  </FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={field.disabled}
                    >
                      <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                        <SelectTrigger invalid={!!fieldState.error}>
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="group/content">
                        {Object.keys(ReturnMadeAs).map(key => (
                          <SelectItem value={key} key={key} className="group/item">
                            {ReturnMadeAs[key as keyof typeof ReturnMadeAs].name}
                            <div className="pr-10">
                              <span className="text-gray-400 group-focus/item:text-gray-100 group-focus/item:font-normal group-data-[state=open]/content:block hidden">
                                {ReturnMadeAs[key as keyof typeof ReturnMadeAs].description}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {returnMadeAs === "onMyOwnBehalf" && (
              <FormField
                control={form.control}
                name="onMyOwnBehalf"
                render={({ field, fieldState }) => (
                  <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                    <FormDescription>If you are the shareholder of the Company you are submitting the STR for</FormDescription>
                  </FormItem>
                )}
              />
            )}
            {returnMadeAs === "asOfficer" && (
              <FormField
                control={form.control}
                name="asOfficer"
                render={({ field, fieldState }) => (
                  <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                    <FormDescription>Name of company you are president, corporate secretary, treasurer or other officer of</FormDescription>
                  </FormItem>
                )}
              />
            )}
            {returnMadeAs === "asAttorney" && (
              <FormField
                control={form.control}
                name="asAttorney"
                render={({ field, fieldState }) => (
                  <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                    <FormDescription>Name of company you are Attorney, Agent, Accountant, Manager or Director of</FormDescription>
                  </FormItem>
                )}
              />
            )}
            {returnMadeAs === "asTrustee" && (
              <FormField
                control={form.control}
                name="asTrustee"
                render={({ field, fieldState }) => (
                  <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        invalid={!!fieldState.error}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                    <FormDescription>Name of entity you act as Trustee, Executor, or Administrator of</FormDescription>
                  </FormItem>
                )}
              />
            )}
          </div>
        </div>
      </RemixForm>
    </Form>
  )
}
