import { useLoaderData } from "@remix-run/react";
import { SummaryTable } from "./table/SummaryTable";
import { SummaryTableData } from "./table/SummaryTableData";
import { SummaryTableRow } from "./table/SummaryTableRow";
import type { FinancialPeriodSchemaType } from "~/lib/economic-substance/types/bahamas/financial-period-schema";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { dayMonthYearFormat, formatDate } from "~/lib/utilities/format";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.$id.summary";

export function FinancialPeriod() {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const {
    hasFinancialPeriodChanged,
    startDate,
    endDate,
    isReclassifiedToPEH,
  } = submissionData[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType
  const formattedHasFinancialPeriodChanged = hasFinancialPeriodChanged ? "Yes" : "No"
  const formattedIsReclassifiedToPEH = isReclassifiedToPEH ? "Yes" : "No"
  // Since this is Bahamas economic substance, use Bahamas timezone
  const financialPeriodStart = formatDate(startDate, { timezone: "Bahamas", formatStr: dayMonthYearFormat })
  const financialPeriodEnd = formatDate(endDate, { timezone: "Bahamas", formatStr: dayMonthYearFormat })

  return (
    <div>
      <h2 className="text-blue-700 font-bold mb-4">Financial Period</h2>
      <SummaryTable>
        <tbody>
          <SummaryTableRow>
            <SummaryTableData>
              Has an application been made and confirmed with Ministry of Finance to
              change your Substance Reporting period?
            </SummaryTableData>
            <SummaryTableData>{formattedHasFinancialPeriodChanged}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Substance reporting period start date:
            </SummaryTableData>
            <SummaryTableData>{financialPeriodStart}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Substance reporting period end date:
            </SummaryTableData>
            <SummaryTableData>{financialPeriodEnd}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Is the entity subject to reclassification on a non-included passive holding
              entity to a pure equity holding (PEH) entity under the Act (CESRA 2023)??
            </SummaryTableData>
            <SummaryTableData>{formattedIsReclassifiedToPEH}</SummaryTableData>
          </SummaryTableRow>
        </tbody>
      </SummaryTable>
    </div>
  )
}
