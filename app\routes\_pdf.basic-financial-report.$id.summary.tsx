import type { LoaderFunctionArgs } from "@remix-run/node";
import type { JSX } from "react";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import { middleware } from "~/lib/middlewares.server";
import type { SubmissionStatus } from "~/services/api-generated";
import { clientGetSubmission } from "~/services/api-generated";
import { SummaryPage } from "~/components/basic-financial-report/components/summary/SummaryPage";
import { BalanceSheet } from "~/components/basic-financial-report/components/summary/BalanceSheet";
import { ProfitLoss } from "~/components/basic-financial-report/components/summary/ProfitLoss";
import { FrontPage } from "~/components/basic-financial-report/components/summary/FrontPage";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export type BasicFinancialReportSummaryLoader = Promise<{
  submissionData: Record<string, any>
  entityDetails: {
    legalEntityName: string
    status: SubmissionStatus | undefined
    companyIdentityCode: string | undefined | null
    masterClientCode: string | undefined | null
    submittedAt: string | undefined | null
  }
}>
export async function loader({ request, params }: LoaderFunctionArgs): BasicFinancialReportSummaryLoader {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireBfrModule"], request);
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found");
  }

  const submissionData = getUnflattenedDataSet(submission);
  const entityDetails = {
    legalEntityName: submission.legalEntityName || "No company name",
    status: submission.status,
    companyIdentityCode: submission.legalEntityCode,
    masterClientCode: submission.masterClientCode,
    submittedAt: submission.submittedAt,
  }

  return {
    submissionData,
    entityDetails,
  };
}

export default function BasicFinancialReportSummary(): JSX.Element {
  return (
    <>
      <SummaryPage>
        <FrontPage />
      </SummaryPage>
      <SummaryPage>
        <BalanceSheet />
      </SummaryPage>
      <SummaryPage>
        <ProfitLoss />
      </SummaryPage>
    </>
  )
}
