import { authHeaders } from "~/lib/auth/utils/auth-headers";
import type { Company } from "~/features/companies/api/get-companies";
import type { ModuleDTO } from "~/services/api-generated";
import { clientGetCompanyModules } from "~/services/api-generated";
import { Modules } from "~/lib/utilities/modules";

export async function requireActiveModule({ request, key, companyId }: { request: Request, key: string, companyId: string }): Promise<{
  module: ModuleDTO
}> {
  const { data, error } = await clientGetCompanyModules({
    headers: await authHeaders(request),
    path: { companyId },
  });

  if (error || !data) {
    if (error) {
      // Log the API error
      console.error(`Failed to retrieve company modules for company ${companyId}`, error);
    }

    throw new Response("Module is not found or disabled", { status: 412 });
  }

  const { modules } = data;
  const module = modules?.find(module => module.key === key && module.isActive);

  if (!module) {
    throw new Error("Module is not found or disabled");
  }

  return { module };
}

export async function getESModule(company: Company, request: Request): Promise<ModuleDTO> {
  let currentModule;
  if (company.jurisdictionName === "Bahamas") {
    const { module } = await requireActiveModule({ request, key: Modules.ECONOMIC_SUBSTANCE_BAHAMAS, companyId: company.companyId });
    currentModule = module;
  } else if (company.jurisdictionName === "BVI") {
    const { module } = await requireActiveModule({ request, key: Modules.ECONOMIC_SUBSTANCE_BVI, companyId: company.companyId });
    currentModule = module;
  }

  if (!currentModule) {
    throw new Response("Module not found", { status: 404 });
  }

  return currentModule;
}
