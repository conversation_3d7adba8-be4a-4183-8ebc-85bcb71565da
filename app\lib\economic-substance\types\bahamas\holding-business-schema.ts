import type { z } from "zod";
import { incomeSchema } from "./income-schema";
import { expenditureSchema } from "./expenditure-schema";
import { employeesSchema } from "./employee-schema";
import { premisesSchema } from "./premises-schema";
import { lawsRegulationsSchema } from "./laws-regulations-schema";

export const holdingBusinessSchema = incomeSchema
  .and(expenditureSchema)
  .and(employeesSchema)
  .and(premisesSchema)
  .and(lawsRegulationsSchema)

export type HoldingBusinessSchemaType = z.infer<typeof holdingBusinessSchema>
