import { Loader2, Search } from "lucide-react"
import { Button, Form, FormControl, FormField, FormItem, FormMessage, Input } from "@netpro/design-system"
import { Form as RemixForm } from "@remix-run/react";
import { useForm } from "react-hook-form";
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod";
import type { JSX } from "react";

type SearchFormProps = {
  placeholder: string
  onSearch: (searchTerm: string) => void
  isSearching?: boolean
}

export function SearchInputForm({
  placeholder,
  onSearch,
  isSearching,
}: SearchFormProps): JSX.Element {
  const formSchema = z.object({
    searchTerm: z.string().optional(),
  });
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { searchTerm: "" },
  });

  function onSubmit(data: z.infer<typeof formSchema>): void {
    onSearch(data.searchTerm ? data.searchTerm.toLowerCase() : "")
  }

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit)} className="flex justify-between gap-3" noValidate>
        <div className="flex-1">
          <FormField
            control={form.control}
            name="searchTerm"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    placeholder={placeholder}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" size="sm" className="gap-1">
          {isSearching ? <Loader2 size={16} className="animate-spin" /> : <Search size={16} />}
          Search
        </Button>

      </RemixForm>
    </Form>
  )
}
