import { But<PERSON> } from "@netpro/design-system";
import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import { Link, useLoaderData, useNavigation } from "@remix-run/react";
import { ChevronRight } from "lucide-react";
import type { JSX } from "react";
import { useState } from "react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { SubmissionRow } from "~/components/economic-substance/SubmissionRow";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import { getESModule } from "~/features/modules/api/get-modules";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { middleware } from "~/lib/middlewares.server";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import type { DocumentDTO, SubmissionDTO } from "~/services/api-generated";
import {
  clientGetCompanyModuleSubmissions,
  clientGetSubmission,
  getApiV1CommonDocumentsByDocumentId,
} from "~/services/api-generated";

const title = "Submissions for" as const;
const breadCrumbList = [
  {
    href: "/economic-substance",
    name: "Economic Substance",
  },
];

export type SubmissionModel = SubmissionDTO & { summaryReport?: DocumentDTO, financialPeriodEndsAt: string, financialPeriodStartsAt: string }
type LoaderResponse = {
  submissions: SubmissionModel[]
}
export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<LoaderResponse | never>> {
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireEsModule"], request);
  const esModule = await getESModule(company, request);
  const { data: submissionData, error } = await clientGetCompanyModuleSubmissions({
    headers: await authHeaders(request),
    path: {
      companyId: company.companyId,
      moduleId: esModule.id as string,
    },
  })

  if (error) {
    throw new Response(error.exceptionMessage as string, { status: 500 })
  }

  if (!submissionData?.data) {
    throw new Response("Submissions not found", { status: 404 })
  }

  const submissions = submissionData?.data?.filter(
    submission => submission.status === SubmissionStatusNames.Submitted
    || submission.status === SubmissionStatusNames.Paid
    || submission.status === SubmissionStatusNames.InformationRequested,
  );
  const mappedSubmissions: SubmissionModel[] = await Promise.all(
    submissions.map(async (submission) => {
      const { data: submissionDetails } = await clientGetSubmission({
        headers: await authHeaders(request),
        path: { submissionId: submission.id! },
        query: { includeFormDocument: true },
      });

      if (!submissionDetails) {
        throw new Error("Submission not found");
      }

      const submissionData = getUnflattenedDataSet(submissionDetails);
      const documentId = submissionData[Pages.FINANCIAL_PERIOD]?.summaryReportDocumentId;
      let summaryReport: DocumentDTO | undefined;

      if (submissionDetails.documentIds?.length && documentId) {
        const { data: document } = await getApiV1CommonDocumentsByDocumentId({
          headers: await authHeaders(request),
          path: { documentId },
        });
        summaryReport = document;
      }

      return { ...submission, summaryReport } as SubmissionModel;
    }),
  );

  return json({ submissions: mappedSubmissions });
}

export default function ESSubmissions(): JSX.Element {
  const { submissions } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const [isCreatingSubmission, setIsCreatingSubmission] = useState(false);
  const isLoading = isCreatingSubmission && navigation.state === "loading";
  const handleNewSubmissionClick = () => {
    setIsCreatingSubmission(true);
  };

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 py-2.5">
        {submissions && submissions.length > 0
          ? (
              submissions.map(submission => (
                <SubmissionRow
                  submission={submission}
                  key={submission.id}
                  moduleUrl="economic-substance"
                  continuePageName={Pages.FINANCIAL_PERIOD}
                />
              ))
            )
          : (
              <CenteredMessage title="No submissions have been completed">
                <Button asChild disabled={isLoading}>
                  <Link to="/economic-substance/new" onClick={handleNewSubmissionClick}>
                    {isLoading ? "Creating submission..." : "File new submission"}
                    <ChevronRight />
                  </Link>
                </Button>
              </CenteredMessage>
            )}
      </div>
    </div>
  );
}
