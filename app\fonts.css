@font-face {
  font-family: "AkkuratLLWeb-Light";

  src: url("/fonts/AkkuratLLWeb-Light.woff") format("woff");
}

@font-face {
  font-family: "AkkuratLLWeb-Light";

  src: url("/fonts/AkkuratLLWeb-Light.woff2") format("woff2");
}

@font-face {
  font-family: "AkkuratLLWeb-Regular";

  src: url("/fonts/AkkuratLLWeb-Regular.woff") format("woff");
}

@font-face {
  font-family: "AkkuratLLWeb-Regular";

  src: url("/fonts/AkkuratLLWeb-Regular.woff2") format("woff2");
}

/* light */

@font-face {
  font-family: "AkkuratLLSub-Light";

  src: url("/fonts/AkkuratLLSub-Light.woff") format("woff");
}

@font-face {
  font-family: "AkkuratLLSub-Light";

  src: url("/fonts/AkkuratLLSub-Light.woff2") format("woff2");
}

@font-face {
  font-family: "AkkuratLLSub-Regular";

  src: url("/fonts/AkkuratLLSub-Regular.woff") format("woff");
}

@font-face {
  font-family: "AkkuratLLSub-Regular";

  src: url("/fonts/AkkuratLLSub-Regular.woff2") format("woff2");
}

@font-face {
  font-family: 'plaakLight';

  src: url('/fonts/Plaak-26-Ney-Light-205TF.woff2') format('woff2'),
       url('/fonts/Plaak-26-Ney-Light-205TF.woff') format('woff');

  font-weight: normal;

  font-style: normal;
}

@font-face {
  font-family: 'plaakRegular';

  src: url('/fonts/Plaak-36-Ney-Regular-205TF.woff2') format('woff2'),
       url('/fonts/Plaak-36-Ney-Regular-205TF.woff') format('woff');

  font-weight: normal;

  font-style: normal;
}

@font-face {
  font-family: 'plaakBold';

  src: url('/fonts/Plaak-46-Ney-Bold-205TF.woff2') format('woff2'),
       url('/fonts/Plaak-46-Ney-Bold-205TF.woff') format('woff');

  font-weight: normal;

  font-style: normal;
}

@font-face {
  font-family: 'plaakHeavy';

  src: url('/fonts/Plaak-56-Ney-Heavy-205TF.woff2') format('woff2'),
       url('/fonts/Plaak-56-Ney-Heavy-205TF.woff') format('woff');

  font-weight: normal;

  font-style: normal;
}