import type {
  AuthenticationResult,
  AuthorizationUrlRequest,
  Configuration,
  IdTokenClaims,
} from "@azure/msal-node";
import {
  LogLevel,
} from "@azure/msal-node";
import {
  createConfiguration as createApplicationConfiguration,
  createClientApplication,
  getAccessTokenByClientCredentials,
  getAccessToken as getMicrosoftEntraAccessToken,
  redirectToLogin as redirectToMicrosoftEntraLogin,
} from "@netpro/auth";
import type {
  LoaderFunctionArgs,
  SessionData,
  SessionStorage,
  TypedResponse,
} from "@remix-run/node";
import {
  redirect,
} from "@remix-run/node";

import { sessionStorage } from "~/lib/auth/utils/session.server";
import { client } from "~/services/api-generated";

// Verify that the required environment variables are defined.
if (!process.env.ENTRA_EXTERNAL_ID_CLIENT_ID) {
  throw new Error("Missing ENTRA_EXTERNAL_ID_CLIENT_ID");
}

if (!process.env.ENTRA_EXTERNAL_ID_TENANT_SUBDOMAIN) {
  throw new Error("Missing ENTRA_EXTERNAL_ID_TENANT_SUBDOMAIN");
}

if (!process.env.ENTRA_EXTERNAL_ID_TENANT_ID) {
  throw new Error("Missing ENTRA_EXTERNAL_ID_TENANT_ID");
}

if (!process.env.ENTRA_EXTERNAL_ID_CLIENT_SECRET) {
  throw new Error("Missing ENTRA_EXTERNAL_ID_CLIENT_SECRET");
}

if (!process.env.ENTRA_EXTERNAL_ID_REDIRECT_URI) {
  throw new Error("Missing ENTRA_EXTERNAL_ID_REDIRECT_URI");
}

if (!process.env.APPLICATION_CLIENT_ID) {
  throw new Error("Missing APPLICATION_CLIENT_ID");
}

if (!process.env.APPLICATION_TENANT_ID) {
  throw new Error("Missing APPLICATION_TENANT_ID");
}

if (!process.env.APPLICATION_CLIENT_SECRET) {
  throw new Error("Missing APPLICATION_CLIENT_SECRET");
}

if (!process.env.ENTRA_API_SCOPE) {
  throw new Error("Missing ENTRA_API_SCOPE");
}

if (!process.env.API_BASE_URL) {
  throw new Error("Missing API_BASE_URL");
}

client.setConfig({ baseUrl: process.env.API_BASE_URL });

export type ConfigurationArgs = {
  clientId: string
  clientSecret: string
  tenantSubdomain: string
  nodeEnv: string
  options?: Partial<Configuration>
};

export type SessionStorageType = SessionStorage<SessionData, SessionData>;

// This function is very similar to the one in @netpro/auth, but it uses a different authority URL.
export function createConfiguration({
  clientId,
  clientSecret,
  tenantSubdomain,
  nodeEnv,
  options,
}: ConfigurationArgs): Configuration {
  if (!clientId || !clientSecret || !tenantSubdomain || !nodeEnv) {
    throw new Error("Missing required arguments");
  }

  return {
    auth: {
      clientId,
      clientSecret,
      authority: `https://${tenantSubdomain}.ciamlogin.com/`,
    },
    system: {
      loggerOptions: {
        loggerCallback(logLevel, message, containsPii): void {
          // We want to log to the console server-side
          // eslint-disable-next-line no-console
          console.log(message, { logLevel, containsPii });
        },
        piiLoggingEnabled: false,
        logLevel:
          nodeEnv === "production" ? LogLevel.Warning : LogLevel.Verbose,
      },
      ...options,
    },
  };
}

const externalIdConfig = createConfiguration({
  clientId: process.env.ENTRA_EXTERNAL_ID_CLIENT_ID,
  clientSecret: process.env.ENTRA_EXTERNAL_ID_CLIENT_SECRET,
  tenantSubdomain: process.env.ENTRA_EXTERNAL_ID_TENANT_SUBDOMAIN,
  nodeEnv: process.env.NODE_ENV,
});
const applicationConfig = createApplicationConfiguration({
  clientId: process.env.APPLICATION_CLIENT_ID,
  clientSecret: process.env.APPLICATION_CLIENT_SECRET,
  tenantId: process.env.APPLICATION_TENANT_ID,
  nodeEnv: process.env.NODE_ENV,
});
const externalIdClientApplication = createClientApplication(externalIdConfig);
const clientApplication = createClientApplication(applicationConfig);
const authCodeUrlParameters: AuthorizationUrlRequest = {
  scopes: ["user.read"],
  redirectUri: process.env.ENTRA_EXTERNAL_ID_REDIRECT_URI,
  prompt: "login",
};

export async function redirectToLogin(request: LoaderFunctionArgs["request"]): Promise<TypedResponse<never>> {
  return redirectToMicrosoftEntraLogin({
    request,
    authCodeUrlParameters,
    clientApplication: externalIdClientApplication,
    sessionStorage,
  });
}

export async function getClientCredentialsToken(): Promise<AuthenticationResult> {
  return getAccessTokenByClientCredentials({
    scopes: [process.env.ENTRA_API_SCOPE as string],
    clientApplication,
  });
}

export async function getAccessToken(
  request: LoaderFunctionArgs["request"],
  redirectTarget: string,
): Promise<TypedResponse<never>> {
  const response = await getMicrosoftEntraAccessToken({
    request,
    clientApplication: externalIdClientApplication,
    sessionStorage,
    entraRedirectUri: process.env.ENTRA_EXTERNAL_ID_REDIRECT_URI as string,
  });
  const idTokenClaims = response.idTokenClaims as IdTokenClaims;
  const session = await sessionStorage.getSession(
    request.headers.get("Cookie"),
  );

  session.set("objectId", idTokenClaims.oid);
  session.set("userEmail", response.account?.username);
  session.unset("mfaMethod");
  session.unset("mfaCompleted");
  session.unset("mfaAttempts");
  session.unset("mfaEmailCodeExpiresAt");
  session.unset("currentMasterClient");
  session.unset("currentCompany");
  session.unset("notifications");
  session.unset("profilePictureUrl");
  session.unset("totalMasterClients");
  session.unset("totalMasterClientCompanies");
  session.unset("termsAndConditionsAccepted");

  return redirect(redirectTarget ?? "/", {
    headers: {
      // Make sure to save session changes before redirecting
      "Set-Cookie": await sessionStorage.commitSession(session),
    },
  });
}
