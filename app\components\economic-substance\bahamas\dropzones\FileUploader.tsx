import type { FileUploaderProps } from "@netpro/design-system";
import type { ReactNode } from "react";
import type { FileRejection } from "react-dropzone";
import { Dropzone, FileUploader as NetProFileUploader, notify } from "@netpro/design-system";
import { CloudUpload } from "lucide-react";

function handleRejections(rejections: FileRejection[]) {
  rejections.forEach((rejection) => {
    notify({
      title: "File Rejected",
      message: `${
        rejection.file.name
      } was rejected. Reason: ${rejection.errors[0].code}`,
      variant: "error",
      duration: 5000,
    });
  });
}

type Props = {
  children: ReactNode
} & FileUploaderProps
export function FileUploader({ children, ...props }: Props) {
  return (
    <NetProFileUploader
      {...props}
      onReject={handleRejections}
    >
      <Dropzone className="flex h-36 flex-col gap-2 border-gray-300 p-3">
        <CloudUpload className="size-10 text-primary" />
        {children}
      </Dropzone>
    </NetProFileUploader>
  )
}
