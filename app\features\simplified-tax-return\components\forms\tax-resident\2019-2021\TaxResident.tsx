import { zodResolver } from "@hookform/resolvers/zod";
import { Combobox, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, RadioGroup, RadioGroupItem } from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import { type JSX, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { ContactAgentDetails } from "../../ContactAgentDetails";
import { getCountryOptions } from "~/lib/utilities/countries";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import type { TaxResidentType } from "~/lib/simplified-tax-return/types/tax-resident/2019-2021/tax-resident-schema";
import { taxResidentSchema } from "~/lib/simplified-tax-return/types/tax-resident/2019-2021/tax-resident-schema";

export function TaxResident(): JSX.Element {
  const { submissionData, setCanContinue } = useSubmission();
  const data = useMemo(() => submissionData[Pages.TAX_RESIDENT] as TaxResidentType, [submissionData]);
  const form = useForm<TaxResidentType>({
    resolver: zodResolver(taxResidentSchema),
    shouldFocusError: false,
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  /**
   *  For residentCountry, instead of using empty string directly, we need to use "no-country"
   *  This is because the Combobox component does not support search for items with empty string
   */

  useEffect(() => {
    // Country is converted into "no-country" when no country is selected to display as it should ("No country")
    if (data?.residentCountry === "") {
      data.residentCountry = "no-country";
    }

    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  const fetcher = useFetcher();

  function onSubmit(data: TaxResidentType): void {
    // Country is converted back into empty string when no country is selected to save as it should ("")
    if (data?.residentCountry === "no-country") {
      data.residentCountry = "";
    }

    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const countryOptions = useMemo(() => getCountryOptions(), []);
  const incorporatedBefore2019 = form.watch("incorporatedBefore2019");
  const nonTaxResident = form.watch("nonTaxResident");

  useEffect(() => {
    if (incorporatedBefore2019 === "false" && nonTaxResident === "true") {
      setCanContinue(false);
    } else {
      setCanContinue(true);
    }
  }, [incorporatedBefore2019, nonTaxResident, setCanContinue]);

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id="str-form">
        <div className="flex-col space-y-5">
          <FormField
            control={form.control}
            name="incorporatedBefore2019"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>
                  Were you incorporated before January 1, 2019? *
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    invalid={!!fieldState.error}
                    value={field.value}
                    orientation="horizontal"
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="true" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Yes
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="false" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        No
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {incorporatedBefore2019 === "false" && (
            <FormField
              control={form.control}
              name="nonTaxResident"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <FormLabel>
                    Are you either a tax resident or a non-resident with a permanent establishment in Saint Kitts and Nevis? *
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      invalid={!!fieldState.error}
                      value={field.value}
                      orientation="horizontal"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {incorporatedBefore2019 === "false" && nonTaxResident === "false" && (
            <FormField
              control={form.control}
              name="residentCountry"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <FormLabel>Where are you resident for tax purposes? *</FormLabel>
                  <FormControl>
                    <Combobox
                      placeholder="Select a country"
                      searchText="Search..."
                      noResultsText="No countries found."
                      items={[
                        { label: "No country", value: "no-country" },
                        ...countryOptions,
                      ]}
                      onChange={field.onChange}
                      defaultValue={field.value}
                      invalid={!!fieldState.error}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {incorporatedBefore2019 === "false" && nonTaxResident === "true" && (
            <ContactAgentDetails />
          )}
        </div>
      </RemixForm>
    </Form>
  )
}
