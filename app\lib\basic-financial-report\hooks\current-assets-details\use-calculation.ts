import type { EquityDetailsSchemaType } from "../../types/equity-details-schema";
import type { ExpenseDetailsSchemaType } from "../../types/expense-details-schema";
import type { IncomeDetailsSchemaType } from "../../types/income-details-schema";
import type { LiabilitiesDetailsSchemaType } from "../../types/liabilities-details-schema";
import type { NonCurrentAssetsDetailsSchemaType } from "../../types/non-current-assets-details-schema";
import { Pages } from "../../utilities/form-pages";

export function useCalculation(submissionData: Record<string, any>) {
  // NOTE: ALl these values appears as "number" in their schemas, but really they are being retrieved as a strings, that's why I must use the Number conversion
  const { cashAmount } = submissionData[Pages.EQUITY_DETAILS] as EquityDetailsSchemaType
  const { totalInterestIncome, totalDividendIncome, otherIncome, otherIncomes, totalProceeds } = submissionData[Pages.INCOME_DETAILS] as IncomeDetailsSchemaType
  const { portfolioManagementFeesPeriod, companyAdministrationFeesPeriod, loanInterestPayments, bankCharges, taxWithheld, dividendsPaidShareholders, totalPaymentsPurchaseSecuritiesInvestments } = submissionData[Pages.EXPENSE_DETAILS] as ExpenseDetailsSchemaType
  const { loans } = submissionData[Pages.LIABILITIES_DETAILS] as LiabilitiesDetailsSchemaType
  const { amountPaid } = submissionData[Pages.NON_CURRENT_ASSETS_DETAILS] as NonCurrentAssetsDetailsSchemaType
  // Cash Set-up for Capital calculation
  const cashSetupCapital = cashAmount ? Number(cashAmount) : 0
  // Cash Received from Income calculation
  const otherIncomesTotal = Number((otherIncomes.reduce((acc, cur) => acc + Number(cur.amount), 0)))
  const cashReceivedIncome = Number(totalInterestIncome) + Number(totalDividendIncome) + (otherIncome ? Number(otherIncome) : 0) + otherIncomesTotal
  // Cash Paid for Expenses calculation
  const cashPaidExpenses = Number(portfolioManagementFeesPeriod) - Number(companyAdministrationFeesPeriod)
    - Number(loanInterestPayments) - Number(bankCharges) - Number(taxWithheld) - Number(dividendsPaidShareholders)
  // Cash Received/(Paid) from Loans calculation
  const totalCurrentLoans = loans ? Number((loans.reduce((acc, cur) => acc + Number(cur.current), 0))) : 0
  const totalNonCurrentLoans = loans ? Number((loans.reduce((acc, cur) => acc + Number(cur.nonCurrent), 0))) : 0
  const cashReceivedPaidLoans = totalCurrentLoans + totalNonCurrentLoans
  // Cash Received for Investments Solds calculation
  const cashReceivedInvestmentsSold = totalProceeds ? Number(totalProceeds) : 0
  // Cash Paid for Investments Purchased calculation
  const cashPaidInvestmentsPurchased = Number(totalPaymentsPurchaseSecuritiesInvestments)
  const cashReceivedSalePaidNonCurrentAssets = amountPaid ? Number(amountPaid) : 0

  return { cashSetupCapital, cashReceivedIncome, cashPaidExpenses, cashReceivedPaidLoans, cashReceivedInvestmentsSold, cashPaidInvestmentsPurchased, cashReceivedSalePaidNonCurrentAssets }
}
