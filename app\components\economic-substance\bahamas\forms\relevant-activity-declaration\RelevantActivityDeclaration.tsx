import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import {
  Date<PERSON><PERSON>,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RadioGroup,
  RadioGroupItem,
  ScrollArea,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { Info } from "lucide-react";
import type { ReactNode } from "react";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import {
  type RelevantActivityDeclarationSchemaType,
  createRelevantActivityDeclarationSchema,
  defaultValues,
} from "~/lib/economic-substance/types/bahamas/relevant-activity-declaration-schema";
import { ECONOMIC_SUBSTANCE_FORM_ID } from "~/lib/economic-substance/utilities/constants";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { formatDate, formatToISODateString, parseDateForDisplay } from "~/lib/utilities/format";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";

function transformRelevantActivities(data: { relevantActivities: Array<Record<string, any>> }): {
  relevantActivities: Array<Record<string, any>>
} | undefined {
  if (!data) {
    return undefined
  }

  return {
    relevantActivities: data.relevantActivities.map(activity =>
      Object.fromEntries(
        Object.entries(activity).map(([key, value]) => [
          key,
          value === "" ? undefined : value,
        ]),
      ),
    ),
  };
}

export function RelevantActivityDeclaration(): ReactNode {
  const { submissionData } = useSubmission();
  const navigation = useNavigation()
  const fetcher = useFetcher()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading" || fetcher.state === "submitting"
  const data = useMemo(() => transformRelevantActivities(submissionData[Pages.RELEVANT_ACTIVITY_DECLARATION]) as RelevantActivityDeclarationSchemaType, [submissionData]);
  // Parse the financial period dates - use form period dates directly
  const startDate = submissionData[Pages.FINANCIAL_PERIOD].startDate;
  const endDate = submissionData[Pages.FINANCIAL_PERIOD].endDate;
  // Create schema with dates for validation
  const schemaWithFinancialPeriod = useMemo(() =>
    createRelevantActivityDeclarationSchema(startDate, endDate), [startDate, endDate]);
  const form = useForm<RelevantActivityDeclarationSchemaType>({
    resolver: zodResolver(schemaWithFinancialPeriod),
    shouldFocusError: false,
    defaultValues: data || defaultValues,
  });
  const { formState, setValue, watch } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  function onSubmit(data: RelevantActivityDeclarationSchemaType): void {
    if (!data.relevantActivities) {
      return
    }

    const submissionData = {
      ...data,
      relevantActivities: data.relevantActivities.map(activity => ({
        ...activity,
        startDate: activity.startDate ? formatToISODateString(new Date(activity.startDate)) : undefined,
        endDate: activity.endDate ? formatToISODateString(new Date(activity.endDate)) : undefined,
      })),
    }
    fetcher.submit({ data: JSON.stringify(submissionData) }, {
      method: "post",
    });
  }

  const resetFields = (activityIndex: number) => {
    setValue(`relevantActivities.${activityIndex}.carriedOnForOnlyPartOfFinancialPeriod`, null);
    setValue(`relevantActivities.${activityIndex}.startDate`, null);
    setValue(`relevantActivities.${activityIndex}.endDate`, null);
  };
  const handleSelectRelevantActivityChange = (id: string, index: number, value: boolean) => {
    // Update selected status
    setValue(`relevantActivities.${index}.selected`, value ? "true" : "false");

    // Reset related fields if the activity is deselected
    if (!value) {
      resetFields(index);
    }

    // Handle "none" option
    if (id === "none") {
      defaultValues.relevantActivities?.forEach((_, i) => {
        if (i > 0) {
          const activityIndex = i;
          setValue(`relevantActivities.${activityIndex}.selected`, "false");
          resetFields(activityIndex);
        }
      });
    }
  };
  const handleCarriedFinancialPeriodChange = (index: number, value: string) => {
    setValue(`relevantActivities.${index}.carriedOnForOnlyPartOfFinancialPeriod`, value as "true" | "false");
    if (value === "false" && startDate && endDate) {
      // Use the parsed Date objects directly
      setValue(`relevantActivities.${index}.startDate`, startDate);
      setValue(`relevantActivities.${index}.endDate`, endDate);
    }
  }
  const isNoneSelected = (id: string) => (watch("relevantActivities.0.selected") === "true") && id !== "none"
  const watchSelectedRelevantActivity = (index: number) => watch(`relevantActivities.${index}.selected`) === "false"
  const watchCarriedFinancialPeriod = (index: number) => watch(`relevantActivities.${index}.carriedOnForOnlyPartOfFinancialPeriod`) === "false"

  return (
    <div className="relative">
      <Form {...form}>
        <RemixForm
          onSubmit={form.handleSubmit(onSubmit, onError)}
          noValidate
          id={ECONOMIC_SUBSTANCE_FORM_ID}
          className="space-y-5"
        >
          <p className="text-xs my-auto">
            {`Select all relevant activities in which the entity has been engaged during the financial period from: ${startDate ? formatDate(startDate) : "N/A"} to ${endDate ? formatDate(endDate) : "N/A"}.`}
          </p>
          <div className="flex-col space-y-7">
            <ScrollArea>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Relevant Activity</TableHead>
                    <TableHead>Carried on for only part of Financial Period?</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {defaultValues.relevantActivities?.map(({ id, label }, index) => (
                    <TableRow key={id} className="border-0">
                      <TableCell>
                        <FormField
                          control={form.control}
                          name={`relevantActivities.${index}.selected`}
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex flex-col gap-2 pr-1">
                                <FormLabel
                                  className="flex justify-start gap-2 w-full items-center cursor-pointer"
                                >
                                  <FormControl>
                                    <Switch
                                      checked={field.value === "true"}
                                      onCheckedChange={val => handleSelectRelevantActivityChange(id, index, val)}
                                      disabled={isSubmitting || isNoneSelected(id)}
                                      withIcon
                                    />
                                  </FormControl>
                                  {label}
                                  {id === "none" && (
                                    <Tooltip delayDuration={0}>
                                      <TooltipTrigger asChild>
                                        <Info className="flex shrink-0 size-4" />
                                      </TooltipTrigger>
                                      <TooltipContent
                                        className="w-96 p-5 space-y-3 font-inter"
                                        side="right"
                                      >
                                        <p>
                                          To be chosen if the company does not
                                          carry on a relevant activity as defined
                                          by the ES Act.
                                        </p>
                                      </TooltipContent>
                                    </Tooltip>
                                  )}
                                </FormLabel>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TableCell>
                      <TableCell>
                        <FormField
                          control={form.control}
                          name={`relevantActivities.${index}.carriedOnForOnlyPartOfFinancialPeriod`}
                          render={({ field, fieldState }) => (
                            <FormItem className="space-y-3 py-2">
                              <FormControl>
                                <RadioGroup
                                  onValueChange={value => handleCarriedFinancialPeriodChange(index, value)}
                                  value={field.value as string | undefined}
                                  invalid={!!fieldState.error}
                                  className="flex flex-row space-x-2"
                                  disabled={isSubmitting || isNoneSelected(id) || watchSelectedRelevantActivity(index)}
                                >
                                  <FormItem
                                    className="flex items-center space-x-2 space-y-0"
                                  >
                                    <FormControl>
                                      <RadioGroupItem
                                        value="true"
                                        checked={field.value === "true"}
                                      />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      Yes
                                    </FormLabel>
                                  </FormItem>
                                  <FormItem
                                    className="flex items-center space-x-2 space-y-0"
                                  >
                                    <FormControl>
                                      <RadioGroupItem
                                        value="false"
                                        checked={field.value === "false"}
                                      />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      No
                                    </FormLabel>
                                  </FormItem>
                                </RadioGroup>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TableCell>
                      <TableCell>
                        <FormField
                          control={form.control}
                          name={`relevantActivities.${index}.startDate`}
                          render={({ field, fieldState }) => (
                            <FormItem>
                              <FormControl className="md:w-1/2 sm:w-full">
                                <DatePicker
                                  date={parseDateForDisplay(field.value)}
                                  onChange={field.onChange}
                                  invalid={!!fieldState.error}
                                  disabled={isSubmitting || isNoneSelected(id) || watchSelectedRelevantActivity(index) || watchCarriedFinancialPeriod(index)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TableCell>
                      <TableCell>
                        <FormField
                          control={form.control}
                          name={`relevantActivities.${index}.endDate`}
                          render={({ field, fieldState }) => (
                            <FormItem>
                              <FormControl className="md:w-1/2 sm:w-full">
                                <DatePicker
                                  date={parseDateForDisplay(field.value)}
                                  onChange={field.onChange}
                                  invalid={!!fieldState.error}
                                  disabled={isSubmitting || isNoneSelected(id) || watchSelectedRelevantActivity(index) || watchCarriedFinancialPeriod(index)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
        </RemixForm>
      </Form>
      <LoadingState
        isLoading={fetcher.state === "submitting" || fetcher.state === "loading"}
        message="Saving..."
      />
    </div>
  )
}
