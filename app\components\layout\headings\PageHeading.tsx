import type { UIMatch } from "@remix-run/react";
import { useMatches } from "@remix-run/react";
import type { JSX, ReactElement } from "react";
import { HeadingTitle } from "~/components/ui/typography/HeadingTitle";

type PageHeadingData = UIMatch<unknown, { breadcrumb: () => ReactElement, title: string }>

type PageHeadingProps = {
  headerTitle?: string
};

/**
 * @deprecated
 * Use _main._card.tsx layout instead
 */
export function PageHeading({
  headerTitle = "",
}: PageHeadingProps): JSX.Element | null {
  const matches = useMatches() as PageHeadingData[];
  const match = matches.filter((item: PageHeadingData) => {
    return item.handle && (item.handle.breadcrumb || item.handle.title)
  });

  if (match.length <= 0) {
    return null;
  }

  return (
    <div className="flex flex-col w-full">
      {match.map(item => (
        <div key={item.id}>
          {item.handle && item.handle.breadcrumb && (
            item.handle.breadcrumb()
          )}
          {item.handle && item.handle.title && (
            <HeadingTitle suffix={headerTitle}>{item.handle.title}</HeadingTitle>
          )}
        </div>
      ))}
    </div>
  );
}
