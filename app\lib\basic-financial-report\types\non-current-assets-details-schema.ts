import { z } from "zod";
import { nonEmptyString, preprocessArray, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";

export const assetSchema = z.object({
  description: nonEmptyString("Description"),
  purchaseYear: nonEmptyString("Purchase Year"),
  purchaseCost: stringNumber({ invalidTypeMessage: "Purchase Cost is required.", greaterThan: 0 }),
  assessedValue: stringNumber({ invalidTypeMessage: "Assessed Value is required.", greaterThan: 0 }),
})

export const nonCurrentAssetsDetailsSchema = z.object({
  companyAssets: stringBoolean(),
  fixedAssets: preprocessArray(z.array(assetSchema)).optional(),
  assetsPaidInCash: stringBoolean().optional(),
  amountPaid: stringNumber({ invalidTypeMessage: "Amount paid is required", greaterThan: 0 }).optional(),
})
  .refine(data =>
    !(data.companyAssets === "true" && !data.fixedAssets?.length), {
    message: "You must add at least one asset.",
    path: ["fixedAssets", 0],
  })
  .refine(data =>
    !(data.companyAssets === "true" && data.assetsPaidInCash === undefined), {
    message: "You must select one value",
    path: ["assetsPaidInCash"],
  })
  .refine(data =>
    !(data.assetsPaidInCash === "true" && !data.amountPaid), {
    message: "You must add an amount paid.",
    path: ["amountPaid"],
  });

export type NonCurrentAssetsDetailsSchemaType = z.infer<typeof nonCurrentAssetsDetailsSchema>;
export type AssetSchemaType = z.infer<typeof assetSchema>;
