import {
  Button,
  Combobox,
  Dialog,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  ScrollArea,
  ScrollBar,
} from "@netpro/design-system";
import type { ReactNode } from "react";
import type { UseFormReturn } from "react-hook-form";
import { Form as RemixForm } from "@remix-run/react"
import { FORM_ID } from "~/lib/economic-substance/types/bahamas/employee-schema";
import { ActivityEnum, type CigaActivitySchemaType } from "~/lib/economic-substance/types/bahamas/ciga-schema";
import { activityOptions } from "~/lib/economic-substance/utilities/ciga";

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (data: CigaActivitySchemaType) => void
  form: UseFormReturn<CigaActivitySchemaType>
}

export function CigaActivityDialog({
  open,
  setO<PERSON>,
  onSubmit,
  form,
}: Props): ReactNode {
  function handleFormSubmit(e: React.FormEvent) {
    // avoid to trigger parent form
    e.preventDefault();
    e.stopPropagation()
    form.handleSubmit(onSubmit)();
  }

  const { watch } = form
  const { description } = watch()
  const filteredActivityOptions = activityOptions.filter(item => item.value !== ActivityEnum.NO_CIGA)

  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-lg"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <RemixForm onSubmit={handleFormSubmit} className="p-2" noValidate id={FORM_ID}>
              <DialogHeader>
                <DialogTitle>Add CIGA Activity</DialogTitle>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field, fieldState }) => (
                    <FormItem className="w-full">
                      <FormLabel>CIGA Description*</FormLabel>
                      <FormControl>
                        <Combobox
                          placeholder="Select a ciga activity"
                          searchText="Search..."
                          noResultsText="No activities found."
                          items={filteredActivityOptions}
                          onChange={field.onChange}
                          value={field.value}
                          invalid={!!fieldState.error}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {description === ActivityEnum.OTHER_PLEASE_SPECIFY && (
                  <FormField
                    control={form.control}
                    name="otherActivity"
                    render={({ field, fieldState }) => (
                      <FormItem className="w-full">
                        <FormLabel>Please specify*</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            invalid={!!fieldState.error}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                  </div>
                </div>
              </DialogFooter>
            </RemixForm>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
