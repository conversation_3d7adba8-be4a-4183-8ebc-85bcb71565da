import { FormControl, FormField, FormItem, FormLabel, FormMessage, RadioGroup, RadioGroupItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { useNavigation } from "@remix-run/react";
import { Info } from "lucide-react";
import { useFormContext } from "react-hook-form";
import type { LawsRegulationsSchemaType } from "~/lib/economic-substance/types/bahamas/laws-regulations-schema";

export function LawsRegulationsSection() {
  const form = useFormContext<LawsRegulationsSchemaType>()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"

  return (
    <>
      <p className="text-md font-bold">Laws & Regulations</p>
      <FormField
        control={form.control}
        name="isCompliantWithBahamasLawsAndRegulations"
        render={({ field, fieldState }) => (
          <FormItem>
            <Tooltip delayDuration={0}>
              <FormLabel>
                <p className="flex gap-1">
                  Does the entity comply with all applicable
                  laws and regulations of The Bahamas?*
                  <TooltipTrigger asChild>
                    <Info className="flex shrink-0 size-4" />
                  </TooltipTrigger>
                </p>
              </FormLabel>
              <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                <p>
                  Where the entity is a company, the statutory obligations under the Bahamas Business
                  Companies Act, 2004. Where the entity is a limited partnership,
                  the statutory obligations under Limited Partnership Act, 2017
                </p>
              </TooltipContent>
            </Tooltip>
            <FormControl className="md:w-1/3 sm:w-full">
              <RadioGroup
                onValueChange={field.onChange}
                value={field.value}
                invalid={!!fieldState.error}
                className="flex flex-row space-x-2"
                disabled={isSubmitting}
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="true" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Yes
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="false" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    No
                  </FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  )
}
