import { useContext, useState } from "react";
import { useLocation } from "@remix-run/react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  cn,
} from "@netpro/design-system";
import { ChevronRightIcon } from "lucide-react";
import type { MenuItem, MenuItemProps } from "./MenuItem";
import ParentMenuItem from "./ParentMenuItem";
import { SessionContext } from "~/components/session-context";

type ItemType = {
  href: string
  label: string
};

const ParentMenu: React.FC<MenuItemProps> = ({ item, active, ...props }) => {
  const [open, setOpen] = useState(active);
  const location = useLocation();
  const { company } = useContext(SessionContext)
  /*
   * Determines if a ParentMenuItem is active based on the current URL path.
   *  - If the item's href is "/", returns true if the current path is exactly "/".
   *  - Otherwise, returns true if the current path starts with the item's href.
   */
  const isActive = (itemElement: ItemType): boolean => {
    if (itemElement.href !== "/") {
      return location.pathname.startsWith(itemElement.href);
    }

    return location.pathname === itemElement.href;
  };

  if (!company) {
    return null
  }

  if (item.entityTypeGuard && !item.entityTypeGuard.includes(company.entityType)) {
    return null
  }

  return (
    <ul>
      <Collapsible defaultOpen={open} onOpenChange={setOpen} {...props}>
        <CollapsibleTrigger
          className={cn(
            active ? "bg-gray-100" : "hover:bg-gray-100",
            "flex w-full items-center gap-2.5 rounded-md py-2.5 px-2 text-sm font-semibold font-inter leading-5 text-gray-800",
          )}
        >
          <div className="group w-full flex justify-between">
            <div className="flex items-center gap-2.5 flex-nowrap overflow-hidden">
              <item.icon
                className={cn(active ? "text-primary" : "text-gray-400 group-hover:text-primary", "h-6 w-6 shrink-0")}
                aria-hidden="true"
              />
              <span className="block overflow-hidden text-ellipsis whitespace-nowrap">
                {item.label}
              </span>
            </div>
            <ChevronRightIcon
              className={cn(
                open
                  ? "rotate-90 text-gray-500 transition-transform duration-300 ease-in-out"
                  : "rotate- 0 text-gray-400 transition-transform duration-300 ease-in-out",
                "size-6 shrink-0",
              )}
              aria-hidden="true"
            />
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent className="mt-1">
          {item.children && item.children.map(
            (subItem: Omit<MenuItem, "icon">) => subItem.show
            && <ParentMenuItem key={subItem.href} item={subItem} active={isActive(subItem)} />,
          )}
        </CollapsibleContent>
      </Collapsible>
    </ul>
  );
}

export default ParentMenu
