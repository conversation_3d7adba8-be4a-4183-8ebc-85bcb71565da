import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  ScrollArea,
  ScrollBar,
} from "@netpro/design-system";
import type { ReactNode } from "react";
import type { UseFormReturn } from "react-hook-form";
import { Form as RemixForm } from "@remix-run/react"
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { Currency } from "~/lib/basic-financial-report/utilities/currencies";
import type { IncomeSchemaType } from "~/lib/basic-financial-report/types/income-schema";

  type Props = {
    open: boolean
    setOpen: (open: boolean) => void
    onSubmit: (data: IncomeSchemaType) => void
    form: UseFormReturn<IncomeSchemaType>
  };

const FORM_ID = "income-dialog-form"

export function IncomeDialog({
  open,
  setOpen,
  onSubmit,
  form,
}: Props): ReactNode {
  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <RemixForm onSubmit={form.handleSubmit(onSubmit)} className="p-2" noValidate id={FORM_ID}>
              <DialogHeader>
                <DialogTitle>Other Income</DialogTitle>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Description*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Property Value*</FormLabel>
                      <FormControl>
                        <CurrencyInput
                          currencyName={Currency.USD}
                          invalid={!!fieldState.error}
                          {...field}
                          type="number"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                  </div>
                </div>
              </DialogFooter>
            </RemixForm>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
