import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@netpro/design-system"
import { useFetcher, useLocation } from "@remix-run/react"
import { CheckIcon } from "lucide-react"
import type { JSX } from "react"
import { useEffect, useRef, useState } from "react"
import type { Column } from "../hooks/useBoDirColumns";
import { useBoDirColumns } from "../hooks/useBoDirColumns"
import UpdateModal from "./UpdateModal"
import { BoDirCell } from "./BoDirCell"
import type { BeneficialOwnerComparisonDTO, BeneficialOwnerDTO, DirectorComparisonDTO, DirectorDTO } from "~/services/api-generated"
import type { BeneficialOwnerType, DirectorType } from "~/lib/bo-directors/utilities/bo-directors-columns"

type ComparisonModalProps = {
  open: boolean
  handleOpenChange: (isRejected: boolean) => void
  item: DirectorDTO | BeneficialOwnerDTO
  type: BeneficialOwnerType | DirectorType
}

type Comparison = DirectorComparisonDTO | BeneficialOwnerComparisonDTO

export default function ComparisonModal({
  open,
  handleOpenChange,
  item,
  type,
}: ComparisonModalProps): JSX.Element {
  const location = useLocation()
  const fetcher = useFetcher<{ comparisonData: Comparison }>()
  const [isLoading, setIsLoading] = useState(false)
  const isBeneficialOwner = location.pathname.includes("beneficial-owner")
  const memoizedColumns = useBoDirColumns({ item, type, isBeneficialOwner });
  const isSubmitting = fetcher.state === "submitting"
  const hasFetchedRef = useRef(false);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const showPreviousState = !["Initial", "Refreshed"].includes(item?.metaData?.status || "")

  useEffect(() => {
    if (open && item && item.id && !hasFetchedRef.current) {
      const path = `/api/bo-directors/${isBeneficialOwner ? "beneficial-owner" : "director"}/${item.id}/comparison`
      fetcher.load(path)
      hasFetchedRef.current = true;
    }

    if (!open) {
      hasFetchedRef.current = false;
    }
  }, [open, item, isBeneficialOwner, fetcher])

  useEffect(() => {
    setIsLoading(fetcher.state === "loading");
  }, [fetcher.state]);

  const hasChanged = (field: Column["field"]): boolean => {
    const currentVersion = fetcher.data?.comparisonData.currentVersion as (BeneficialOwnerDTO | DirectorDTO) || {};
    const priorVersion = fetcher.data?.comparisonData.priorVersion as (BeneficialOwnerDTO | DirectorDTO) || {};
    const currentValue = field in currentVersion ? currentVersion[field as keyof typeof currentVersion] : "";
    const priorValue = field in priorVersion ? priorVersion[field as keyof typeof priorVersion] : "";

    return currentValue !== priorValue;
  }
  const handleConfirmChanges = (): void => {
    if (fetcher.data && item.id) {
      const path = `/api/bo-directors/${isBeneficialOwner ? "beneficial-owner" : "director"}/${item.id}/confirm`;
      const updateRequestDTO = {
        relationId: item.id,
      };

      fetcher.submit(
        updateRequestDTO,
        {
          method: "PUT",
          action: path,
          encType: "application/json",
        },
      );
      handleOpenChange(false)
    }
  };
  const handleRequestUpdate = (): void => {
    handleOpenChange(false);
    setIsUpdateModalOpen(true);
  };
  const getSubmittingText = () => {
    if (isSubmitting) {
      return "Confirming..."
    }

    return showPreviousState ? "Confirm Changes" : "Confirm"
  }

  return (
    <>
      <Dialog modal open={open && !!item} onOpenChange={handleOpenChange}>
        <DialogContent className="max-w-4xl [&>button]:hidden">
          <DialogHeader>
            <DialogDescription>
              {item?.isIndividual ? "Individual" : "Corporate"}
              {isBeneficialOwner ? " Beneficial Owner" : " Officer"}
            </DialogDescription>
            <DialogTitle className="text-2xl font-semibold">

            </DialogTitle>
            <DialogTitle>
              {showPreviousState ? "Confirm changes for" : "Confirm details for"}
              {isBeneficialOwner ? " Beneficial Owner " : " Officer "}
              <span className="text-blue-500">{(fetcher.data?.comparisonData?.currentVersion?.name || item?.name) ?? ""}</span>
            </DialogTitle>
          </DialogHeader>
          {fetcher.data?.comparisonData
            ? (
                <div>
                  <Table>
                    {showPreviousState
                    && (
                      <TableHeader>
                        <TableRow>
                          <TableHead />
                          <TableHead>Current state</TableHead>
                          <TableHead>Previous state</TableHead>
                        </TableRow>
                      </TableHeader>
                    )}
                    <TableBody>
                      {memoizedColumns.length === 0
                        ? (
                            <TableRow>
                              <TableCell colSpan={3} className="text-center py-4">
                                No data available
                              </TableCell>
                            </TableRow>
                          )
                        : (
                            memoizedColumns.map(({ field, name }) => (
                              <TableRow
                                key={field}
                                className={`border-t border-gray-200 ${showPreviousState && hasChanged(field) ? "bg-teal-50" : ""}`}
                              >
                                <TableCell className="font-bold text-black">{name}</TableCell>
                                <TableCell>
                                  <BoDirCell field={field} item={fetcher.data?.comparisonData.currentVersion as (BeneficialOwnerDTO | DirectorDTO)} />
                                </TableCell>
                                {showPreviousState
                                && (
                                  <TableCell>
                                    <BoDirCell field={field} item={fetcher.data?.comparisonData.priorVersion as (BeneficialOwnerDTO | DirectorDTO)} />
                                  </TableCell>
                                )}
                              </TableRow>
                            ))
                          )}
                    </TableBody>
                  </Table>
                </div>
              )
            : null}
          <DialogFooter className="flex items-center justify-between gap-4">
            {isLoading
              ? (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <p>Loading ...</p>
                  </div>
                )
              : (
                  <div className="flex gap-4">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleOpenChange(false)}
                    >
                      <span className="text-xs font-bold">Cancel</span>
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleRequestUpdate}
                      className="flex items-center gap-2"
                    >
                      <span className="text-xs font-bold">Request Update</span>
                    </Button>
                    {fetcher.data && (
                      <Button
                        type="button"
                        variant="default"
                        size="sm"
                        onClick={handleConfirmChanges}
                        className="flex items-center gap-2"
                        disabled={isSubmitting}
                      >
                        <span className="text-xs font-bold">
                          {getSubmittingText()}
                        </span>
                        {!isSubmitting && <CheckIcon size={16} />}
                      </Button>
                    )}
                  </div>
                )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <UpdateModal
        open={isUpdateModalOpen}
        handleOpenChange={setIsUpdateModalOpen}
        selectedItem={item}
      />
    </>
  )
}
