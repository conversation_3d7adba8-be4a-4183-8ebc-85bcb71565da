import {
  Button,
  Combobox,
  Dialog,
  DialogContent,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  ScrollArea,
  ScrollBar,
} from "@netpro/design-system";
import { type ReactNode, useMemo } from "react";
import type { UseFormReturn } from "react-hook-form";
import { Form as RemixForm } from "@remix-run/react"
import type { ArrayFieldName } from "../../forms/tax-payer-identification/TaxPayerIdentification";
import { getCountryOptions } from "~/lib/utilities/countries";
import type { ParentEntitySchemaType } from "~/lib/economic-substance/types/bahamas/tax-payer-identification-schema";

 type Props = {
   open: boolean
   setOpen: (open: boolean) => void
   onSubmit: (data: ParentEntitySchemaType) => void
   form: UseFormReturn<ParentEntitySchemaType>
   type: ArrayFieldName
 }

const FORM_ID = "entity-parent-form"

export function ParentEntityDialog({
  open,
  setOpen,
  onSubmit,
  form,
  type,
}: Props): ReactNode {
  const countryOptions = useMemo(() => getCountryOptions(), []);
  const title = type === "immediateParentEntities" ? "Immediate" : "Ultimate"

  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <RemixForm onSubmit={form.handleSubmit(onSubmit)} className="p-2" noValidate id={FORM_ID}>
              <DialogHeader>
                <DialogTitle>{`${title} Parent Details`}</DialogTitle>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Name*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="alternativeName"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Alternative Name</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="jurisdictionOfFormation"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>
                        <p className="flex gap-1">
                          Jurisdiction of formation*
                        </p>
                      </FormLabel>
                      <FormControl>
                        <Combobox
                          placeholder="Select a country"
                          searchText="Search..."
                          noResultsText="No countries found."
                          items={countryOptions}
                          onChange={field.onChange}
                          value={field.value}
                          invalid={!!fieldState.error}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="incorporationNumber"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Incorporation Number or its equivalent*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="taxpayerIdentificationNumber"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Taxpayer Identification Number TIN#) or other identification reference number*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                  </div>
                </div>
              </DialogFooter>
            </RemixForm>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
