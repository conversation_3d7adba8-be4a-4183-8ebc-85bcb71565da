import { zodResolver } from "@hookform/resolvers/zod";
import {
  DatePicker,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RadioGroup,
  RadioGroupItem,
} from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import type { ReactNode } from "react";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import type { FinancialPeriodSchemaType } from "~/lib/economic-substance/types/bvi/financial-period-schema";
import { financialPeriodSchema } from "~/lib/economic-substance/types/bvi/financial-period-schema";
import { ECONOMIC_SUBSTANCE_FORM_ID } from "~/lib/economic-substance/utilities/constants";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bvi";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";

export function FinancialPeriod(): ReactNode {
  const { submissionData } = useSubmission();
  const fetcher = useFetcher();
  const data = useMemo(() => submissionData[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType, [submissionData]);
  const form = useForm<FinancialPeriodSchemaType>({
    resolver: zodResolver(financialPeriodSchema),
    shouldFocusError: false,
    defaultValues: data || {
      firstFinancialReport: "true",
      startDate: undefined,
      endDate: undefined,
      isReclassifiedToPEH: "false",
    },
  });
  const { formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  function onSubmit(data: FinancialPeriodSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  return (
    <Form {...form}>
      <RemixForm
        onSubmit={form.handleSubmit(onSubmit, onError)}
        noValidate
        id={ECONOMIC_SUBSTANCE_FORM_ID}
        className="space-y-5"
      >
        <div className="flex-col space-y-5">
          <FormField
            control={form.control}
            name="hasFinancialPeriodChanged"
            render={({ field, fieldState }) => (
              <FormItem className="space-y-3 py-2">
                <FormLabel>
                  <p className="flex gap-1">
                    A. Has an application been made and confirmed with ITA to change your financial period?*
                  </p>
                </FormLabel>
                <FormControl>
                  <RadioGroup
                    value={field.value}
                    invalid={!!fieldState.error}
                    className="flex flex-row space-x-2"
                    disabled
                  >
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="true" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Yes
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="false" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        No
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex flex-col gap-5">
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem className="w-full md:w-1/2">
                  <FormLabel>B. Financial Period begins*</FormLabel>
                  <FormControl>
                    <DatePicker
                      date={field.value}
                      onChange={field.onChange}
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem className="w-full md:w-1/2">
                  <FormLabel>C. Financial Period ends*</FormLabel>
                  <FormControl>
                    <DatePicker
                      date={field.value}
                      onChange={field.onChange}
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="previousEndDate"
              render={({ field }) => (
                <FormItem className="w-full md:w-1/2">
                  <FormLabel>C.1 Previous end date</FormLabel>
                  <FormControl>
                    <DatePicker
                      date={field.value}
                      onChange={field.onChange}
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="applicationDateToITA"
              render={({ field }) => (
                <FormItem className="w-full md:w-1/2">
                  <FormLabel>C.2 Date of application to ITA under Rule 13, 15 or 16</FormLabel>
                  <FormControl>
                    <DatePicker
                      date={field.value}
                      onChange={field.onChange}
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </RemixForm>
    </Form>
  )
}
