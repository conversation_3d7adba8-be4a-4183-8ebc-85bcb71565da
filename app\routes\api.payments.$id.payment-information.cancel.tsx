import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/react";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import { clientCancelPayment } from "~/services/api-generated";

export async function action({ request, params }: ActionFunctionArgs) {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);

  const { error } = await clientCancelPayment({
    headers: await authHeaders(request),
    path: {
      paymentId: params.id!,
    },
  });

  if (error) {
    return json({ success: false, error }, { status: 500 });
  } else {
    return json({ success: true }, { status: 200 });
  }
}
