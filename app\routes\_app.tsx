import type { JS<PERSON> } from "react";
import { Outlet } from "@remix-run/react";
import { Logo } from "~/components/ui/branding/Logo";
import { Profile } from "~/components/profile/Profile";

export default function Layout(): JSX.Element {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <header className="flex items-center justify-between ">
        <div className="flex h-16 px-4 items-center gap-2.5">
          <Logo className="h-5 w-auto" />
        </div>
        <Profile alignment="right" className="mr-4" />
      </header>
      <main className="flex grow bg-[top_left]">
        <div className="w-full justify-center items-center flex p-4 gap-2.5 rounded">
          <Outlet />
        </div>
      </main>
    </div>
  )
}
