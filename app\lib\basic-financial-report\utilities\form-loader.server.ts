import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { getPreviousStep } from "../hooks/use-form-steps";
import type { PageSlug } from "./form-pages";
import { mapDocumentIdsToKeys, mapDocumentsToFormKeys } from "./documents";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { middleware } from "~/lib/middlewares.server";
import type { DocumentDTO } from "~/services/api-generated";
import { clientGetSubmission, getApiV1CommonDocuments } from "~/services/api-generated";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import type { BasicFinancialReportContainerLoader } from "~/routes/_main._card.basic-financial-report.$id.$pageName";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function getFormLoader({ request, params }: LoaderFunctionArgs, page: PageSlug): Promise<BasicFinancialReportContainerLoader> {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireBfrModule"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found");
  }

  if (submission.statusText !== SubmissionStatusNames.Draft && submission.statusText !== SubmissionStatusNames.Revision && submission.statusText !== SubmissionStatusNames.Temporal) {
    session.flash("notification", { title: "Error!", message: "Submission was already submitted", variant: "error" });

    return redirect("/basic-financial-report/new", { headers: { "Set-Cookie": await commitSession(session) } });
  }

  const submissionData = getUnflattenedDataSet(submission);
  // Validate previous step was filled in
  const previousPage = getPreviousStep(submissionData, page);
  if (previousPage && !submissionData[previousPage]) {
    // Redirect to the previous page
    return redirect(`/basic-financial-report/${id}/${previousPage}`);
  }

  let mappedDocuments = {}

  if (submission.documentIds?.length && submissionData[page]) {
    const match = mapDocumentIdsToKeys(submissionData[page], submission.documentIds)

    if (Object.values(match).length > 0) {
      const { data: document } = await getApiV1CommonDocuments({
        headers: await authHeaders(request),
        query: {
          documentIds: Object.keys(match),
          includeData: true,
        },
      });

      mappedDocuments = mapDocumentsToFormKeys(document as DocumentDTO[], match)
    }
  }

  return { submission, page, mappedDocuments };
}
