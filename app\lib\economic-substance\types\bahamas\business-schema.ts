import type { z } from "zod";
import { premisesSchema } from "./premises-schema";
import { incomeSchema } from "./income-schema";
import { expenditureSchema } from "./expenditure-schema";
import { employeesSchema } from "./employee-schema";
import { directionManagementSchema } from "./direction-management-schema";
import { cigaSchema } from "./ciga-schema";

export const businessSchema = incomeSchema
  .and(expenditureSchema)
  .and(employeesSchema)
  .and(premisesSchema)
  .and(directionManagementSchema)
  .and(cigaSchema)

export type BusinessSchemaType = z.infer<typeof businessSchema>
