import type { JS<PERSON> } from "react";
import { Link, Outlet } from "@remix-run/react";
import { <PERSON><PERSON> } from "@netpro/design-system";
import { ChevronLeft } from "lucide-react";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";

export function ErrorBoundary(): JSX.Element {
  return (
    <PageErrorBoundary>
      <Button asChild variant="link">
        <Link to="/dashboard">
          <ChevronLeft />
          Go back to the dashboard
        </Link>
      </Button>
    </PageErrorBoundary>
  )
}

export default function Layout(): JSX.Element {
  return <Outlet />
}
