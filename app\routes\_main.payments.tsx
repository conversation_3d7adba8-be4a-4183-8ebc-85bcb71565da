import type { JSX } from "react";
import { Outlet } from "@remix-run/react";
import { PageHeading } from "~/components/layout/headings/PageHeading";

export default function CardLayout(): JSX.Element {
  return (
    <main className="flex flex-col min-h-screen py-9 px-10 lg:pl-[290px] bg-gray-50">
      <header className="flex w-full 1 pb-7">
        <PageHeading />
      </header>
      <section className="flex grow w-full rounded border border-gray-200 py-4 px-5 gap-2.5 bg-white">
        <Outlet />
      </section>
    </main>
  );
}
