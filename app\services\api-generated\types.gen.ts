// This file is auto-generated by @hey-api/openapi-ts

export type AcceptTermsConditionsDTO = {
    version?: (string) | null;
};

export type AcceptTermsConditionsResultDTO = {
    success?: boolean;
    acceptedAt?: string;
};

export type ActivityLogItemDTO = {
    id?: (string) | null;
    actionDate?: string;
    userName?: (string) | null;
    emailAddress?: (string) | null;
    activityType?: (string) | null;
    action?: (string) | null;
    shortDescription?: (string) | null;
    text?: (string) | null;
    entityName?: (string) | null;
    audits?: Array<AuditUnitOfWorkDTO> | null;
};

export type ActivityLogItemDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<ActivityLogItemDTO> | null;
};

export type AddActivityLogDTO = {
    activityType?: (string) | null;
    shortDescription?: (string) | null;
    text?: (string) | null;
};

export type AllSubmissionYearsDTO = {
    moduleId?: string;
    years?: Array<(number)> | null;
};

export type AnnouncementStatus = 'Draft' | 'Scheduled' | 'Sent';

export type AnnualFeeDTO = {
    financialYear?: number;
    isPaid?: boolean;
};

export type APIExceptionModel = {
    code?: number;
    error?: (string) | null;
    exceptionMessage?: (string) | null;
    innerExceptionMessages?: Array<(string)> | null;
};

export type ApplicationUserDTO = {
    id?: string;
    name?: (string) | null;
    surname?: (string) | null;
    username?: (string) | null;
    displayName?: (string) | null;
    email?: (string) | null;
    roleNames?: Array<(string)> | null;
    roleIds?: Array<(string)> | null;
    isActive?: boolean;
    isBlocked?: boolean;
    applicationUserRoles?: Array<ApplicationUserRoleDTO> | null;
    objectId?: (string) | null;
};

export type ApplicationUserRoleDTO = {
    userId?: string;
    roleId?: string;
};

export type AppVersionDTO = {
    version?: (string) | null;
};

export type AttributeCollectionStartResponse = {
    data?: AttributeCollectionStartResponse_Data;
};

export type AttributeCollectionStartResponse_Action = {
    '@odata.type'?: (string) | null;
    message?: (string) | null;
    inputs?: AttributeCollectionStartResponse_Inputs;
};

export type AttributeCollectionStartResponse_Data = {
    '@odata.type'?: (string) | null;
    actions?: Array<AttributeCollectionStartResponse_Action> | null;
};

export type AttributeCollectionStartResponse_Inputs = {
    displayName?: (string) | null;
};

export type AttributeCollectionSubmitResponse = {
    data?: AttributeCollectionSubmitResponse_Data;
};

export type AttributeCollectionSubmitResponse_Action = {
    '@odata.type'?: (string) | null;
    message?: (string) | null;
    attributes?: AttributeCollectionSubmitResponse_Attribute;
    attributeErrors?: AttributeCollectionSubmitResponse_AttributeError;
};

export type AttributeCollectionSubmitResponse_Attribute = {
    displayName?: (string) | null;
};

export type AttributeCollectionSubmitResponse_AttributeError = {
    city?: (string) | null;
    displayName?: (string) | null;
};

export type AttributeCollectionSubmitResponse_Data = {
    '@odata.type'?: (string) | null;
    actions?: Array<AttributeCollectionSubmitResponse_Action> | null;
};

export type AuditUnitOfWorkDetailDTO = {
    id?: string;
    column?: (string) | null;
    oldValue?: (string) | null;
    newValue?: (string) | null;
};

export type AuditUnitOfWorkDTO = {
    id?: string;
    changedAt?: string;
    changedBy?: (string) | null;
    contextId?: (string) | null;
    entities?: Array<AuditUnitOfWorkEntityDTO> | null;
};

export type AuditUnitOfWorkDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<AuditUnitOfWorkDTO> | null;
};

export type AuditUnitOfWorkEntityDTO = {
    id?: string;
    entityName?: (string) | null;
    entityId?: (string) | null;
    action?: (string) | null;
    details?: Array<AuditUnitOfWorkDetailDTO> | null;
};

export type AvailableSubmissionYearsDTO = {
    legalEntityId?: string;
    moduleId?: string;
    years?: Array<(number)> | null;
};

/**
 * Represents the response types for a bad request, for Swagger documentation.
 */
export type BadRequestResponseTypes = {
    readonly value?: unknown;
    readonly index?: number;
    readonly isT0?: boolean;
    readonly isT1?: boolean;
    asT0?: ValidationProblemDetails;
    asT1?: APIExceptionModel;
};

export type BeneficialOwnerComparisonDTO = {
    currentVersion?: BeneficialOwnerDTO;
    priorVersion?: BeneficialOwnerDTO;
};

export type BeneficialOwnerDTO = {
    id?: string;
    uniqueRelationCode?: (string) | null;
    isIndividual?: boolean;
    officerTypeCode?: (string) | null;
    officerTypeName?: (string) | null;
    legalEntityId?: string;
    legalEntityName?: (string) | null;
    name?: (string) | null;
    dateOfBirth?: (string) | null;
    placeOfBirth?: (string) | null;
    countryOfBirth?: (string) | null;
    countryCodeOfBirth?: (string) | null;
    nationality?: (string) | null;
    residentialAddress?: (string) | null;
    incorporationNumber?: (string) | null;
    dateOfIncorporation?: (string) | null;
    address?: (string) | null;
    countryOfFormation?: (string) | null;
    jurisdictionOfRegulator?: (string) | null;
    nameOfRegulator?: (string) | null;
    sovereignState?: (string) | null;
    tin?: (string) | null;
    stockCode?: (string) | null;
    stockExchange?: (string) | null;
    metaData?: LegalEntityRelationMetaData;
};

export type BlockUserDTO = {
    isBlocked: boolean;
};

export type BoDirDataStatus = 'Initial' | 'Refreshed' | 'Confirmed' | 'PendingUpdateRequest' | 'Subsequent';

export type BoDirItemDTO = {
    id?: string;
    productionOffice?: (string) | null;
    referralOffice?: (string) | null;
    legalEntityName?: (string) | null;
    vpEntityNumber?: (string) | null;
    entityPortalCode?: (string) | null;
    masterClientCode?: (string) | null;
    directorVPCode?: (string) | null;
    directorName?: (string) | null;
    position?: (string) | null;
    directorType?: (string) | null;
    officerType?: (string) | null;
    specifics?: (string) | null;
    status?: LegalEntityRelationStatus;
    requestUpdateDate?: (string) | null;
    confirmedDate?: (string) | null;
    isIndividual?: boolean;
};

export type BoDirItemDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<BoDirItemDTO> | null;
};

export type BoDirPosition = 'Director' | 'BeneficialOwner';

export type BoDirSpecifics = 'BoDirInformation' | 'MissingInformation' | 'NoBoDirInformation';

export type CompaniesSearchResultDTO = {
    companyId?: string;
    companyName?: (string) | null;
    incorporationNumber?: (string) | null;
    isActive?: boolean;
    jurisdictionId?: string;
    jurisdictionName?: (string) | null;
    masterClientId?: string;
    masterClientCode?: (string) | null;
    entityType?: (string) | null;
    vpEntityStatus?: (string) | null;
};

export type CompaniesSearchResultsDTO = {
    companies?: Array<CompaniesSearchResultDTO> | null;
};

export type CompanyAnnualFeesDTO = {
    companyId?: string;
    annualFees?: Array<AnnualFeeDTO> | null;
};

export type CompanyDTO = {
    id?: string;
    masterClientId?: string;
    masterClientCode?: (string) | null;
    jurisdictionId?: string;
    jurisdictionName?: (string) | null;
    code?: (string) | null;
    name?: (string) | null;
    vpEntityStatus?: (string) | null;
    entityType?: (string) | null;
    incorporationNumber?: (string) | null;
    isActive?: boolean;
    legacyCode?: (string) | null;
    incorporationDate?: string;
    referralOffice?: (string) | null;
    onboardingStatus?: OnboardingStatus;
    previouslyDeclined?: boolean;
};

export type CompanyDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<CompanyDTO> | null;
};

export type CompanyFinancialYearDto = {
    companyVPCode?: (string) | null;
    financialYear?: number;
};

export type CompanyModuleDTO = {
    id?: string;
    name?: (string) | null;
    key?: (string) | null;
    isActive?: boolean;
    isEnabled?: boolean;
    isApproved?: boolean;
    jurisdictionIsEnabled?: boolean;
};

export type CompanyWithAnnualFeeStatusSearchResultDTO = {
    companyId?: string;
    companyName?: (string) | null;
    companyLegacyCode?: (string) | null;
    companyCode?: (string) | null;
    masterClientId?: string;
    masterClientCode?: (string) | null;
    dateSubmissionCreated?: (string) | null;
    dateSubmissionSubmitted?: (string) | null;
    isPaid?: boolean;
};

export type CompanyWithAnnualFeeStatusSearchResultDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<CompanyWithAnnualFeeStatusSearchResultDTO> | null;
};

export type CompleteRequestForInformationDTO = {
    response?: (string) | null;
    includeAttachments?: boolean;
};

export type ConfirmMFAResetResultDTO = {
    confirmationCode?: (string) | null;
    success?: boolean;
};

export type ConsumerModel = {
    applicationId?: string;
    id?: string;
    name?: (string) | null;
};

export type CreateCurrencyDTO = {
    name?: (string) | null;
    code?: (string) | null;
    symbol?: (string) | null;
};

export type CreateFormTemplateVersionDTO = {
    formTemplateId?: string;
    name?: (string) | null;
    version?: (string) | null;
    startDate?: (string) | null;
    year?: (number) | null;
    formBuilder?: FormBuilder;
};

export type CreateMasterClientUserDTO = {
    masterClientId?: string;
    emailAddress?: (string) | null;
};

export type CreateOrUpdateTaxRateDTO = {
    jurisdictionId?: string;
    taxRate?: number;
    startDate?: string;
};

export type CreatePaymentRequestDTO = {
    legalEntityId: string;
    currencyId: string;
    invoiceIds?: Array<(string)> | null;
};

export type CreateRFIDTO = {
    submissionId?: string;
    deadLine?: string;
    comments?: (string) | null;
    includeAttachments?: boolean;
};

export type CreateTransactionRequestDTO = {
    paymentId?: string;
    description?: (string) | null;
    orderId?: (string) | null;
    paymentRedirectUrl?: (string) | null;
    cancelUrl?: (string) | null;
    companyName?: (string) | null;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: (string) | null;
    merchantEmail?: (string) | null;
};

export type CreateTransactionResponseDTO = {
    paymentProcessorResponse?: StartPaymentResponse;
};

export type CreateUpdateAnnouncementDTO = {
    id?: (string) | null;
    subject?: (string) | null;
    emailSubject?: (string) | null;
    body?: (string) | null;
    includeAttachments?: boolean;
    sendNow?: boolean;
    sendAt?: (string) | null;
    sendToAllMasterClients?: boolean;
    sendToAllJurisdictions?: boolean;
    masterClientCodes?: Array<(string)> | null;
    legalEntityIds?: Array<(string)> | null;
    jurisdictionId?: string;
    userIds?: Array<(string)> | null;
};

export type CreateUserDTO = {
    firstName: string;
    lastName: string;
    displayName: string;
    email: string;
};

export type CurrencyDTO = {
    id?: (string) | null;
    name?: (string) | null;
    code?: (string) | null;
    symbol?: (string) | null;
};

export type CurrencyDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<CurrencyDTO> | null;
};

export type DataMigrationDTO = {
    region?: (string) | null;
    lastUpdated?: string;
    initialSyncCompleted?: boolean;
    migrationCompleted?: boolean;
    status?: MigrationStatus;
    stopRequested?: boolean;
    unprocessedRecords?: Array<UnprocessedRecordDTO> | null;
    entityMigrationProgresses?: Array<EntityMigrationProgressDTO> | null;
    error?: (string) | null;
};

export type DeclineCompanyDTO = {
    declineReason?: (string) | null;
};

export type DirectorComparisonDTO = {
    currentVersion?: DirectorDTO;
    priorVersion?: DirectorDTO;
};

export type DirectorDTO = {
    id?: string;
    uniqueRelationCode?: (string) | null;
    legalEntityId?: string;
    legalEntityName?: (string) | null;
    isIndividual?: boolean;
    officerTypeName?: (string) | null;
    directorType?: (string) | null;
    name?: (string) | null;
    formerName?: (string) | null;
    dateOfBirth?: (string) | null;
    placeOfBirth?: (string) | null;
    countryOfBirth?: (string) | null;
    countryCodeOfBirth?: (string) | null;
    nationality?: (string) | null;
    tin?: (string) | null;
    residentialAddress?: (string) | null;
    serviceAddress?: (string) | null;
    address?: (string) | null;
    incorporationNumber?: (string) | null;
    appointmentDate?: (string) | null;
    cessationDate?: (string) | null;
    dateOfIncorporation?: (string) | null;
    incorporationPlace?: (string) | null;
    incorporationCountry?: (string) | null;
    incorporationCountryCode?: (string) | null;
    metaData?: LegalEntityRelationMetaData;
};

export type DocumentDTO = {
    id?: string;
    type?: number;
    filename?: (string) | null;
    description?: (string) | null;
    fileSize?: number;
    documentData?: (string) | null;
    addedAt?: string;
    hash?: (string) | null;
    expirationDate?: (string) | null;
};

export type DocumentType = 'Unknown' | 'Xls' | 'Pdf' | 'Image';

export type EntityMigrationProgressDTO = {
    entityName?: (string) | null;
    sourceCount?: number;
    processedCount?: number;
    successCount?: number;
    failedCount?: number;
    lastUpdated?: string;
};

export type ExportSubmissionDTO = {
    submissionIds: Array<(string)>;
    financialYear: number;
    jurisdiction: string;
    module: string;
};

export type FeeSettingsDTO = {
    strSubmissionFee?: (number) | null;
    strSubmissionFeeInvoiceText?: (string) | null;
    strSubmissionLatePaymentFeeExempt?: (boolean) | null;
    bfrSubmissionFee?: (number) | null;
};

export type FormBase = {
    id?: (string) | null;
    createdBy?: (string) | null;
    createdAt?: (string) | null;
    name?: (string) | null;
    description?: (string) | null;
    version?: (string) | null;
};

export type FormBuilder = {
    form?: FormBase;
};

export type FormDocumentRevisionDTO = {
    id?: string;
    createdAt?: string;
    revision?: number;
    status?: FormDocumentRevisionStatus;
    statusText?: (string) | null;
    formBuilder?: FormBuilder;
};

/**
 * Version of NetProGroup.Trust.Application.Contracts.Forms.FormDocumentRevisionDTO that includes a NetProGroup.Trust.API.Swagger.ResponseTypes.KeyValueFormBuilderDTO form document.
 * Intended to generate correct OpenAPI documentation.
 */
export type FormDocumentRevisionKeyValueDTO = {
    id?: string;
    createdAt?: string;
    revision?: number;
    status?: FormDocumentRevisionStatus;
    statusText?: (string) | null;
    formBuilder?: KeyValueFormBuilderDTO;
};

export type FormDocumentRevisionStatus = 'Draft' | 'Abandoned' | 'Finalized';

export type FormDocumentStatus = 'Draft' | 'Revision' | 'Finalized';

export type FormDocumentWithRevisionsDTO = {
    id?: string;
    name?: (string) | null;
    createdAt?: string;
    status?: FormDocumentStatus;
    statusText?: (string) | null;
    revisions?: Array<FormDocumentRevisionDTO> | null;
};

/**
 * Version of NetProGroup.Trust.Application.Contracts.Forms.FormDocumentWithRevisionsDTO that includes a NetProGroup.Trust.API.Swagger.ResponseTypes.FormDocumentRevisionKeyValueDTO form document.
 * Intended to generate correct OpenAPI documentation.
 */
export type FormDocumentWithRevisionsKeyValueDTO = {
    id?: string;
    name?: (string) | null;
    createdAt?: string;
    status?: FormDocumentStatus;
    statusText?: (string) | null;
    /**
     * Gets or sets the collection of revisions for the document.
     * Override of the type of the revisions, to display the correct type in OpenAPI documentation.
     */
    revisions?: Array<FormDocumentRevisionKeyValueDTO> | null;
};

export type FormTemplateDTO = {
    id?: string;
    name?: (string) | null;
    jurisdictionId?: string;
    jurisdictionName?: (string) | null;
    moduleId?: string;
    moduleName?: (string) | null;
};

export type FormTemplateVersionDTO = {
    id?: string;
    name?: (string) | null;
    version?: (string) | null;
    startAt?: (string) | null;
    formBuilder?: FormBuilder;
};

export type FormTemplateWithVersionsDTO = {
    id?: string;
    name?: (string) | null;
    jurisdictionId?: string;
    jurisdictionName?: (string) | null;
    moduleId?: string;
    moduleName?: (string) | null;
    versions?: Array<FormTemplateVersionDTO> | null;
};

export type GetSubmissionsPaidStatusRequestDTO = {
    companyFilingYears?: Array<CompanyFinancialYearDto> | null;
    moduleId?: string;
};

export type GetUserMFAMethodDTO = {
    userId?: string;
    mfaMethod?: (string) | null;
};

export type InboxAttachmentDTO = {
    id?: string;
    filename?: (string) | null;
    description?: (string) | null;
    fileSize?: number;
};

export type InboxInfoDTO = {
    totalMessages?: number;
    totalUnreadMessages?: number;
};

export type InboxMessageDTO = {
    id?: string;
    fromUserId?: (string) | null;
    fromUserName?: (string) | null;
    toUserId?: string;
    toUserName?: (string) | null;
    subject?: (string) | null;
    isRead?: boolean;
    readAt?: (string) | null;
    createdAt?: string;
    hasAttachments?: boolean;
    masterClients?: Array<(string)> | null;
    jurisdictions?: Array<(string)> | null;
    legalEntities?: Array<(string)> | null;
    users?: Array<(string)> | null;
    body?: (string) | null;
    readonly inboxAttachments?: Array<InboxAttachmentDTO> | null;
    urlAttachments?: Array<(string)> | null;
};

export type InboxMessageListItemDTO = {
    id?: string;
    fromUserId?: (string) | null;
    fromUserName?: (string) | null;
    toUserId?: string;
    toUserName?: (string) | null;
    subject?: (string) | null;
    isRead?: boolean;
    readAt?: (string) | null;
    createdAt?: string;
    hasAttachments?: boolean;
    masterClients?: Array<(string)> | null;
    jurisdictions?: Array<(string)> | null;
    legalEntities?: Array<(string)> | null;
    users?: Array<(string)> | null;
};

export type InboxMessageListItemDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<InboxMessageListItemDTO> | null;
};

export type InvoiceDTO = {
    id?: string;
    invoiceNr?: (string) | null;
    companyName?: (string) | null;
    amount?: number;
    financialYear?: number;
    incorporationNr?: (string) | null;
    file?: (string) | null;
    date?: string;
    status?: PaymentStatus;
    currencySymbol?: (string) | null;
    paidDate?: (string) | null;
    transactionId?: (string) | null;
    txId?: (string) | null;
    currencyId?: string;
    layout?: (string) | null;
    invoiceLines?: Array<InvoiceLineDTO> | null;
    createdAt?: string;
    address1?: (string) | null;
    address2?: (string) | null;
    addressZipCode?: (string) | null;
    addressCity?: (string) | null;
    addressCountry?: (string) | null;
};

export type InvoiceDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<InvoiceDTO> | null;
};

export type InvoiceLineDTO = {
    id?: string;
    invoiceId?: string;
    currencyId?: string;
    description?: (string) | null;
    sequence?: number;
    amount?: number;
    articleNr?: (string) | null;
};

export type InvoicePaymentStatus = 'UnPaid' | 'Paid';

export type JurisdictionDocumentSettingsDTO = {
    header?: (string) | null;
    footer?: (string) | null;
    companyInfo?: (string) | null;
};

export type JurisdictionDTO = {
    id?: string;
    name?: (string) | null;
    code?: (string) | null;
};

export type JurisdictionDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<JurisdictionDTO> | null;
};

export type JurisdictionTaxRateDTO = {
    id?: string;
    jurisdictionId?: string;
    taxRate?: number;
    startDate?: string;
};

export type JurisdictionTaxRateDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<JurisdictionTaxRateDTO> | null;
};

export type KeyValueForm = {
    id?: (string) | null;
    createdBy?: (string) | null;
    createdAt?: (string) | null;
    name?: (string) | null;
    description?: (string) | null;
    version?: (string) | null;
    dataSet?: {
        [key: string]: ((string) | null);
    } | null;
};

/**
 * Represents the top level of a document holding a polymorphic form.
 */
export type KeyValueFormBuilderDTO = {
    form?: KeyValueForm;
};

export type LegalEntityRelationAssistanceRequestType = 'NoBeneficialOwner' | 'NoDirector' | 'NoShareholder';

export type LegalEntityRelationMetaData = {
    status?: LegalEntityRelationStatus;
    statusText?: (string) | null;
    receivedAt?: (string) | null;
    confirmedAt?: (string) | null;
    confirmedByUserId?: (string) | null;
    confirmedByUserName?: (string) | null;
    updateRequestedAt?: (string) | null;
    updateRequestedByUserId?: (string) | null;
    updateRequestedByUserName?: (string) | null;
    updateRequestType?: LegalEntityRelationUpdateRequestType;
    updateRequestTypeName?: (string) | null;
    updateRequestComments?: (string) | null;
    missingDataFields?: Array<(string)> | null;
};

export type LegalEntityRelationStatus = 'Initial' | 'Refreshed' | 'Confirmed' | 'PendingUpdateRequest' | 'UpdateReceived';

export type LegalEntityRelationUpdateRequestType = 'MissingBeneficialOwners' | 'MissingDirectors' | 'MissingShareholders' | 'ChangeOfBeneficialOwners' | 'ChangeOfBeneficialOwnersAddress' | 'ChangeOfBeneficialOwnersParticulars' | 'ChangeOfDirectors' | 'ChangeOfDirectorsAddress' | 'ChangeOfDirectorsParticulars' | 'ChangeOfShareholders' | 'ChangeOfShareholdersAddress' | 'ChangeOfShareholdersParticulars' | 'OtherUpdateOfBeneficialOwners' | 'OtherUpdateOfDirectors' | 'OtherUpdateOfShareholders';

export type ListAnnouncementDTO = {
    id?: string;
    subject?: (string) | null;
    body?: (string) | null;
    status?: AnnouncementStatus;
    sendAt?: string;
    sentAt?: (string) | null;
    masterClientIds?: Array<(string)> | null;
    legalEntityIds?: Array<(string)> | null;
    jurisdictionIds?: Array<(string)> | null;
    userIds?: Array<(string)> | null;
};

export type ListAnnouncementDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<ListAnnouncementDTO> | null;
};

export type ListBeneficialOwnersDTO = {
    beneficialOwners?: Array<BeneficialOwnerDTO> | null;
};

export type ListCompanyModulesDTO = {
    modules?: Array<CompanyModuleDTO> | null;
};

export type ListDirectorsDTO = {
    directors?: Array<DirectorDTO> | null;
};

export type ListFormTemplatesDTO = {
    formTemplates?: Array<FormTemplateDTO> | null;
};

export type ListModulesDTO = {
    modules?: Array<ModuleDTO> | null;
};

export type ListSubmissionBahamasDTO = {
    id?: string;
    name?: (string) | null;
    createdByEmail?: (string) | null;
    legalEntityCode?: (string) | null;
    legalEntityName?: (string) | null;
    masterClientCode?: (string) | null;
    legalEntityVPCode?: (string) | null;
    status?: SubmissionStatus;
    createdAt?: string;
    createdAtLocal?: (string) | null;
    submittedAt?: (string) | null;
    submittedAtLocal?: (string) | null;
    reopenedAt?: (string) | null;
    initialSubmittedAt?: (string) | null;
    exportedAt?: (string) | null;
    exportedAtLocal?: (string) | null;
    incorporationDate?: (string) | null;
    incorporationCode?: (string) | null;
    paymentMethod?: (string) | null;
    paymentReference?: (string) | null;
    paymentReceivedAt?: (string) | null;
    paymentReceivedAtLocal?: (string) | null;
    financialPeriodStartsAt?: (string) | null;
    financialPeriodEndsAt?: (string) | null;
    legalEntityReferralOffice?: (string) | null;
    reopenRequestComments?: (string) | null;
    hasActivityNone?: boolean;
    hasActivityHoldingBusiness?: boolean;
    hasActivityFinanceLeasingBusiness?: boolean;
    hasActivityBankingBusiness?: boolean;
    hasActivityInsuranceBusiness?: boolean;
    hasActivityFundManagementBusiness?: boolean;
    hasActivityHeadquartersBusiness?: boolean;
    hasActivityShippingBusiness?: boolean;
    hasActivityIntellectualPropertyBusiness?: boolean;
    requestForinformation?: (string) | null;
    requestForInformationStatus?: (string) | null;
    requestForInformationCompletedAt?: (string) | null;
    isPaid?: (boolean) | null;
    isDeleted?: boolean;
    deletedAt?: (string) | null;
};

export type ListSubmissionBahamasDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<ListSubmissionBahamasDTO> | null;
};

export type ListSubmissionDTO = {
    id?: string;
    name?: (string) | null;
    financialYear?: number;
    createdAt?: string;
    createdAtLocal?: (string) | null;
    status?: SubmissionStatus;
    submittedAt?: (string) | null;
    submittedAtLocal?: (string) | null;
    createdByEmail?: (string) | null;
    /**
     * @deprecated
     */
    statusText?: (string) | null;
    isPaid?: (boolean) | null;
    reportId?: (string) | null;
    moduleId?: (string) | null;
    legalEntityId?: string;
    layout?: (string) | null;
    exportedAt?: (string) | null;
    exportedAtLocal?: (string) | null;
    legalEntityName?: (string) | null;
    legalEntityCode?: (string) | null;
    legalEntityVPCode?: (string) | null;
    legalEntityReferralOffice?: (string) | null;
    masterClientCode?: (string) | null;
    paymentMethod?: (string) | null;
    paymentReceivedAt?: (string) | null;
    paymentReceivedAtLocal?: (string) | null;
    paymentReference?: (string) | null;
    txId?: (string) | null;
    invoiceId?: (string) | null;
    isUsingAccountingRecordsTool?: (string) | null;
    financialPeriodStartsAt?: (string) | null;
    financialPeriodEndsAt?: (string) | null;
    lastActivityAt?: (string) | null;
    reopenRequestComments?: (string) | null;
    rfiDeadLine?: (string) | null;
    legalEntityVPStatus?: (string) | null;
    legalEntityVPSubStatus?: (string) | null;
    incorporationNr?: (string) | null;
    moduleName?: (string) | null;
    isDeleted?: boolean;
    deletedAt?: (string) | null;
};

export type ListSubmissionDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<ListSubmissionDTO> | null;
};

export type ListSubmissionRFIDTO = {
    id?: string;
    legalEntityName?: (string) | null;
    legalEntityCode?: (string) | null;
    masterClientCode?: (string) | null;
    financialPeriodStartsAt?: string;
    financialPeriodEndsAt?: string;
    status?: SubmissionStatus;
    exportedAt?: (string) | null;
    rfiCreatedAt?: string;
    rfiDeadLine?: string;
    rfiCompletedAt?: (string) | null;
    rfiLastReminderSentAt?: (string) | null;
};

export type ListSubmissionRFIDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<ListSubmissionRFIDTO> | null;
};

export type ListUserDTO = {
    id?: string;
    firstName?: (string) | null;
    lastName?: (string) | null;
    displayName?: (string) | null;
    email?: (string) | null;
    isRegistered?: boolean;
    invitationDetails?: UserInvitationDetailsDTO;
};

export type ListUserDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<ListUserDTO> | null;
};

export type MarkSubmissionsAsPaidByCompanyAndYearResultDTO = {
    companyVPCode?: (string) | null;
    financialYear?: number;
    errorMessage?: (string) | null;
};

export type MarkSubmissionsAsPaidByCompanyYearResponse = {
    results?: Array<MarkSubmissionsAsPaidByCompanyAndYearResultDTO> | null;
};

export type MarkSubmissionsAsPaidByCompanyYearsRequestDTO = {
    companyFilingYears?: Array<CompanyFinancialYearDto> | null;
    isPaid?: boolean;
    moduleId?: string;
};

export type MarkSubmissionsAsPaidRequestDTO = {
    submissionIds?: Array<(string)> | null;
    isPaid?: boolean;
};

export type MarkSubmissionsAsPaidResponse = {
    results?: {
        [key: string]: MarkSubmissionsAsPaidResultDTO;
    } | null;
};

export type MarkSubmissionsAsPaidResultDTO = {
    errorMessage?: (string) | null;
};

export type MasterClientDTO = {
    id?: string;
    code?: (string) | null;
    jurisdictions?: Array<JurisdictionDTO> | null;
    masterClientUsers?: Array<ListUserDTO> | null;
    masterClientManagers?: Array<ListUserDTO> | null;
};

export type MasterClientDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<MasterClientDTO> | null;
};

export type MasterClientsSearchResultDTO = {
    masterClientId?: string;
    masterClientCode?: (string) | null;
    companies?: Array<CompanyDTO> | null;
    hasActiveRequestsForInformation?: boolean;
};

export type MasterClientsSearchResultsDTO = {
    masterClients?: Array<MasterClientsSearchResultDTO> | null;
};

export type MasterClientUserDTO = {
    id?: string;
    masterClientId?: string;
    userId?: string;
    isManuallyAdded?: boolean;
    firstName?: (string) | null;
    lastName?: (string) | null;
    email?: (string) | null;
};

export type MFAInfoDTO = {
    userId?: string;
    mfaMethod?: (string) | null;
    mfaIsEnabled?: boolean;
    mfaAuthenticatorQRUrl?: (string) | null;
    mfaAuthenticatorSecret?: (string) | null;
    mfaEmailCodeExpiresAt?: (string) | null;
    mfaEmailCodeExpiresIn?: number;
};

export type MigrationStatus = 'NotStarted' | 'InProgress' | 'Completed' | 'Failed' | 'Cancelled';

export type ModuleDTO = {
    id?: string;
    name?: (string) | null;
    key?: (string) | null;
    isActive?: (boolean) | null;
    isEnabled?: (boolean) | null;
};

export type OnboardingStatus = 'Unknown' | 'Onboarding' | 'Approved' | 'Declined' | 'ClosedWhileOnboarding';

export type PaymentDetailsResponseDTO = {
    id?: string;
    legalEntityId?: string;
    currencyId?: string;
    amount?: number;
    status?: PaymentStatus;
    paymentTransactions?: Array<PaymentTransactionResponseDTO> | null;
};

export type PaymentDTO = {
    id?: string;
    legalEntityId?: string;
    companyName?: (string) | null;
    financialYear?: number;
    incorporationNr?: (string) | null;
    dateTime?: string;
    currencyId?: string;
    currencySymbol?: (string) | null;
    amount?: number;
    status?: PaymentStatus;
};

export type PaymentDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<PaymentDTO> | null;
};

export type PaymentStatus = 'Pending' | 'Completed' | 'Failed' | 'Refunded' | 'Cancelled' | 'InProgress' | 'OnHold' | 'Disputed' | 'Decline';

export type PaymentTransactionResponseDTO = {
    result?: (string) | null;
    resultCode?: (string) | null;
    resultMessage?: (string) | null;
    transactionId?: (string) | null;
    status?: (string) | null;
    processCreatedAt?: (string) | null;
    paidAt?: (string) | null;
    isFinished?: boolean;
    paymentProviderId?: string;
};

export type PCPApplicationUserDTO = {
    id?: string;
    name?: (string) | null;
    surname?: (string) | null;
    username?: (string) | null;
    displayName?: (string) | null;
    email?: (string) | null;
    roleNames?: Array<(string)> | null;
    roleIds?: Array<(string)> | null;
    isActive?: boolean;
    isBlocked?: boolean;
    applicationUserRoles?: Array<ApplicationUserRoleDTO> | null;
    objectId?: (string) | null;
    permissions?: Array<UserPermissionDTO> | null;
    primaryRoleLabel?: (string) | null;
};

export type PCPApplicationUserDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<PCPApplicationUserDTO> | null;
};

export type PermissionDTO = {
    permissionName?: ('announcements.search' | 'announcements.view' | 'announcements.delete' | 'announcements.create-limited' | 'announcements.create' | 'bfr.panama.submissions.view' | 'bfr.panama.submissions.search' | 'bfr.panama.submissions.export' | 'bfr.panama.submissions.reset' | 'bfr.panama.submissions.view-paid' | 'bfr.panama.submissions.mark-paid' | 'bfr.panama.invoices.export' | 'bfr.panama.rfi-request.view' | 'bfr.panama.rfi-request.start' | 'bfr.panama.rfi-request.cancel' | 'companies.custom-bfr-fee.view' | 'companies.custom-bfr-fee.set' | 'bo-dir.search' | 'bo-dir.view' | 'bo-dir.export' | 'companies.onboarding.access' | 'companies.onboarding.approve' | 'companies.onboarding.reject' | 'companies.search' | 'companies.view' | 'companies.modules.available.view' | 'companies.modules.available.set' | 'companies.custom-str-fee.view' | 'companies.custom-str-fee.set' | 'companies.annual-fee.view' | 'companies.annual-fee.set' | 'companies.log.view' | 'es.bahamas.submissions.view' | 'es.bahamas.submissions.search' | 'es.bahamas.submissions.export' | 'es.bahamas.submissions.reset' | 'es.bahamas.submissions.delete-completed' | 'es.bahamas.submissions.delete-saved' | 'es.bahamas.submissions.view-paid' | 'es.bahamas.submissions.mark-paid' | 'es.bahamas.payments.import' | 'es.bahamas.submissions.export.ita' | 'es.bahamas.invoices.export' | 'es.bahamas.rfi-request.view' | 'es.bahamas.rfi-request.start' | 'es.bahamas.rfi-request.cancel' | 'es.bahamas.companies.custom-es-fee.view' | 'es.bahamas.companies.custom-es-fee.set' | 'es.bvi.submissions.view' | 'es.bvi.submissions.search' | 'es.bvi.submissions.export' | 'es.bvi.submissions.reset' | 'es.bvi.submissions.delete-completed' | 'es.bvi.submissions.delete-saved' | 'es.bvi.submissions.view-paid' | 'es.bvi.submissions.mark-paid' | 'es.bvi.payments.import' | 'es.bvi.submissions.export.ita' | 'es.bvi.invoices.export' | 'es.bvi.rfi-request.start' | 'es.bvi.companies.custom-es-fee.view' | 'es.bvi.companies.custom-es-fee.set' | 'masterclients.search' | 'masterclients.view' | 'masterclients.send-invitation' | 'masterclients.trident-users.view' | 'masterclients.trident-users.add' | 'masterclients.log.view' | 'status.viewpoint-sync.view' | 'str.submissions.view' | 'str.submissions.search' | 'str.submissions.export' | 'str.submissions.reset' | 'str.submissions.delete-completed' | 'str.submissions.delete-saved' | 'str.submissions.view-paid' | 'str.submissions.mark-paid' | 'str.payments.import' | 'str.submissions.export.ird' | 'str.invoices.export' | 'str.management-information' | 'str.rfi-request.view' | 'str.rfi-request.start' | 'str.rfi-request.cancel' | 'str.fee.view' | 'str.fee.set' | 'str.late-payments.view' | 'str.late-payments.set' | 'str.data-migration' | 'users.search' | 'users.view' | 'users.block' | 'users.unblock' | 'users.reset-authentication' | 'users.view-log') | null;
};

export type permissionName = 'announcements.search' | 'announcements.view' | 'announcements.delete' | 'announcements.create-limited' | 'announcements.create' | 'bfr.panama.submissions.view' | 'bfr.panama.submissions.search' | 'bfr.panama.submissions.export' | 'bfr.panama.submissions.reset' | 'bfr.panama.submissions.view-paid' | 'bfr.panama.submissions.mark-paid' | 'bfr.panama.invoices.export' | 'bfr.panama.rfi-request.view' | 'bfr.panama.rfi-request.start' | 'bfr.panama.rfi-request.cancel' | 'companies.custom-bfr-fee.view' | 'companies.custom-bfr-fee.set' | 'bo-dir.search' | 'bo-dir.view' | 'bo-dir.export' | 'companies.onboarding.access' | 'companies.onboarding.approve' | 'companies.onboarding.reject' | 'companies.search' | 'companies.view' | 'companies.modules.available.view' | 'companies.modules.available.set' | 'companies.custom-str-fee.view' | 'companies.custom-str-fee.set' | 'companies.annual-fee.view' | 'companies.annual-fee.set' | 'companies.log.view' | 'es.bahamas.submissions.view' | 'es.bahamas.submissions.search' | 'es.bahamas.submissions.export' | 'es.bahamas.submissions.reset' | 'es.bahamas.submissions.delete-completed' | 'es.bahamas.submissions.delete-saved' | 'es.bahamas.submissions.view-paid' | 'es.bahamas.submissions.mark-paid' | 'es.bahamas.payments.import' | 'es.bahamas.submissions.export.ita' | 'es.bahamas.invoices.export' | 'es.bahamas.rfi-request.view' | 'es.bahamas.rfi-request.start' | 'es.bahamas.rfi-request.cancel' | 'es.bahamas.companies.custom-es-fee.view' | 'es.bahamas.companies.custom-es-fee.set' | 'es.bvi.submissions.view' | 'es.bvi.submissions.search' | 'es.bvi.submissions.export' | 'es.bvi.submissions.reset' | 'es.bvi.submissions.delete-completed' | 'es.bvi.submissions.delete-saved' | 'es.bvi.submissions.view-paid' | 'es.bvi.submissions.mark-paid' | 'es.bvi.payments.import' | 'es.bvi.submissions.export.ita' | 'es.bvi.invoices.export' | 'es.bvi.rfi-request.start' | 'es.bvi.companies.custom-es-fee.view' | 'es.bvi.companies.custom-es-fee.set' | 'masterclients.search' | 'masterclients.view' | 'masterclients.send-invitation' | 'masterclients.trident-users.view' | 'masterclients.trident-users.add' | 'masterclients.log.view' | 'status.viewpoint-sync.view' | 'str.submissions.view' | 'str.submissions.search' | 'str.submissions.export' | 'str.submissions.reset' | 'str.submissions.delete-completed' | 'str.submissions.delete-saved' | 'str.submissions.view-paid' | 'str.submissions.mark-paid' | 'str.payments.import' | 'str.submissions.export.ird' | 'str.invoices.export' | 'str.management-information' | 'str.rfi-request.view' | 'str.rfi-request.start' | 'str.rfi-request.cancel' | 'str.fee.view' | 'str.fee.set' | 'str.late-payments.view' | 'str.late-payments.set' | 'str.data-migration' | 'users.search' | 'users.view' | 'users.block' | 'users.unblock' | 'users.reset-authentication' | 'users.view-log';

export type ProblemDetails = {
    type?: (string) | null;
    title?: (string) | null;
    status?: (number) | null;
    detail?: (string) | null;
    instance?: (string) | null;
    [key: string]: (unknown | string | number) | undefined;
};

export type ProductionOfficeType = 'THKO' | 'TBVI' | 'TCYP' | 'TPANVG' | 'TNEV';

export type ProviderModel = {
    id?: string;
    name?: (string) | null;
};

export type ReopenSubmissionDTO = {
    submissionId?: string;
    comments?: (string) | null;
};

export type ReportDTO = {
    id?: string;
    reportName?: (string) | null;
    filename?: (string) | null;
    type?: (string) | null;
    createdAt?: string;
};

export type ReportDTOPaginatedResponse = {
    pageNumber?: number;
    pageCount?: number;
    pageSize?: number;
    totalItemCount?: number;
    readonly hasPrevious?: boolean;
    readonly hasNext?: boolean;
    data?: Array<ReportDTO> | null;
};

export type ReportType = 'Financial' | 'CompaniesWithoutSubmissions' | 'SubmissionsNotPaid' | 'ContactsInfo' | 'BasicFinancialReport' | 'EconomicSubstance';

export type RequestAssistanceDTO = {
    legalEntityId?: string;
    assistanceRequestType?: LegalEntityRelationAssistanceRequestType;
    assistanceRequestComments?: (string) | null;
};

export type RequestForInformationStatus = 'Draft' | 'Active' | 'Cancelled' | 'Completed';

export type RequestMFAResetResultDTO = {
    userId?: string;
    mfaEmailCodeExpiresAt?: (string) | null;
    mfaEmailCodeExpiresIn?: number;
};

export type RequestUpdateDTO = {
    uniqueRelationId?: (string) | null;
    updateRequestType?: LegalEntityRelationUpdateRequestType;
    updateRequestComments?: (string) | null;
};

export type SendEmailDTO = {
    recipientEmailAddress: string;
    subject: string;
    body: string;
    legalEntityId?: (string) | null;
};

export type SendInvitationsDTO = {
    userIds?: Array<(string)> | null;
    userMasterClients?: Array<SendInvitationUserDTO> | null;
};

export type SendInvitationUserDTO = {
    userId?: string;
    masterClientId?: string;
};

export type SetCompanyAnnualFeesDTO = {
    annualFees?: Array<AnnualFeeDTO> | null;
};

export type SetCompanyModuleDTO = {
    id?: string;
    isEnabled?: boolean;
    isApproved?: boolean;
};

export type SetCompanyModulesDTO = {
    modules?: Array<SetCompanyModuleDTO> | null;
};

export type SetModuleDTO = {
    id?: string;
    isEnabled?: (boolean) | null;
    isApproved?: (boolean) | null;
};

export type SetModulesDTO = {
    modules?: Array<SetModuleDTO> | null;
};

export type SettingsDTO = {
    jurisdictionDocumentSettings?: JurisdictionDocumentSettingsDTO;
    feeSettings?: FeeSettingsDTO;
    strLatePaymentFeeSettings?: STRLatePaymentFeeSettingsDTO;
    submissionSettings?: SubmissionSettingsDTO;
    logs?: Array<ActivityLogItemDTO> | null;
};

export type SetUserMFAMethodDTO = {
    userId?: string;
    mfaMethod?: (string) | null;
};

export type ShareholderDTO = {
    id?: string;
    uniqueRelationCode?: (string) | null;
    isIndividual?: boolean;
    officerTypeCode?: (string) | null;
    officerTypeName?: (string) | null;
    legalEntityId?: string;
    legalEntityName?: (string) | null;
    name?: (string) | null;
    dateOfBirth?: (string) | null;
    placeOfBirth?: (string) | null;
    countryOfBirth?: (string) | null;
    countryCodeOfBirth?: (string) | null;
    nationality?: (string) | null;
    residentialAddress?: (string) | null;
    incorporationNumber?: (string) | null;
    dateOfIncorporation?: (string) | null;
    address?: (string) | null;
    countryOfFormation?: (string) | null;
    jurisdictionOfRegulator?: (string) | null;
    nameOfRegulator?: (string) | null;
    sovereignState?: (string) | null;
    tin?: (string) | null;
    stockCode?: (string) | null;
    stockExchange?: (string) | null;
    metaData?: LegalEntityRelationMetaData;
};

export type StartPaymentResponse = {
    transactionId?: string;
    providerTransactionId?: (string) | null;
    callBackUrl?: (string) | null;
    result?: number;
    resultText?: (string) | null;
    resultNumber?: number;
    htmlContent?: (string) | null;
    consumer?: ConsumerModel;
    provider?: ProviderModel;
};

export type STRLatePaymentFeeDTO = {
    id?: (string) | null;
    description?: (string) | null;
    invoiceText?: (string) | null;
    startAt?: string;
    endAt?: string;
    financialYear?: (number) | null;
    amount?: number;
    currencyCode?: (string) | null;
    charge?: boolean;
};

export type STRLatePaymentFeeSettingsDTO = {
    strLatePaymentFees?: Array<STRLatePaymentFeeDTO> | null;
};

export type SubmissionDataRequestDTO = {
    submissionIds: Array<(string)>;
};

export type SubmissionDataSetDTO = {
    id?: string;
    dataSet?: {
        [key: string]: ((string) | null);
    } | null;
    documentIds?: Array<(string)> | null;
};

export type SubmissionDTO = {
    id?: string;
    name?: (string) | null;
    financialYear?: number;
    createdAt?: string;
    createdAtLocal?: (string) | null;
    status?: SubmissionStatus;
    submittedAt?: (string) | null;
    submittedAtLocal?: (string) | null;
    createdByEmail?: (string) | null;
    /**
     * @deprecated
     */
    statusText?: (string) | null;
    formDocument?: FormDocumentWithRevisionsDTO;
    isPaid?: (boolean) | null;
    paidAt?: (string) | null;
    reportId?: (string) | null;
    moduleId?: (string) | null;
    legalEntityId?: string;
    layout?: (string) | null;
    exportedAt?: (string) | null;
    exportedAtLocal?: (string) | null;
    legalEntityName?: (string) | null;
    legalEntityCode?: (string) | null;
    legalEntityVPCode?: (string) | null;
    legalEntityReferralOffice?: (string) | null;
    masterClientCode?: (string) | null;
    paymentMethod?: (string) | null;
    paymentReceivedAt?: (string) | null;
    paymentReceivedAtLocal?: (string) | null;
    paymentReference?: (string) | null;
    invoiceId?: (string) | null;
    startsAt?: (string) | null;
    endsAt?: (string) | null;
    documentIds?: Array<(string)> | null;
    initialSubmittedAt?: (string) | null;
    reopenedAt?: (string) | null;
    reopenRequestComments?: (string) | null;
    requestsForInformation?: Array<SubmissionRFIDTO> | null;
    legalEntityVPStatus?: (string) | null;
    legalEntityVPSubStatus?: (string) | null;
    isDeleted?: boolean;
    deletedAt?: (string) | null;
};

export type SubmissionDTOResponseTypes = {
    readonly value?: unknown;
    readonly index?: number;
    readonly isT0?: boolean;
    readonly isT1?: boolean;
    asT0?: SubmissionDTO;
    asT1?: SubmissionKeyValueDTO;
};

/**
 * Version of NetProGroup.Trust.Application.Contracts.Submissions.SubmissionDTO that includes a NetProGroup.Trust.API.Swagger.ResponseTypes.FormDocumentWithRevisionsKeyValueDTO form document.
 * Intended to generate correct OpenAPI documentation.
 */
export type SubmissionKeyValueDTO = {
    id?: string;
    name?: (string) | null;
    financialYear?: number;
    createdAt?: string;
    createdAtLocal?: (string) | null;
    status?: SubmissionStatus;
    submittedAt?: (string) | null;
    submittedAtLocal?: (string) | null;
    createdByEmail?: (string) | null;
    /**
     * @deprecated
     */
    statusText?: (string) | null;
    formDocument?: FormDocumentWithRevisionsKeyValueDTO;
    isPaid?: (boolean) | null;
    paidAt?: (string) | null;
    reportId?: (string) | null;
    moduleId?: (string) | null;
    legalEntityId?: string;
    layout?: (string) | null;
    exportedAt?: (string) | null;
    exportedAtLocal?: (string) | null;
    legalEntityName?: (string) | null;
    legalEntityCode?: (string) | null;
    legalEntityVPCode?: (string) | null;
    legalEntityReferralOffice?: (string) | null;
    masterClientCode?: (string) | null;
    paymentMethod?: (string) | null;
    paymentReceivedAt?: (string) | null;
    paymentReceivedAtLocal?: (string) | null;
    paymentReference?: (string) | null;
    invoiceId?: (string) | null;
    startsAt?: (string) | null;
    endsAt?: (string) | null;
    documentIds?: Array<(string)> | null;
    initialSubmittedAt?: (string) | null;
    reopenedAt?: (string) | null;
    reopenRequestComments?: (string) | null;
    requestsForInformation?: Array<SubmissionRFIDTO> | null;
    legalEntityVPStatus?: (string) | null;
    legalEntityVPSubStatus?: (string) | null;
    isDeleted?: boolean;
    deletedAt?: (string) | null;
};

export type SubmissionPaidStatusDto = {
    companyVPCode?: (string) | null;
    financialYear?: number;
    isPaid?: (boolean) | null;
    submissionAvailable?: boolean;
};

export type SubmissionRFIDetailsDTO = {
    id?: string;
    status?: SubmissionStatus;
    requestsForInformation?: Array<SubmissionRFIDTO> | null;
    activityLogItems?: Array<ActivityLogItemDTO> | null;
};

export type SubmissionRFIDocumentDTO = {
    id?: string;
    documentId?: string;
    document?: DocumentDTO;
    requestForInformationId?: string;
    createdByManagement?: boolean;
};

export type SubmissionRFIDTO = {
    id?: string;
    submissionId?: string;
    deadLine?: string;
    comments?: (string) | null;
    status?: RequestForInformationStatus;
    readonly documents?: Array<SubmissionRFIDocumentDTO> | null;
};

export type SubmissionSettingsDTO = {
    firstSubmissionYear?: (number) | null;
};

export type SubmissionsPaidStatusResponse = {
    paidStatuses?: Array<SubmissionPaidStatusDto> | null;
};

export type SubmissionStatus = 'Draft' | 'Revision' | 'Scheduled' | 'Submitted' | 'Paid' | 'Temporal' | 'InformationRequested';

export type SubmitPaymentRequestDTO = {
    transactionId: string;
    tokenId: string;
};

export type SubmitPaymentResponseDTO = {
    transactionId?: string;
    providerTransactionId?: (string) | null;
    result?: number;
    resultText?: (string) | null;
    resultNumber?: number;
};

export type SubmitSubmissionDTO = {
    submissionId?: string;
    scheduleSubmit?: boolean;
};

export type SyncDetailsDTO = {
    lastSuccessfulSync?: (string) | null;
    jurisdictionsUsed?: Array<(string)> | null;
    updatedCount?: number;
    deletedCount?: number;
};

export type TermsConditionsStatusDTO = {
    isAccepted?: boolean;
    version?: (string) | null;
    acceptedAt?: (string) | null;
};

export type UnprocessedRecordDTO = {
    entityType?: (string) | null;
    identifier?: (string) | null;
    reason?: (string) | null;
};

export type UpdateCompanyAnnualFeeStatusDTO = {
    financialYear?: number;
    isPaid?: boolean;
    legalEntityIds?: Array<(string)> | null;
};

export type UpdateFormTemplateVersionDTO = {
    id?: string;
    name?: (string) | null;
    version?: (string) | null;
    startDate?: (string) | null;
    year?: (number) | null;
    formBuilder?: FormBuilder;
};

export type UpdateSubmissionInformationDTO = {
    startAt?: string;
    endAt?: string;
};

export type UserInvitationDetailsDTO = {
    isInvited?: boolean;
    lastInvitationAt?: (string) | null;
};

export type UserMasterClientsDTO = {
    userId?: string;
    masterClientIds?: Array<(string)> | null;
    removeUnmentionedMasterClients?: boolean;
};

export type UserPermissionDTO = {
    permissionName?: ('announcements.search' | 'announcements.view' | 'announcements.delete' | 'announcements.create-limited' | 'announcements.create' | 'bfr.panama.submissions.view' | 'bfr.panama.submissions.search' | 'bfr.panama.submissions.export' | 'bfr.panama.submissions.reset' | 'bfr.panama.submissions.view-paid' | 'bfr.panama.submissions.mark-paid' | 'bfr.panama.invoices.export' | 'bfr.panama.rfi-request.view' | 'bfr.panama.rfi-request.start' | 'bfr.panama.rfi-request.cancel' | 'companies.custom-bfr-fee.view' | 'companies.custom-bfr-fee.set' | 'bo-dir.search' | 'bo-dir.view' | 'bo-dir.export' | 'companies.onboarding.access' | 'companies.onboarding.approve' | 'companies.onboarding.reject' | 'companies.search' | 'companies.view' | 'companies.modules.available.view' | 'companies.modules.available.set' | 'companies.custom-str-fee.view' | 'companies.custom-str-fee.set' | 'companies.annual-fee.view' | 'companies.annual-fee.set' | 'companies.log.view' | 'es.bahamas.submissions.view' | 'es.bahamas.submissions.search' | 'es.bahamas.submissions.export' | 'es.bahamas.submissions.reset' | 'es.bahamas.submissions.delete-completed' | 'es.bahamas.submissions.delete-saved' | 'es.bahamas.submissions.view-paid' | 'es.bahamas.submissions.mark-paid' | 'es.bahamas.payments.import' | 'es.bahamas.submissions.export.ita' | 'es.bahamas.invoices.export' | 'es.bahamas.rfi-request.view' | 'es.bahamas.rfi-request.start' | 'es.bahamas.rfi-request.cancel' | 'es.bahamas.companies.custom-es-fee.view' | 'es.bahamas.companies.custom-es-fee.set' | 'es.bvi.submissions.view' | 'es.bvi.submissions.search' | 'es.bvi.submissions.export' | 'es.bvi.submissions.reset' | 'es.bvi.submissions.delete-completed' | 'es.bvi.submissions.delete-saved' | 'es.bvi.submissions.view-paid' | 'es.bvi.submissions.mark-paid' | 'es.bvi.payments.import' | 'es.bvi.submissions.export.ita' | 'es.bvi.invoices.export' | 'es.bvi.rfi-request.start' | 'es.bvi.companies.custom-es-fee.view' | 'es.bvi.companies.custom-es-fee.set' | 'masterclients.search' | 'masterclients.view' | 'masterclients.send-invitation' | 'masterclients.trident-users.view' | 'masterclients.trident-users.add' | 'masterclients.log.view' | 'status.viewpoint-sync.view' | 'str.submissions.view' | 'str.submissions.search' | 'str.submissions.export' | 'str.submissions.reset' | 'str.submissions.delete-completed' | 'str.submissions.delete-saved' | 'str.submissions.view-paid' | 'str.submissions.mark-paid' | 'str.payments.import' | 'str.submissions.export.ird' | 'str.invoices.export' | 'str.management-information' | 'str.rfi-request.view' | 'str.rfi-request.start' | 'str.rfi-request.cancel' | 'str.fee.view' | 'str.fee.set' | 'str.late-payments.view' | 'str.late-payments.set' | 'str.data-migration' | 'users.search' | 'users.view' | 'users.block' | 'users.unblock' | 'users.reset-authentication' | 'users.view-log') | null;
};

export type ValidationProblemDetails = {
    type?: (string) | null;
    title?: (string) | null;
    status?: (number) | null;
    detail?: (string) | null;
    instance?: (string) | null;
    errors?: {
        [key: string]: Array<(string)>;
    } | null;
    [key: string]: (unknown | string | number) | undefined;
};

export type VerifyMFACodeResultDTO = {
    verificationCode?: (string) | null;
    success?: boolean;
};

export type ViewPointSyncStatusDTO = {
    syncDetails?: {
        [key: string]: SyncDetailsDTO;
    } | null;
};

export type AddActivityLogData = {
    /**
     * The model as AddActivityLogDTO.
     */
    body?: AddActivityLogDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type AddActivityLogResponse = (unknown);

export type AddActivityLogError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ListActivityLogsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * Optional. The ID of the entity to filter activity logs for.
         */
        entityId?: string;
        /**
         * The optional start date for the period to look for.
         */
        fromDate?: string;
        /**
         * Optional. Page number for pagination (default is 1).
         */
        pageNumber?: number;
        /**
         * Optional. Number of records per page (default is 20).
         */
        pageSize?: number;
        /**
         * The optional end date for the period to look for.
         */
        toDate?: string;
    };
};

export type ListActivityLogsResponse = (ActivityLogItemDTOPaginatedResponse);

export type ListActivityLogsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetActivityLogData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the ActivityLog to get.
         */
        activityLogId: string;
    };
};

export type GetActivityLogResponse = (ActivityLogItemDTO);

export type GetActivityLogError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementListAnnouncementsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        GeneralSearchTerm?: string;
        PageNumber?: number;
        PageSize?: number;
        SortBy?: string;
        SortOrder?: string;
    };
};

export type ManagementListAnnouncementsResponse = (ListAnnouncementDTOPaginatedResponse);

export type ManagementListAnnouncementsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementAnnouncementCreateData = {
    /**
     * The necessary data used to create an announcement.
     */
    body?: CreateUpdateAnnouncementDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementAnnouncementCreateResponse = ((Blob | File));

export type ManagementAnnouncementCreateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementAnnouncementUpdateData = {
    /**
     * The necessary data used to update an announcement.
     */
    body?: CreateUpdateAnnouncementDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The announcement id as Guid.
         */
        announcementId: string;
    };
};

export type ManagementAnnouncementUpdateResponse = ((Blob | File));

export type ManagementAnnouncementUpdateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementAnnouncementGetByIdData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The announcementId as Guid.
         */
        announcementId: string;
    };
};

export type ManagementAnnouncementGetByIdResponse = ((Blob | File));

export type ManagementAnnouncementGetByIdError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementDeleteAnnouncementData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the announcement to delete as Guid.
         */
        announcementId: string;
    };
};

export type ManagementDeleteAnnouncementResponse = (unknown);

export type ManagementDeleteAnnouncementError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementCreateAnnouncementDocumentData = {
    body?: {
        UploadComplete?: boolean;
        File: (Blob | File);
        Type?: DocumentType;
        Description?: string;
    };
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the announcement as Guid.
         */
        announcementId: string;
    };
};

export type ManagementCreateAnnouncementDocumentResponse = (string);

export type ManagementCreateAnnouncementDocumentError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementDeleteAnnouncementDocumentData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the announcement document to delete as Guid.
         */
        announcementDocumentId: string;
    };
    query?: {
        /**
         * The value indicating whether the upload of documents is finished or not.
         */
        uploadComplete?: boolean;
    };
};

export type ManagementDeleteAnnouncementDocumentResponse = (unknown);

export type ManagementDeleteAnnouncementDocumentError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ListAuditsData = {
    headers?: {
        'x-userid'?: string;
    };
    query: {
        /**
         * The ID of the entity to get audits for.
         */
        entityId: string;
        /**
         * Optional. Page number for pagination (default is 1).
         */
        pageNumber?: number;
        /**
         * Optional. Number of records per page (default is 20).
         */
        pageSize?: number;
    };
};

export type ListAuditsResponse = (AuditUnitOfWorkDTOPaginatedResponse);

export type ListAuditsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetAuditData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the Audit to get.
         */
        auditId: string;
    };
};

export type GetAuditResponse = (AuditUnitOfWorkDTO);

export type GetAuditError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetBeneficialOwnerData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the beneficial owner to get.
         */
        beneficialownerId: string;
    };
};

export type ManagementGetBeneficialOwnerResponse = (BeneficialOwnerDTO);

export type ManagementGetBeneficialOwnerError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetBeneficialOwnerForComparisonData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the beneficial owner to get.
         */
        beneficialownerId: string;
    };
};

export type ManagementGetBeneficialOwnerForComparisonResponse = (BeneficialOwnerComparisonDTO);

export type ManagementGetBeneficialOwnerForComparisonError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetBeneficialOwnerData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the beneficial owner to get.
         */
        relationId: string;
    };
};

export type ClientGetBeneficialOwnerResponse = (BeneficialOwnerDTO);

export type ClientGetBeneficialOwnerError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetBeneficialOwnerForComparisonData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the beneficial owner to get.
         */
        relationId: string;
    };
};

export type ClientGetBeneficialOwnerForComparisonResponse = (BeneficialOwnerComparisonDTO);

export type ClientGetBeneficialOwnerForComparisonError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientConfirmBeneficialOwnerData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the beneficial owner to confirm.
         */
        relationId: string;
    };
};

export type ClientConfirmBeneficialOwnerResponse = (BeneficialOwnerDTO);

export type ClientConfirmBeneficialOwnerError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientRequestBeneficialOwnerUpdateData = {
    /**
     * The model for the request for update.
     */
    body?: RequestUpdateDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the beneficial owner to create an update request for.
         */
        relationId: string;
    };
};

export type ClientRequestBeneficialOwnerUpdateResponse = (BeneficialOwnerDTO);

export type ClientRequestBeneficialOwnerUpdateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientBeneficialOwnerUpdateSimulationData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the beneficial owner to simulate update for.
         */
        relationId: string;
    };
    query?: {
        /**
         * The new name to simulate for the beneficial owner.
         */
        newName?: string;
    };
};

export type ClientBeneficialOwnerUpdateSimulationResponse = (BeneficialOwnerComparisonDTO);

export type ClientBeneficialOwnerUpdateSimulationError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementListBoDirsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        ConfirmedDateFrom?: string;
        ConfirmedDateTo?: string;
        DataStatuses?: Array<BoDirDataStatus>;
        PageNumber?: number;
        PageSize?: number;
        Position?: BoDirPosition;
        ProductionOffice?: ProductionOfficeType;
        SearchTerm?: string;
        SortBy?: 'LegalEntityName' | 'Position' | 'DirectorName' | 'ProductionOffice' | 'ReferralOffice' | 'Specifics' | 'Status' | 'RequestUpdateDate' | 'ConfirmedDate' | 'VPEntityNumber' | 'EntityPortalCode' | 'MasterClientCode' | 'DirectorVPCode' | 'DirectorType' | 'OfficerType';
        SortOrder?: string;
        Specifics?: Array<BoDirSpecifics>;
    };
};

export type ManagementListBoDirsResponse = (BoDirItemDTOPaginatedResponse);

export type ManagementListBoDirsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementDownloadBoDirListData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        ConfirmedDateFrom?: string;
        ConfirmedDateTo?: string;
        DataStatuses?: Array<BoDirDataStatus>;
        PageNumber?: number;
        PageSize?: number;
        Position?: BoDirPosition;
        ProductionOffice?: ProductionOfficeType;
        SearchTerm?: string;
        SortBy?: 'LegalEntityName' | 'Position' | 'DirectorName' | 'ProductionOffice' | 'ReferralOffice' | 'Specifics' | 'Status' | 'RequestUpdateDate' | 'ConfirmedDate' | 'VPEntityNumber' | 'EntityPortalCode' | 'MasterClientCode' | 'DirectorVPCode' | 'DirectorType' | 'OfficerType';
        SortOrder?: string;
        Specifics?: Array<BoDirSpecifics>;
    };
};

export type ManagementDownloadBoDirListResponse = ((Blob | File));

export type ManagementDownloadBoDirListError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetCompaniesData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * Optional flag to filter active companies.
         */
        isActive?: boolean;
        /**
         * Optional id of the masterclient.
         */
        masterClientId?: string;
        /**
         * Optional onboarding status to filter companies (multiple statuses possible).
         */
        onboardingStatus?: Array<OnboardingStatus>;
        /**
         * The page number to retrieve. Default is the first page.
         */
        pageNumber?: number;
        /**
         * The number of items per page. Default is the specified page size.
         */
        pageSize?: number;
        /**
         * Optional search term to filter companies.
         */
        searchTerm?: string;
        /**
         * The field to sort on.
         */
        sortBy?: string;
        /**
         * The order to sort on.
         */
        sortOrder?: string;
    };
};

export type GetCompaniesResponse = (CompanyDTOPaginatedResponse);

export type GetCompaniesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetCompanyModulesData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company to get the modules for.
         */
        companyId: string;
    };
};

export type GetCompanyModulesResponse = (ListCompanyModulesDTO);

export type GetCompanyModulesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetCompanyModulesData = {
    /**
     * The model with modules to set.
     */
    body?: SetCompanyModulesDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company to set the modules for.
         */
        companyId: string;
    };
};

export type SetCompanyModulesResponse = (ListCompanyModulesDTO);

export type SetCompanyModulesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetCompanySettingsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the company.
         */
        companyId: string;
    };
};

export type GetCompanySettingsResponse = (SettingsDTO);

export type GetCompanySettingsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetCompanySettingsData = {
    /**
     * The settings.
     */
    body?: SettingsDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the company.
         */
        companyId: string;
    };
};

export type SetCompanySettingsResponse = (void);

export type SetCompanySettingsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetCompanySettingsByKeyData = {
    /**
     * The payload for the settings.
     */
    body?: unknown;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the company.
         */
        companyId: string;
        /**
         * The key to identify the type of settings.
         */
        key: string;
    };
};

export type SetCompanySettingsByKeyResponse = (void);

export type SetCompanySettingsByKeyError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetCompanyAnnualFeesData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * The financial year for which to retrieve the annual fee statuses.
         */
        financialYear?: number;
        /**
         * Parameter to filter by paid status. True for paid, false for unpaid.
         */
        isPaid?: boolean;
        /**
         * The page number to retrieve. Default is the first page.
         */
        pageNumber?: number;
        /**
         * The number of items per page. Default is the specified page size.
         */
        pageSize?: number;
        /**
         * Optional search term to filter the results.
         */
        searchTerm?: string;
    };
};

export type GetCompanyAnnualFeesResponse = (CompanyWithAnnualFeeStatusSearchResultDTOPaginatedResponse);

export type GetCompanyAnnualFeesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetCompanyAnnualFeeStatusData = {
    /**
     * The DTO containing the update information.
     */
    body?: UpdateCompanyAnnualFeeStatusDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type SetCompanyAnnualFeeStatusResponse = (void);

export type SetCompanyAnnualFeeStatusError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetCompanyAnnualFeeStatusByCompanyData = {
    /**
     * The DTO containing the update information.
     */
    body?: SetCompanyAnnualFeesDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the compnay to set the annual fees for.
         */
        companyId: string;
    };
};

export type SetCompanyAnnualFeeStatusByCompanyResponse = (CompanyAnnualFeesDTO);

export type SetCompanyAnnualFeeStatusByCompanyError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetCompanyAnnualFeeStatusByCompanyData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the compnay to get the annual fees for.
         */
        companyId: string;
    };
};

export type GetCompanyAnnualFeeStatusByCompanyResponse = (CompanyAnnualFeesDTO);

export type GetCompanyAnnualFeeStatusByCompanyError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SendCompanyEmailMessageData = {
    /**
     * The DTO holding the parameters for the new email message.
     */
    body?: SendEmailDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company thet the email is related to.
         */
        companyId: string;
    };
};

export type SendCompanyEmailMessageResponse = (void);

export type SendCompanyEmailMessageError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetCompanyByIdData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the company to retrieve.
         */
        companyId: string;
    };
};

export type GetCompanyByIdResponse = (CompanyDTO);

export type GetCompanyByIdError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ApproveCompanyData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the company to approve.
         */
        companyId: string;
    };
};

export type ApproveCompanyResponse = (void);

export type ApproveCompanyError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type DeclineCompanyData = {
    /**
     * The DTO containing the decline reason.
     */
    body?: DeclineCompanyDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the company to decline.
         */
        companyId: string;
    };
};

export type DeclineCompanyResponse = (void);

export type DeclineCompanyError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetCompanyModulesData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company to get the modules for.
         */
        companyId: string;
    };
};

export type ClientGetCompanyModulesResponse = (ListModulesDTO);

export type ClientGetCompanyModulesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetCompanyBeneficialOwnersData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company to get the BOs for.
         */
        companyId: string;
    };
};

export type ClientGetCompanyBeneficialOwnersResponse = (ListBeneficialOwnersDTO);

export type ClientGetCompanyBeneficialOwnersError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetCompanyDirectorsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company to get the Directors for.
         */
        companyId: string;
    };
};

export type ClientGetCompanyDirectorsResponse = (ListDirectorsDTO);

export type ClientGetCompanyDirectorsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientRequestAssistanceData = {
    /**
     * The request holding the parameters.
     */
    body?: RequestAssistanceDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company to create an assistance request for.
         */
        companyId: string;
    };
};

export type ClientRequestAssistanceResponse = (void);

export type ClientRequestAssistanceError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientCreateSubmissionData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company to create the submission for.
         */
        companyId: string;
        /**
         * The id of the module to create the submission for.
         */
        moduleId: string;
    };
    query?: {
        /**
         * The year to create the submission for. Only for Nevis submissions.
         */
        year?: number;
    };
};

export type ClientCreateSubmissionResponse = (SubmissionDTO);

export type ClientCreateSubmissionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetCompanyModuleSubmissionsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company to get the submissions for.
         */
        companyId: string;
        /**
         * The id of the module to get the submissions for.
         */
        moduleId: string;
    };
    query?: {
        PageNumber?: number;
        PageSize?: number;
        SortBy?: string;
        SortOrder?: string;
    };
};

export type ClientGetCompanyModuleSubmissionsResponse = (ListSubmissionDTOPaginatedResponse);

export type ClientGetCompanyModuleSubmissionsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetAvailableSubmissionYearsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the company to create the submission for.
         */
        companyId: string;
        /**
         * The id of the module to create the submission for.
         */
        moduleId: string;
    };
};

export type ClientGetAvailableSubmissionYearsResponse = (AvailableSubmissionYearsDTO);

export type ClientGetAvailableSubmissionYearsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetAllCurrenciesData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * The name of the currency to filter by. Optional.
         */
        name?: string;
        /**
         * The page number to retrieve. Default is the first page.
         */
        pageNumber?: number;
        /**
         * The number of items per page. Default is the specified page size.
         */
        pageSize?: number;
    };
};

export type GetAllCurrenciesResponse = (CurrencyDTOPaginatedResponse);

export type GetAllCurrenciesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type CreateCurrencyData = {
    /**
     * The data transfer object containing the currency details.
     */
    body?: CreateCurrencyDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type CreateCurrencyResponse = (unknown);

export type CreateCurrencyError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type DeleteCurrencyData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the currency to be deleted.
         */
        id: string;
    };
};

export type DeleteCurrencyResponse = (void);

export type DeleteCurrencyError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetDirectorData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the director to get.
         */
        directorId: string;
    };
};

export type ManagementGetDirectorResponse = (DirectorDTO);

export type ManagementGetDirectorError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetDirectorForComparisonData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the director to get.
         */
        directorId: string;
    };
};

export type ManagementGetDirectorForComparisonResponse = (DirectorComparisonDTO);

export type ManagementGetDirectorForComparisonError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetDirectorData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the director to confirm.
         */
        relationId: string;
    };
};

export type ClientGetDirectorResponse = (DirectorDTO);

export type ClientGetDirectorError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetDirectorForComparisonData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the director to get.
         */
        relationId: string;
    };
};

export type ClientGetDirectorForComparisonResponse = (DirectorComparisonDTO);

export type ClientGetDirectorForComparisonError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientConfirmDirectorData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the director to confirm.
         */
        relationId: string;
    };
};

export type ClientConfirmDirectorResponse = (DirectorDTO);

export type ClientConfirmDirectorError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientRequestDirectorUpdateData = {
    /**
     * The model for the request for update.
     */
    body?: RequestUpdateDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the director to create an update request for.
         */
        relationId: string;
    };
};

export type ClientRequestDirectorUpdateResponse = (DirectorDTO);

export type ClientRequestDirectorUpdateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientDirecctorUpdateSimulationData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        relationId: string;
    };
    query?: {
        newName?: string;
    };
};

export type ClientDirecctorUpdateSimulationResponse = (DirectorComparisonDTO);

export type ClientDirecctorUpdateSimulationError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type CreateDocumentData = {
    body?: {
        File: (Blob | File);
        Type?: DocumentType;
        Description?: string;
    };
    headers?: {
        'x-userid'?: string;
    };
};

export type CreateDocumentResponse = (string);

export type CreateDocumentError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetApiV1CommonDocumentsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * A list of Guid containing the documents id.
         */
        documentIds?: Array<(string)>;
        /**
         * Determine if the data for the document needs to be included.
         */
        includeData?: boolean;
    };
};

export type GetApiV1CommonDocumentsResponse = (Array<DocumentDTO>);

export type GetApiV1CommonDocumentsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetApiV1CommonDocumentsByDocumentIdData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The document id as Guid.
         */
        documentId: string;
    };
    query?: {
        /**
         * Determine if the data for the document needs to be included.
         */
        includeData?: boolean;
    };
};

export type GetApiV1CommonDocumentsByDocumentIdResponse = (DocumentDTO);

export type GetApiV1CommonDocumentsByDocumentIdError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetFormTemplatesData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * Id of the jurisdiction to get the templates for.
         */
        jurisdictionId?: string;
        /**
         * Optional id of the module to get the templates for.
         */
        moduleId?: string;
    };
};

export type GetFormTemplatesResponse = (ListFormTemplatesDTO);

export type GetFormTemplatesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetFormTemplateData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the template to get.
         */
        templateId: string;
    };
};

export type GetFormTemplateResponse = (FormTemplateDTO);

export type GetFormTemplateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type CreateFormTemplateVersionData = {
    /**
     * The model holding the data for the new version.
     */
    body?: CreateFormTemplateVersionDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the template to add the version to.
         */
        templateId: string;
    };
};

export type CreateFormTemplateVersionResponse = (FormTemplateWithVersionsDTO);

export type CreateFormTemplateVersionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type UpdateFormTemplateVersionData = {
    /**
     * The model holding the data for the new version.
     */
    body?: UpdateFormTemplateVersionDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the template that the version belongs to.
         */
        templateId: string;
        /**
         * The id of the version that is to be updated.
         */
        versionId: string;
    };
};

export type UpdateFormTemplateVersionResponse = (FormTemplateWithVersionsDTO);

export type UpdateFormTemplateVersionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetInboxInfoData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type GetInboxInfoResponse = (InboxInfoDTO);

export type GetInboxInfoError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetInboxMessagesData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * Optional flag to specify whether to select only the Unread or the Read messages.
         */
        isRead?: boolean;
        /**
         * The optional pageNumber for the messages.
         */
        pageNumber?: number;
        /**
         * The optional pageSize for the messages.
         */
        pageSize?: number;
    };
};

export type GetInboxMessagesResponse = (InboxMessageListItemDTOPaginatedResponse);

export type GetInboxMessagesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetInboxMessageData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the message to get.
         */
        messageId: string;
    };
};

export type GetInboxMessageResponse = (InboxMessageDTO);

export type GetInboxMessageError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type CreateMessageReadStatusData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the message to create a read status for.
         */
        messageId: string;
    };
};

export type CreateMessageReadStatusResponse = (unknown);

export type CreateMessageReadStatusError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type DeleteInboxReadStatusData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the message to delete the read status for.
         */
        messageId: string;
    };
};

export type DeleteInboxReadStatusResponse = (void);

export type DeleteInboxReadStatusError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetInvoiceByIdData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the invoice to retrieve.
         */
        id: string;
    };
};

export type ManagementGetInvoiceByIdResponse = (InvoiceDTO);

export type ManagementGetInvoiceByIdError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetInvoicesData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        CompanyId?: string;
        FinancialYear?: number;
        FromDate?: string;
        MasterClientId?: string;
        PageNumber?: number;
        PageSize?: number;
        SearchTerm?: string;
        SortBy?: string;
        SortOrder?: string;
        Status?: InvoicePaymentStatus;
        ToDate?: string;
        UserId?: string;
    };
};

export type ClientGetInvoicesResponse = (InvoiceDTOPaginatedResponse);

export type ClientGetInvoicesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetInvoiceByIdData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the invoice to retrieve.
         */
        id: string;
    };
};

export type ClientGetInvoiceByIdResponse = (InvoiceDTO);

export type ClientGetInvoiceByIdError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetJurisdictionsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * The page number to retrieve. Default is the first page.
         */
        pageNumber?: number;
        /**
         * The number of items per page. Default is the specified page size.
         */
        pageSize?: number;
    };
};

export type GetJurisdictionsResponse = (JurisdictionDTOPaginatedResponse);

export type GetJurisdictionsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetJurisdictionModulesData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the jurisdiction to get the modules for.
         */
        jurisdictionId: string;
    };
};

export type GetJurisdictionModulesResponse = (ListModulesDTO);

export type GetJurisdictionModulesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetJurisdictionModulesData = {
    /**
     * The model with modules to set.
     */
    body?: SetModulesDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the jurisdiction to set the modules for.
         */
        jurisdictionId: string;
    };
};

export type SetJurisdictionModulesResponse = (ListModulesDTO);

export type SetJurisdictionModulesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetJurisdictionTaxRatesData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the jurisdiction.
         */
        jurisdictionId: string;
    };
};

export type GetJurisdictionTaxRatesResponse = (JurisdictionTaxRateDTOPaginatedResponse);

export type GetJurisdictionTaxRatesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetJurisdictionSettingsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the jurisdiction.
         */
        jurisdictionId: string;
    };
};

export type GetJurisdictionSettingsResponse = (SettingsDTO);

export type GetJurisdictionSettingsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetJurisdictionSettingsData = {
    /**
     * The settings.
     */
    body?: SettingsDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the juridiction.
         */
        jurisdictionId: string;
    };
};

export type SetJurisdictionSettingsResponse = (void);

export type SetJurisdictionSettingsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetJurisdictionSettingsByKeyData = {
    /**
     * The
     */
    body?: unknown;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the jurisdiction.
         */
        jurisdictionId: string;
        /**
         * The type of settings to set.
         */
        key: string;
    };
};

export type SetJurisdictionSettingsByKeyResponse = (void);

export type SetJurisdictionSettingsByKeyError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetJurisdictionFormTemplatesData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the jurisdiction to get the form templates for.
         */
        jurisdictionId: string;
    };
};

export type GetJurisdictionFormTemplatesResponse = (ListFormTemplatesDTO);

export type GetJurisdictionFormTemplatesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetTaxRatesByJurisdictionData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * The ID of the jurisdiction.
         */
        jurisdictionId?: string;
        /**
         * The page number to retrieve. Default is the first page.
         */
        pageNumber?: number;
        /**
         * The number of items per page. Default is the specified page size.
         */
        pageSize?: number;
    };
};

export type GetTaxRatesByJurisdictionResponse = (JurisdictionTaxRateDTOPaginatedResponse);

export type GetTaxRatesByJurisdictionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type CreateOrUpdateTaxRateData = {
    /**
     * The data transfer object containing the tax rate details.
     */
    body?: CreateOrUpdateTaxRateDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type CreateOrUpdateTaxRateResponse = (unknown);

export type CreateOrUpdateTaxRateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type DeleteTaxRateData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the tax rate to be deleted.
         */
        jurisdictionTaxRateId: string;
    };
};

export type DeleteTaxRateResponse = (void);

export type DeleteTaxRateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetMasterClientData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the masterclient to get.
         */
        masterclientId: string;
    };
};

export type GetMasterClientResponse = (MasterClientDTO);

export type GetMasterClientError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetMasterClientsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * Optional id of the jurisdiction.
         */
        jurisdictionId?: string;
        /**
         * The page number to retrieve. Default is the first page.
         */
        pageNumber?: number;
        /**
         * The number of items per page. Default is the specified page size.
         */
        pageSize?: number;
        /**
         * Optional search term which searches in Code and owner email addresses.
         */
        searchTerm?: string;
    };
};

export type GetMasterClientsResponse = (MasterClientDTOPaginatedResponse);

export type GetMasterClientsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetMasterClientCompaniesData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the master client.
         */
        masterclientId: string;
    };
    query?: {
        /**
         * The page number to retrieve. Default is the first page.
         */
        pageNumber?: number;
        /**
         * The number of items per page. Default is the specified page size.
         */
        pageSize?: number;
        sortBy?: string;
        sortOrder?: string;
    };
};

export type GetMasterClientCompaniesResponse = (CompanyDTOPaginatedResponse);

export type GetMasterClientCompaniesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetMasterClientUsersData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the master client.
         */
        masterclientId: string;
    };
    query?: {
        PageNumber?: number;
        PageSize?: number;
        SortBy?: string;
        SortOrder?: string;
    };
};

export type GetMasterClientUsersResponse = (ListUserDTOPaginatedResponse);

export type GetMasterClientUsersError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type AddUserToMasterClientData = {
    /**
     * The DTO containing the user email address.
     */
    body?: CreateMasterClientUserDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the masterclient to add the user to.
         */
        masterclientId: string;
    };
};

export type AddUserToMasterClientResponse = (MasterClientUserDTO);

export type AddUserToMasterClientError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetMasterClientSettingsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the master client.
         */
        masterclientId: string;
    };
    query?: {
        /**
         * The key to identify the type of settings.
         */
        key?: string;
    };
};

export type GetMasterClientSettingsResponse = (unknown);

export type GetMasterClientSettingsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetMasterClientSettingsData = {
    /**
     * The data to save as settings.
     */
    body?: unknown;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the master client.
         */
        masterclientId: string;
    };
    query?: {
        /**
         * The key to identify the type of settings.
         */
        key?: string;
    };
};

export type SetMasterClientSettingsResponse = (void);

export type SetMasterClientSettingsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type RemoveUserFromMasterClientData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the masterclient to remove the user from.
         */
        masterclientId: string;
        /**
         * Id of the user to remove from the masterclient.
         */
        userId: string;
    };
};

export type RemoveUserFromMasterClientResponse = (unknown);

export type RemoveUserFromMasterClientError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type PostApiV1ImportsMasterclientsData = {
    body?: {
        excelFile?: (Blob | File);
    };
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * Id of the jurisdiction to assign the master clients to.
         */
        jurisdictionId?: string;
    };
};

export type ClientGetMasterClientsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * Term to use for searching the master clients.
         */
        search?: string;
    };
};

export type ClientGetMasterClientsResponse = (MasterClientsSearchResultsDTO);

export type ClientGetMasterClientsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetMasterClientCompaniesData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the masterclient to search the companies for.
         */
        masterClientId: string;
    };
    query?: {
        /**
         * Optional filter to select only active or inactive companies.
         */
        active?: boolean;
        /**
         * Term to use for searching the companies.
         */
        search?: string;
    };
};

export type ClientGetMasterClientCompaniesResponse = (CompaniesSearchResultsDTO);

export type ClientGetMasterClientCompaniesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetMasterClientSubmissionsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the master client to get the submissions for.
         */
        masterClientId: string;
    };
    query?: {
        FinancialYears?: Array<(number)>;
        HasInvoice?: boolean;
        IsPaid?: boolean;
        LegalEntityIds?: Array<(string)>;
        PageNumber?: number;
        PageSize?: number;
        SubmissionStatuses?: Array<SubmissionStatus>;
    };
};

export type ClientGetMasterClientSubmissionsResponse = (ListSubmissionDTOPaginatedResponse);

export type ClientGetMasterClientSubmissionsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SendEmailMessageData = {
    /**
     * The DTO holding the parameters for the new email message.
     */
    body?: SendEmailDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type SendEmailMessageResponse = (void);

export type SendEmailMessageError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetApiV1ToolsMfaMfaInfoAuthenticatorData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type GetApiV1ToolsMfaMfaInfoAuthenticatorResponse = (unknown);

export type GetApiV1ToolsMfaMfaInfoAuthenticatorError = unknown;

export type GetApiV1ToolsMfaMfaInfoEmailData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type GetApiV1ToolsMfaMfaInfoEmailResponse = (unknown);

export type GetApiV1ToolsMfaMfaInfoEmailError = unknown;

export type GetApiV1ToolsMfaMfaVerificationData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * The verification code to check.
         */
        code?: string;
    };
};

export type GetApiV1ToolsMfaMfaVerificationResponse = (unknown);

export type GetApiV1ToolsMfaMfaVerificationError = unknown;

export type GetMfaMethodData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user to the mfa method for.
         */
        userId: string;
    };
};

export type GetMfaMethodResponse = (GetUserMFAMethodDTO);

export type GetMfaMethodError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type PutMfaMethodData = {
    /**
     * The model with info for setting the method.
     */
    body?: SetUserMFAMethodDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user to set the method for.
         */
        userId: string;
    };
};

export type PutMfaMethodResponse = (MFAInfoDTO);

export type PutMfaMethodError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type PatchMfaMethodData = {
    /**
     * The model with info for setting the method.
     */
    body?: SetUserMFAMethodDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user to set the method for.
         */
        userId: string;
    };
};

export type PatchMfaMethodResponse = (MFAInfoDTO);

export type PatchMfaMethodError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ResetMfaMethodData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user to reset the MFA for.
         */
        userId: string;
    };
};

export type ResetMfaMethodResponse = (void);

export type ResetMfaMethodError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type RequestMfaResetData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user to create an MFA reset request for.
         */
        userId: string;
    };
};

export type RequestMfaResetResponse = (RequestMFAResetResultDTO);

export type RequestMfaResetError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ConfirmMfaResetData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user to create an MFA reset request for.
         */
        userId: string;
    };
    query?: {
        /**
         * The code that the user entered for the MFA reset confirmation.
         */
        code?: string;
    };
};

export type ConfirmMfaResetResponse = (ConfirmMFAResetResultDTO);

export type ConfirmMfaResetError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SendMfaEmailData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user to send an email with the code to.
         */
        userId: string;
    };
};

export type SendMfaEmailResponse = (MFAInfoDTO);

export type SendMfaEmailError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type VerifyMfaCodeData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user to send an email with the code to.
         */
        userId: string;
    };
    query?: {
        /**
         * The code that the user entered for the MFA verification.
         */
        code?: string;
    };
};

export type VerifyMfaCodeResponse = (VerifyMFACodeResultDTO);

export type VerifyMfaCodeError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type StartMigrationData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type StartMigrationResponse = (string);

export type StartMigrationError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetMigrationStatusData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type GetMigrationStatusResponse = (DataMigrationDTO);

export type GetMigrationStatusError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type StopMigrationData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type StopMigrationResponse = (string);

export type StopMigrationError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type PostMigrationCleanupData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type PostMigrationCleanupResponse = (string);

export type PostMigrationCleanupError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetMigrationLogWorkbookData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type GetMigrationLogWorkbookResponse = ((Blob | File));

export type GetMigrationLogWorkbookError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetModulesData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * Indicates whether to get the active (true), inactive (false) or all (omit) modules.
         */
        active?: boolean;
    };
};

export type GetModulesResponse = (ListModulesDTO);

export type GetModulesError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetAllSubmissionYearsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the module to get the allowed years for.
         */
        moduleId: string;
    };
};

export type ManagementGetAllSubmissionYearsResponse = (AllSubmissionYearsDTO);

export type ManagementGetAllSubmissionYearsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type PostApiV1ExternalidOnAttributeCollectionStartData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type PostApiV1ExternalidOnAttributeCollectionStartResponse = (AttributeCollectionStartResponse);

export type PostApiV1ExternalidOnAttributeCollectionStartError = unknown;

export type PostOnattributecollectionstartData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        'api-version'?: string;
    };
};

export type PostOnattributecollectionstartResponse = (AttributeCollectionStartResponse);

export type PostOnattributecollectionstartError = unknown;

export type PostApiV1ExternalidOnAttributeCollectionSubmitData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type PostApiV1ExternalidOnAttributeCollectionSubmitResponse = (AttributeCollectionSubmitResponse);

export type PostApiV1ExternalidOnAttributeCollectionSubmitError = unknown;

export type PostOnattributecollectionsubmitData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        'api-version'?: string;
    };
};

export type PostOnattributecollectionsubmitResponse = (AttributeCollectionSubmitResponse);

export type PostOnattributecollectionsubmitError = unknown;

export type ClientGetPaymentsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        CompanyId?: string;
        FinancialYear?: number;
        FromDate?: string;
        PageNumber?: number;
        PageSize?: number;
        SearchTerm?: string;
        SortBy?: string;
        SortOrder?: string;
        Status?: PaymentStatus;
        ToDate?: string;
    };
};

export type ClientGetPaymentsResponse = (PaymentDTOPaginatedResponse);

export type ClientGetPaymentsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientCreatePaymentData = {
    /**
     * The request object containing payment details such as legal entity ID, currency ID, and associated invoice IDs.
     */
    body?: CreatePaymentRequestDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type ClientCreatePaymentResponse = (PaymentDTO | unknown);

export type ClientCreatePaymentError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetPaymentData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique identifier of the payment.
         */
        id: string;
    };
};

export type ClientGetPaymentResponse = (PaymentDetailsResponseDTO);

export type ClientGetPaymentError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientCancelPaymentData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique identifier of the payment.
         */
        paymentId: string;
    };
};

export type ClientCancelPaymentResponse = (void);

export type ClientCancelPaymentError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetAllPermissionsData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type GetAllPermissionsResponse = (Array<PermissionDTO>);

export type GetAllPermissionsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetPermissionsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * The optional unique identifier jurisdiction.
         */
        jurisdictionId?: string;
    };
};

export type ClientGetPermissionsResponse = (Array<UserPermissionDTO>);

export type ClientGetPermissionsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetReportsByTypeData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        PageNumber?: number;
        PageSize?: number;
        ReportTypes?: Array<ReportType>;
        SearchTerm?: string;
        SortBy?: string;
        SortOrder?: string;
    };
};

export type ManagementGetReportsByTypeResponse = (ReportDTOPaginatedResponse);

export type ManagementGetReportsByTypeError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementDownloadReportData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * A unique identifier (GUID) for the report to download. This is specified in the route.
         */
        reportId: string;
    };
};

export type ManagementDownloadReportResponse = ((Blob | File));

export type ManagementDownloadReportError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementListRfiSubmissionsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        CompanyIncorporatedAfterDate?: string;
        CompanyIncorporatedBeforeDate?: string;
        LegalEntityId?: string;
        ModuleId?: string;
        'PagingInfo.HasNext'?: boolean;
        'PagingInfo.HasPrevious'?: boolean;
        'PagingInfo.PageCount'?: number;
        'PagingInfo.PageNumber'?: number;
        'PagingInfo.PageSize'?: number;
        'PagingInfo.TotalItemCount'?: number;
        'SortingInfo.SortBy'?: string;
        'SortingInfo.SortOrder'?: string;
        SubmittedAfterDate?: string;
        SubmittedBeforeDate?: string;
    };
};

export type ManagementListRfiSubmissionsResponse = (ListSubmissionRFIDTOPaginatedResponse);

export type ManagementListRfiSubmissionsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetSubmissionRfiDetailsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the submission to get.
         */
        submissionId: string;
    };
};

export type ManagementGetSubmissionRfiDetailsResponse = (SubmissionRFIDetailsDTO);

export type ManagementGetSubmissionRfiDetailsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementRfiCreateData = {
    /**
     * The necessary data used to create a request for information.
     */
    body?: CreateRFIDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementRfiCreateResponse = (string);

export type ManagementRfiCreateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementRfiDocumentCreateData = {
    body?: {
        UploadComplete?: boolean;
        File: (Blob | File);
        Type?: DocumentType;
        Description?: string;
    };
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the request for information as Guid.
         */
        requestForInformationId: string;
    };
};

export type ManagementRfiDocumentCreateResponse = (string);

export type ManagementRfiDocumentCreateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementCancelRfiData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the request for information to delete as Guid.
         */
        requestForInformationId: string;
    };
};

export type ManagementCancelRfiResponse = (unknown);

export type ManagementCancelRfiError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetSubmissionRfiDetailsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * The id of the submission to get.
         */
        submissionId?: string;
    };
};

export type ClientGetSubmissionRfiDetailsResponse = (SubmissionRFIDetailsDTO);

export type ClientGetSubmissionRfiDetailsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientRfiCcompleteData = {
    /**
     * The necessary data used to complete a request for information.
     */
    body?: CompleteRequestForInformationDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the request for information as Guid.
         */
        requestForInformationId: string;
    };
};

export type ClientRfiCcompleteResponse = (unknown);

export type ClientRfiCcompleteError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientRfiDocumentCreateData = {
    body?: {
        UploadComplete?: boolean;
        File: (Blob | File);
        Type?: DocumentType;
        Description?: string;
    };
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the request for information as Guid.
         */
        requestForInformationId: string;
    };
};

export type ClientRfiDocumentCreateResponse = (string);

export type ClientRfiDocumentCreateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetShareholderData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the Shareholder to confirm.
         */
        relationId: string;
    };
};

export type ClientGetShareholderResponse = (ShareholderDTO);

export type ClientGetShareholderError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientConfirmShareholderData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the Shareholder to confirm.
         */
        relationId: string;
    };
};

export type ClientConfirmShareholderResponse = (ShareholderDTO);

export type ClientConfirmShareholderError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientRequestShareholderUpdateData = {
    /**
     * The model for the request for update.
     */
    body?: RequestUpdateDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique relationId of the Shareholder to create an update request for.
         */
        relationId: string;
    };
};

export type ClientRequestShareholderUpdateResponse = (ShareholderDTO);

export type ClientRequestShareholderUpdateError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetViewPointSyncStatusData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementGetViewPointSyncStatusResponse = (ViewPointSyncStatusDTO);

export type ManagementGetViewPointSyncStatusError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetAppVersionData = {
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementGetAppVersionResponse = (AppVersionDTO);

export type ManagementGetAppVersionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementMarkAsPaidData = {
    /**
     * The request containing submission IDs and payment status.
     */
    body?: MarkSubmissionsAsPaidRequestDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementMarkAsPaidResponse = (MarkSubmissionsAsPaidResponse);

export type ManagementMarkAsPaidError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementMarkAsPaidByCompanyAndYearData = {
    /**
     * The request containing company code and filing year pairs and payment status.
     */
    body?: MarkSubmissionsAsPaidByCompanyYearsRequestDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementMarkAsPaidByCompanyAndYearResponse = (MarkSubmissionsAsPaidByCompanyYearResponse);

export type ManagementMarkAsPaidByCompanyAndYearError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetPaidStatusByCompanyAndYearData = {
    /**
     * The request containing company code and filing year pairs.
     */
    body?: GetSubmissionsPaidStatusRequestDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementGetPaidStatusByCompanyAndYearResponse = (SubmissionsPaidStatusResponse);

export type ManagementGetPaidStatusByCompanyAndYearError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetSubmissionData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the submission to get.
         */
        submissionId: string;
    };
    query?: {
        /**
         * Denotes wether to include the form document in the result.
         */
        includeFormDocument?: boolean;
    };
};

export type ManagementGetSubmissionResponse = ((SubmissionDTO | SubmissionKeyValueDTO));

export type ManagementGetSubmissionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementReopenSubmissionData = {
    /**
     * The necessary data used to reopen a submission.
     */
    body?: ReopenSubmissionDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the submission to reopen.
         */
        submissionId: string;
    };
};

export type ManagementReopenSubmissionResponse = (SubmissionDTO);

export type ManagementReopenSubmissionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementListSubmissionsData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        Country?: string;
        FinancialYear?: number;
        GeneralSearchTerm?: string;
        IsDeleted?: boolean;
        IsExported?: boolean;
        IsPaid?: boolean;
        LegalEntitySearchTerm?: string;
        MasterClientSearchTerm?: string;
        ModuleId?: string;
        PageNumber?: number;
        PageSize?: number;
        ReferralOfficeSearchTerm?: string;
        SortBy?: 'LegalEntityName' | 'LegalEntityCode' | 'LegalEntityVPCode' | 'MasterClientCode' | 'Status' | 'FinancialYear' | 'CreatedAt' | 'ExportedAt' | 'SubmittedAt' | 'PaymentMethod' | 'PaymentReceivedAt' | 'PaymentReference';
        SortOrder?: string;
        SubmittedAfterDate?: string;
        SubmittedBeforeDate?: string;
    };
};

export type ManagementListSubmissionsResponse = (ListSubmissionDTOPaginatedResponse);

export type ManagementListSubmissionsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGenerateNevisSubmissionsReportData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        Country?: string;
        FinancialYear?: number;
        GeneralSearchTerm?: string;
        IsExported?: boolean;
        IsPaid?: boolean;
        LegalEntitySearchTerm?: string;
        MasterClientSearchTerm?: string;
        ModuleId?: string;
        ReferralOfficeSearchTerm?: string;
        SubmittedAfterDate?: string;
        SubmittedBeforeDate?: string;
    };
};

export type ManagementGenerateNevisSubmissionsReportResponse = ((Blob | File));

export type ManagementGenerateNevisSubmissionsReportError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementExportSubmissionData = {
    /**
     * The request containing the submission IDs to export.
     */
    body?: ExportSubmissionDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementExportSubmissionResponse = ((Blob | File));

export type ManagementExportSubmissionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementDownloadSubmissionDocumentsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The submission id as Guid.
         */
        submissionId: string;
    };
};

export type ManagementDownloadSubmissionDocumentsResponse = ((Blob | File));

export type ManagementDownloadSubmissionDocumentsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementDownloadSubmissionsDocumentsData = {
    /**
     * The submission ids as a list of Guid.
     */
    body?: Array<(string)>;
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementDownloadSubmissionsDocumentsResponse = ((Blob | File));

export type ManagementDownloadSubmissionsDocumentsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementUpdateSubmissionInformationData = {
    /**
     * The necessary data to update the submission general information.
     */
    body?: UpdateSubmissionInformationDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the submission to delete as Guid.
         */
        submissionId: string;
    };
};

export type ManagementUpdateSubmissionInformationResponse = (unknown);

export type ManagementUpdateSubmissionInformationError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementPanamaListSubmissionsByModuleData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        FinancialPeriodEndAt?: string;
        FinancialPeriodStartAt?: string;
        GeneralSearchTerm?: string;
        IsDeleted?: boolean;
        IsExported?: boolean;
        IsPaid?: boolean;
        IsUsingAccountingRecordsTool?: boolean;
        ModuleId?: string;
        PageNumber?: number;
        PageSize?: number;
        SortBy?: 'IsUsingAccountingRecordsTool' | 'LegalEntityName' | 'LegalEntityCode' | 'LegalEntityVPCode' | 'MasterClientCode' | 'Status' | 'FinancialPeriodStartsAt' | 'FinancialPeriodEndsAt' | 'CreatedAt' | 'PaymentMethod' | 'PaymentReceivedAt' | 'PaymentReference';
        SortOrder?: string;
        SubmittedAfterDate?: string;
        SubmittedBeforeDate?: string;
    };
};

export type ManagementPanamaListSubmissionsByModuleResponse = (ListSubmissionDTOPaginatedResponse);

export type ManagementPanamaListSubmissionsByModuleError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGenerateSubmissionsReportData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        FinancialPeriodEndAt?: string;
        FinancialPeriodStartAt?: string;
        GeneralSearchTerm?: string;
        IsPaid?: boolean;
        IsUsingAccountingRecordsTool?: boolean;
        ModuleId?: string;
        SubmittedAfterDate?: string;
        SubmittedBeforeDate?: string;
    };
};

export type ManagementGenerateSubmissionsReportResponse = ((Blob | File));

export type ManagementGenerateSubmissionsReportError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementExportSubmissionDataData = {
    /**
     * The necessary data to generate the submission data report.
     */
    body?: SubmissionDataRequestDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type ManagementExportSubmissionDataResponse = ((Blob | File));

export type ManagementExportSubmissionDataError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementBahamasListSubmissionsByModuleData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        AllowReopen?: boolean;
        CompanyIncorporatedAfterDate?: string;
        CompanyIncorporatedBeforeDate?: string;
        FinancialPeriodEndAt?: string;
        FinancialPeriodStartAt?: string;
        GeneralSearchTerm?: string;
        IsDeleted?: boolean;
        IsExported?: boolean;
        IsPaid?: boolean;
        ModuleId?: string;
        PageNumber?: number;
        PageSize?: number;
        PaidAfterDate?: string;
        PaidBeforeDate?: string;
        RelevantActivities?: Array<(string)>;
        ShowSubmitted?: boolean;
        SortBy?: 'LegalEntityName' | 'LegalEntityCode' | 'LegalEntityVPCode' | 'MasterClientCode' | 'Status' | 'FinancialPeriodStartsAt' | 'FinancialPeriodEndsAt' | 'CreatedAt' | 'PaymentMethod' | 'PaymentReceivedAt' | 'PaymentReference';
        SortOrder?: string;
        Status?: SubmissionStatus;
        SubmittedAfterDate?: string;
        SubmittedBeforeDate?: string;
    };
};

export type ManagementBahamasListSubmissionsByModuleResponse = (ListSubmissionBahamasDTOPaginatedResponse);

export type ManagementBahamasListSubmissionsByModuleError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementBahamasGenerateSubmissionsReportData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        AllowReopen?: boolean;
        CompanyIncorporatedAfterDate?: string;
        CompanyIncorporatedBeforeDate?: string;
        FinancialPeriodEndAt?: string;
        FinancialPeriodStartAt?: string;
        GeneralSearchTerm?: string;
        IsPaid?: boolean;
        ModuleId?: string;
        PaidAfterDate?: string;
        PaidBeforeDate?: string;
        RelevantActivities?: Array<(string)>;
        ShowSubmitted?: boolean;
        Status?: SubmissionStatus;
        SubmittedAfterDate?: string;
        SubmittedBeforeDate?: string;
    };
};

export type ManagementBahamasGenerateSubmissionsReportResponse = ((Blob | File));

export type ManagementBahamasGenerateSubmissionsReportError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientGetSubmissionData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the submission to get.
         */
        submissionId: string;
    };
    query?: {
        /**
         * Denotes wether to include the form document in the result.
         */
        includeFormDocument?: boolean;
    };
};

export type ClientGetSubmissionResponse = ((SubmissionDTO | SubmissionKeyValueDTO));

export type ClientGetSubmissionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientDeleteSubmissionData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the submission to delete as Guid.
         */
        submissionId: string;
    };
};

export type ClientDeleteSubmissionResponse = (unknown);

export type ClientDeleteSubmissionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientPutSubmissionDataSetData = {
    /**
     * The DTO with the dataset.
     */
    body?: SubmissionDataSetDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the submission to update.
         */
        submissionId: string;
    };
};

export type ClientPutSubmissionDataSetResponse = (SubmissionDataSetDTO);

export type ClientPutSubmissionDataSetError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientSubmitSubmissionData = {
    /**
     * The model with information about the finalizing of the submission.
     */
    body?: SubmitSubmissionDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the submission to finalize (submit).
         */
        submissionId: string;
    };
};

export type ClientSubmitSubmissionResponse = (SubmissionDTO);

export type ClientSubmitSubmissionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientUpdateSubmissionInformationData = {
    /**
     * The necessary data to update the submission general information.
     */
    body?: UpdateSubmissionInformationDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the submission to delete as Guid.
         */
        submissionId: string;
    };
};

export type ClientUpdateSubmissionInformationResponse = (unknown);

export type ClientUpdateSubmissionInformationError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientCreatePaymentTransactionData = {
    /**
     * The model containing details for creating the payment transaction, including customer information.
     */
    body?: CreateTransactionRequestDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique identifier for the payment.
         */
        paymentId: string;
    };
};

export type ClientCreatePaymentTransactionResponse = (CreateTransactionResponseDTO);

export type ClientCreatePaymentTransactionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ClientSubmitPaymentTransactionData = {
    /**
     * The model containing payment submission details such as the `TransactionId` (guid of the payment transaction) and the `TokenId` (token received from the payment provider).
     */
    body?: SubmitPaymentRequestDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        paymentId: string;
        transactionId: string;
    };
};

export type ClientSubmitPaymentTransactionResponse = (SubmitPaymentResponseDTO);

export type ClientSubmitPaymentTransactionError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SecuritySetUserSignedInData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        /**
         * The email address of the user.
         */
        email?: string;
        /**
         * The objectId of the user in ExternalId.
         */
        objectId?: string;
    };
};

export type SecuritySetUserSignedInResponse = (PCPApplicationUserDTO);

export type SecuritySetUserSignedInError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SecuritySetUserSignedOutData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the user.
         */
        userId: string;
    };
};

export type SecuritySetUserSignedOutResponse = (void);

export type SecuritySetUserSignedOutError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetPermissionsData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user to check the permissions for.
         */
        userId: string;
    };
    query?: {
        /**
         * The optional unique identifier jurisdiction.
         */
        jurisdictionId?: string;
    };
};

export type GetPermissionsResponse = (Array<UserPermissionDTO>);

export type GetPermissionsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetUsersData = {
    headers?: {
        'x-userid'?: string;
    };
    query?: {
        Filter?: string;
        PageNumber?: number;
        PageSize?: number;
        SortBy?: string;
        SortOrder?: string;
    };
};

export type ManagementGetUsersResponse = (PCPApplicationUserDTOPaginatedResponse);

export type ManagementGetUsersError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type CreateUserData = {
    /**
     * The model of the user to create.
     */
    body?: CreateUserDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type CreateUserResponse = (ApplicationUserDTO);

export type CreateUserError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type ManagementGetUserData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The unique identifier of the user.
         */
        id: string;
    };
};

export type ManagementGetUserResponse = (PCPApplicationUserDTO);

export type ManagementGetUserError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type BlockUnblockUserData = {
    /**
     * The model containing the block/unblock status.
     */
    body?: BlockUserDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The ID of the user to be blocked or unblocked.
         */
        userId: string;
    };
};

export type BlockUnblockUserResponse = (void);

export type BlockUnblockUserError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SetUserMasterClientsData = {
    /**
     * The model with the masterclients to set.
     */
    body?: UserMasterClientsDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the user.
         */
        userId: string;
    };
};

export type SetUserMasterClientsResponse = (void);

export type SetUserMasterClientsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SendUserInvitationData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * The id of the user to send the invitation to.
         */
        userId: string;
    };
    query?: {
        /**
         * The optional id of the masterclient to use for the code on the invitation.
         */
        masterClientId?: string;
    };
};

export type SendUserInvitationResponse = (void);

export type SendUserInvitationError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type SendUserInvitationsData = {
    /**
     * The DTO with the ids of the users.
     */
    body?: SendInvitationsDTO;
    headers?: {
        'x-userid'?: string;
    };
};

export type SendUserInvitationsResponse = (void);

export type SendUserInvitationsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type GetTermsConditionsStatusData = {
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * ID of the user to get the status for.
         */
        userId: string;
    };
};

export type GetTermsConditionsStatusResponse = (TermsConditionsStatusDTO);

export type GetTermsConditionsStatusError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);

export type AcceptTermsConditionsData = {
    /**
     * The acceptance model containing version information.
     */
    body?: AcceptTermsConditionsDTO;
    headers?: {
        'x-userid'?: string;
    };
    path: {
        /**
         * Id of the user accepting the terms.
         */
        userId: string;
    };
};

export type AcceptTermsConditionsResponse = (AcceptTermsConditionsResultDTO);

export type AcceptTermsConditionsError = ((ValidationProblemDetails | APIExceptionModel) | ProblemDetails | APIExceptionModel);