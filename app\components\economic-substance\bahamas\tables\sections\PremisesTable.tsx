import { <PERSON><PERSON>, <PERSON>rollA<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Pencil, X } from "lucide-react";
import type { JSX } from "react"
import type { PremiseSchemaType } from "~/lib/economic-substance/types/bahamas/premises-schema";

type Props = {
  premises: (PremiseSchemaType & { formArrayId: string })[]
  onSelect: (income: PremiseSchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function PremisesTable({
  premises,
  onSelect,
  onDelete,
  disabled,
}: Props): JSX.Element {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Address Line 1</TableHead>
              <TableHead>Address Line 2</TableHead>
              <TableHead>Country</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!premises.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No premises available
                </TableCell>
              </TableRow>
            )}
            {premises.length > 0 && premises.map((item, index) => (
              <TableRow key={item.formArrayId}>
                <TableCell>{item.addressLine1}</TableCell>
                <TableCell>{item.addressLine2}</TableCell>
                <TableCell>{item.country}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(item, index)} disabled={disabled}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
