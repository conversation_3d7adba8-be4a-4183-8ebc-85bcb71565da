import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import type { ReactNode } from "react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { requireValidPage, useCurrentStep } from "~/lib/economic-substance/hooks/use-form-steps";
import { getFormAction as getFormActionBahamas } from "~/lib/economic-substance/utilities/form-action-bahamas.server";
import { getFormLoader as getFormLoaderBahamas } from "~/lib/economic-substance/utilities/form-loader-bahamas.server";
import { getFormAction as getFormActionBVI } from "~/lib/economic-substance/utilities/form-action-bvi.server";
import { getFormLoader as getFormLoaderBVI } from "~/lib/economic-substance/utilities/form-loader-bvi.server";
import type { PageSlug } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import type { DocumentDTO, SubmissionKeyValueDTO } from "~/services/api-generated";
import { middleware } from "~/lib/middlewares.server";

const title = "Submission for" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Economic Substance",
  },
];

export const handle = {
  breadcrumb: (): ReactNode => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function action(actionArgs: ActionFunctionArgs): Promise<TypedResponse<null> | undefined> {
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireEsModule"], actionArgs.request);
  const { page } = requireValidPage(actionArgs.params, company.jurisdictionName);

  if (company.jurisdictionName === "BVI") {
    return getFormActionBVI(actionArgs, page);
  } else if (company.jurisdictionName === "Bahamas") {
    return getFormActionBahamas(actionArgs, page);
  } else {
    throw new Error("Jurisdiction not supported");
  }
}

export type EconomicSubstanceData = {
  submission: SubmissionKeyValueDTO
  page: PageSlug
  mappedDocuments: Record<string, DocumentDTO>
  jurisdictionName: string
}

export type EconomicSubstanceContainerLoader = TypedResponse<never> | EconomicSubstanceData

export async function loader(loaderArgs: LoaderFunctionArgs): Promise<EconomicSubstanceContainerLoader> {
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireEsModule"], loaderArgs.request);
  const { page } = requireValidPage(loaderArgs.params, company.jurisdictionName);
  if (company.jurisdictionName === "BVI") {
    return getFormLoaderBVI(loaderArgs, page);
  } else if (company.jurisdictionName === "Bahamas") {
    return getFormLoaderBahamas(loaderArgs, page);
  } else {
    throw new Error("Jurisdiction not supported");
  }
}

export default function EconomicSubstanceContainer(): ReactNode | never {
  const { page, jurisdictionName } = useLoaderData<typeof loader>();
  const currentStep = useCurrentStep(page, jurisdictionName);

  if (!currentStep) {
    throw new Error("Current step is not available");
  }

  return currentStep.component();
}
