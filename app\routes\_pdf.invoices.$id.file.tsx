import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { redirect, useLoaderData } from "@remix-run/react";
import type { FC, ReactNode } from "react";
import Page from "~/components/pages/Page";
import PageContent from "~/components/pages/PageContent";
import { Logo } from "~/components/ui/branding/Logo";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { getSessionData } from "~/lib/auth/utils/session.server";
import { middleware } from "~/lib/middlewares.server";
import { InvoiceStatus } from "~/lib/payments/utilities/invoice-status";
import { Layout } from "~/lib/simplified-tax-return/utilities/layouts";
import { formatDate } from "~/lib/utilities/format";
import { type InvoiceDTO, clientGetInvoiceById } from "~/services/api-generated";

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  invoice: InvoiceDTO
}> {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const { accessToken, userId } = await getSessionData(request);
  const { id } = params;

  if (!id) {
    throw new Error("Payment ID is required");
  }

  if (!accessToken || !userId) {
    return redirect("/login");
  }

  const { data: invoice, error } = await clientGetInvoiceById({
    headers: await authHeaders(request),
    path: {
      id,
    },
  });

  if (error) {
    console.error("Error fetching invoice:", error);
    throw new Response("Failed to fetch invoice", { status: 500 });
  }

  if (!invoice) {
    return redirect("/payments/pending");
  }

  return {
    invoice,
  };
}

const InvoiceAddress: FC<{ invoice: InvoiceDTO }> = ({ invoice }) => {
  return (
    <span>
      {invoice.companyName}
      <br />
      {invoice.address1}
      <br />
      {invoice.address2 && (
        <>
          {invoice.address2}
          <br />
        </>
      )}
      {invoice.addressZipCode}
      <br />
      {invoice.addressCity}
      {", "}
      {invoice.addressCountry}
    </span>
  )
}

export default function InvoiceFile(): ReactNode {
  const { invoice } = useLoaderData<typeof loader>();
  const fees = invoice.invoiceLines?.sort((a, b) => a.sequence! - b.sequence!) || [];

  return (
    <Page>
      <PageContent>
        <div className="font-arial text-[10pt]/[11.5pt]">
          <div className="grid grid-cols-3">
            <div className="col-span-2">
              <div>
                { invoice.layout === Layout.MorningStar
                  ? (
                      <img src="/images/nevis-morning-star-logo.png" className="w-[181.7pt]" />
                    )
                  : (
                      <Logo className="w-[181.7pt]" />
                    )}
              </div>
              <h1 className="tracking-widest text-blue-900 text-[17pt] mt-16">
                {`${invoice.status === InvoiceStatus.COMPLETED ? "PAID" : "DRAFT"} INVOICE`}
              </h1>
              <div className="grid grid-cols-9 pt-6">
                <div className="col-span-2 space-y-1">
                  <p className="text-blue-900">Date:</p>
                  <p className="text-blue-900">Invoice No:</p>
                  <p className="text-blue-900">To:</p>
                </div>
                <div className="col-span-7 space-y-1">
                  <p className="text-zinc-400">
                    {invoice.date
                      ? formatDate(invoice.date, {
                        timezone: "Nevis",
                      })
                      : "N/A"}
                  </p>
                  <p className="text-zinc-400">
                    {invoice.invoiceNr || "N/A"}
                  </p>
                  <p className="text-zinc-400"><InvoiceAddress invoice={invoice} /></p>
                </div>
              </div>
            </div>
            <div className="pl-12">
              <div className="text-blue-500 font-inter">TRIDENT TRUST COMPANY (NEVIS) LTD</div>
              <div className="text-blue-900">Hunkins Waterfront Plaza</div>
              <div className="text-blue-900">Suite 556</div>
              <div className="text-blue-900">Charlestown</div>
              <div className="text-blue-900">Nevis, West Indies</div>
              <div className="text-blue-900 flex">
                <div className="text-blue-500 w-5">T</div>
                +1 869 469 1817
              </div>
              <div className="text-blue-900 flex">
                <div className="text-blue-500 w-5">E</div>
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
              <div className="text-blue-900 flex">
                <div className="text-blue-500 w-5">W</div>
                <a href="https://tridenttrust.com">tridenttrust.com</a>
              </div>
            </div>
          </div>
          <div className="mt-12">
            <div className="text-blue-500 tracking-wider">
              FEES
            </div>
            <div className="mt-2">
              <table className="w-full">
                <tbody>
                  {fees.map(fee => (
                    <tr key={fee.id}>
                      <td className="text-blue-900 py-1.5">
                        {fee.description}
                      </td>
                      <td className="text-blue-900 py-1.5 text-right w-24">{`$ ${fee.amount ? fee.amount.toFixed(2) : "0.00"}`}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          {/* NOTE: This section is commented out because it is not needed for the current invoice layout.
          It will be needed later */}
          {/* <div className="mt-5">
            <div className="text-blue-500 tracking-wider">
              DISBURSEMENTS
            </div>
            <div className="mt-2">
              <table className="w-full">
                <tbody>
                  <tr className="">
                    <td className="text-blue-900 py-1.5">xxxxxxxx</td>
                    <td className="text-blue-900 py-1.5 text-right w-24">{`$ ${Number(0).toFixed(2)}`}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div> */}
          <div className="flex justify-between mt-5">
            <div className="text-blue-500 font-semibold tracking-wider">
              TOTAL CHARGES
            </div>
            <div className="font-semibold text-blue-900">
              {`$ ${invoice.amount ? invoice.amount.toFixed(2) : "0.00"}`}
            </div>
          </div>
          <div className="py-10">
            <p>Payment Terms: Due on presentation</p>
          </div>
          <div className="mt-5">
            <div className="text-blue-500 tracking-wider">
              PAYMENT OPTIONS:
            </div>
            <div className="mt-3">
              <ol className="list-decimal pl-9 text-blue-900 text-[9pt]">
                <li className="pl-3 mb-7">
                  Mastercard and visa debit and credit cards via our payment portal:
                  <a className="ml-2" href="https://tridenttrust.com/nevis-payments">https://tridenttrust.com/nevis-payments</a>
                </li>
                {" "}
                <li className="pl-3 mb-7">
                  <p>ACH PAYMENT AND BANK WIRE TRANSFER TO:</p>
                  <p className="mt-4 underline">
                    DESTINATION / BENEFICIARY BANK
                  </p>
                  <p className="mt-3">
                    <span className="font-bold">Name and Address:</span>
                    {" "}
                    CIBC BANK USA, ADDRESS: 120 SOUTH LASALLE STREET, CHICAGO, IL 60603, USA
                  </p>
                  <p className="mt-2">
                    <span className="font-bold">SWIFT CODE:</span>
                    <span className="ml-2">PVTBUS44</span>
                    <span className="font-bold ml-11">ABA #:</span>
                    <span className="ml-2">***********</span>
                    <span></span>
                  </p>
                  <p className="mt-4 underline">
                    BENEFICIARY
                  </p>
                  <p className="mt-3">
                    <span className="font-bold">NAME:</span>
                    {" "}
                    TRIDENT TRUST COMPANY (NEVIS) LIMITED
                  </p>
                  <p className="mt-3">
                    <span className="font-bold">ACCOUNT:</span>
                    {" "}
                    2606275
                  </p>
                  <p className="mt-3">
                    <span className="font-bold">ADDRESS:</span>
                    {" "}
                    Hunkins Waterfront Plaza, Ste 556, Main St, Charlestown, Nevis, West Indies
                  </p>
                </li>
              </ol>
            </div>
          </div>
          <div className="flex justify-center text-blue-900">
            Checks are not accepted.
          </div>
        </div>
      </PageContent>
    </Page>
  );
}
