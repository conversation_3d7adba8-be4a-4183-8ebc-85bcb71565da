import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, DatePicker, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, RadioGroup, RadioGroupItem, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Textarea, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useLoaderData, useLocation, useNavigation, useParams } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { FileUploadDialog } from "../../dialogs/financial-period/FileUploadDialog";
import type { FinancialPeriodSchemaType } from "~/lib/basic-financial-report/types/financial-period-schema";
import { CompanyActivity, financialPeriodSchema } from "~/lib/basic-financial-report/types/financial-period-schema";
import { BASIC_FINANCIAL_REPORT_FORM_ID } from "~/lib/basic-financial-report/utilities/constants";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import type { BasicFinancialReportData } from "~/routes/_main._card.basic-financial-report.$id.$pageName";
import { convertMappedDocumentsToFileObject } from "~/lib/utilities/files";
import type { FileSchemaType } from "~/lib/basic-financial-report/types/file-schema";
import { fileSchema } from "~/lib/basic-financial-report/types/file-schema";
import { formatDate } from "~/lib/utilities/format";

export type FileFieldName = keyof Pick<FinancialPeriodSchemaType, "summaryReport" | "accountingRecords">;

export function FinancialPeriod(): ReactNode {
  const loader = useLoaderData<BasicFinancialReportData>()
  const documentsArray = convertMappedDocumentsToFileObject(loader.mappedDocuments)
  const { submissionData } = useSubmission();
  const { id } = useParams()
  const [open, setOpen] = useState(false);
  const location = useLocation()
  const [fieldName, setFieldName] = useState< FileFieldName | undefined>()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const data = useMemo(() => submissionData[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType & { summaryReportDocumentId: string, accountRecordsDocumentId: string }, [submissionData]);
  const form = useForm<FinancialPeriodSchemaType>({
    resolver: zodResolver(financialPeriodSchema),
    shouldFocusError: false,
    defaultValues: data || {
      firstFinancialReport: "true",
      startFiscalYear: undefined,
      endFiscalYear: undefined,
      companyActivity: undefined,
      tridentAccountingRecordsTool: undefined,
      otherCompanyActivity: "",
    },
  });
  const fileForm = useForm<FileSchemaType>({
    resolver: zodResolver(fileSchema),
    shouldFocusError: false,
  });
  const { formState, setValue } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    if (documentsArray) {
      setValue("summaryReport", documentsArray.summaryReportDocumentId ? { files: [documentsArray.summaryReportDocumentId] } : undefined)
      setValue("accountingRecords", documentsArray.accountingRecordsDocumentId ? { files: [documentsArray.accountingRecordsDocumentId] } : undefined)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setValue]);

  const fetcher = useFetcher();
  const fetcherCreateDocument = useFetcher<string>();

  function onSubmit(data: FinancialPeriodSchemaType): void {
    const processedData = data
    delete processedData.summaryReport
    delete processedData.accountingRecords
    fetcher.submit({ data: JSON.stringify(processedData) }, {
      method: "post",
    });
  }

  const handleCompanyActivityChange = (value: string) => {
    if (value === CompanyActivity.HOLDING_BUSINESS) {
      form.setValue("otherCompanyActivity", undefined)
    }
  }
  const handleTridentAccountingRecordsToolChange = (value: string) => {
    if (value === "false") {
      form.setValue("summaryReport", undefined)
      form.setValue("accountingRecords", undefined)
    }
  }

  function onSubmitFile(data: FileSchemaType): void {
    if (fieldName) {
      form.setValue(fieldName, data)
      const formData = new FormData()
      const fileData = data.files[0]
      formData.append("file", fileData)
      formData.append("location", location.pathname)
      fetcherCreateDocument.submit(formData, { action: "/document/create", encType: "multipart/form-data", method: "post" })
    }

    setOpen(false)
  }

  const onOpenDialog = (fieldName: FileFieldName): void => {
    setFieldName(fieldName)
    const files = form.getValues(fieldName)
    fileForm.reset(files, { keepDefaultValues: true })
    setOpen(true)
  }
  const watchCompanyActivity = form.watch("companyActivity")
  const watchTridentAccountingRecordsTool = form.watch("tridentAccountingRecordsTool")
  const watchSummaryReport = form.watch("summaryReport")
  const watchAccountingRecords = form.watch("accountingRecords")
  const startDate = form.watch("startFiscalYear")
  const endDate = form.watch("endFiscalYear")
  const dateRange = `${startDate ? formatDate(startDate) : "N/A"} to ${endDate ? formatDate(endDate) : "N/A"}`

  useEffect(() => {
    const documentId = fetcherCreateDocument.data
    if (documentId && fieldName) {
      const keyName = `${fieldName}DocumentId`
      fetcherCreateDocument.submit({ data: JSON.stringify({ [keyName]: documentId }), location: location.pathname }, { action: `/basic-financial-report/${id}/pages/${Pages.FINANCIAL_PERIOD}/documents/${documentId}/update`, method: "post" })
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcherCreateDocument.data])

  return (
    <>
      {fieldName && (
        <FileUploadDialog
          form={fileForm}
          open={open}
          setOpen={setOpen}
          onSubmit={onSubmitFile}
        />
      )}
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={BASIC_FINANCIAL_REPORT_FORM_ID} className="space-y-5">
          <span className="text-xs text-gray-500 my-auto">NOTE: You are required to file your basic financial report every year.</span>
          <div className="flex-col space-y-7">
            <FormField
              control={form.control}
              name="firstFinancialReport"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Is this the first set of financial summary/reports that will be prepared for the company?*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                      disabled={isSubmitting}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="space-y-2">
              <Tooltip delayDuration={0}>
                <FormLabel>
                  <p className="flex gap-1">
                    Please state the financial period of your fiscal year*
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                </FormLabel>
                <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                  <p>
                    Per Circular No.9 of 2021, the financial year is defined as the calendar year, unless it is changed by the legal person or legal arrangement.
                  </p>
                </TooltipContent>
              </Tooltip>
              <div className="flex flex-col gap-5 sm:flex-row">
                <FormField
                  control={form.control}
                  name="startFiscalYear"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>From *</FormLabel>
                      <FormControl className="md:w-1/2 sm:w-full">
                        <DatePicker
                          date={field.value && new Date(field.value)}
                          onChange={field.onChange}
                          invalid={!!fieldState.error}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="endFiscalYear"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>To *</FormLabel>
                      <FormControl className="md:w-1/2 sm:w-full">
                        <DatePicker
                          date={field.value && new Date(field.value)}
                          onChange={field.onChange}
                          invalid={!!fieldState.error}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <FormField
              control={form.control}
              name="companyActivity"
              render={({ field, fieldState }) => (
                <FormItem>
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        Please state the purpose of the company / the main activity of the company*
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      Securities Investment - tradeable financial assets such as equities or fixed income (bonds)
                    </TooltipContent>
                  </Tooltip>
                  <FormControl>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value)
                        handleCompanyActivityChange(value)
                      }}
                      value={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl className="md:w-1/2 sm:w-full">
                        <SelectTrigger invalid={!!fieldState.error} disabled={isSubmitting}>
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        { Object.values(CompanyActivity).map(activity => (
                          <SelectItem key={activity} value={activity}>
                            {activity}
                          </SelectItem>
                        )) }
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchCompanyActivity === CompanyActivity.OTHER && (
              <FormField
                control={form.control}
                name="otherCompanyActivity"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Please specify*</FormLabel>
                    <FormControl className="md:w-1/2 sm:w-full">
                      <Textarea
                        invalid={!!fieldState.error}
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <FormField
              control={form.control}
              name="tridentAccountingRecordsTool"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <Tooltip delayDuration={0}>
                      <FormLabel>
                        <p className="flex gap-1">
                          Would you like to use the Trident Accounting Records tool?*
                          <TooltipTrigger asChild>
                            <Info className="flex shrink-0 size-4" />
                          </TooltipTrigger>
                        </p>
                      </FormLabel>
                      <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                        <p>The Trident Accounting Records tool will assist you in preparing you Financial Summary/Reports.</p>
                      </TooltipContent>
                    </Tooltip>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value)
                        handleTridentAccountingRecordsToolChange(value)
                      }}
                      value={field.value}
                      invalid={!!fieldState.error}
                      disabled={isSubmitting}
                      className="flex flex-row space-x-2"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchTridentAccountingRecordsTool === "false" && (
              <>
                <FormField
                  control={form.control}
                  name="summaryReport"
                  render={() => (
                    <FormItem className="flex flex-col">
                      <Tooltip delayDuration={0}>
                        <FormLabel>
                          <p className="flex gap-1">
                            {`Please upload your Financial Summary/Reports for the fiscal year ${dateRange}*`}
                            <TooltipTrigger asChild>
                              <Info className="flex shrink-0 size-4" />
                            </TooltipTrigger>
                          </p>
                        </FormLabel>
                        <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                          <p>Financial Summary/Reports includes the Balance Sheet and Profit and Loss Statement.</p>
                          <p>The Financial Services Authority (FSA) will be providing further guidance as to the content of the Financial Summary.</p>
                        </TooltipContent>
                      </Tooltip>
                      <FormControl className="md:w-fit sm:w-full">
                        <Button size="sm" onClick={() => onOpenDialog("summaryReport")} type="button" disabled={isSubmitting}>
                          <Plus className="size-4 mr-2" />
                          {`${watchSummaryReport ? "File uploaded" : "  Upload file"}`}
                        </Button>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="accountingRecords"
                  render={() => (
                    <FormItem className="flex flex-col">
                      <Tooltip delayDuration={0}>
                        <FormLabel>
                          <p className="flex gap-1">
                            {`Please upload your Accounting Records for the fiscal year ${dateRange}*`}
                            <TooltipTrigger asChild>
                              <Info className="flex shrink-0 size-4" />
                            </TooltipTrigger>
                          </p>
                        </FormLabel>
                        <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                          <p>Accounting Records, in relation to a legal person or legal arrangement, is defined as documents in respect of the legal person's or legal arrangement's assets and liabilities, the receipts and expenditure, sales, purchases and other transactions to which the legal person and legal arrangment is a party to.</p>
                          <p>It implies that accounting records (including the underlying documents), can take on many forms and includes: (a) bank statements, (b) receipts, (c) invoices, (d) vouchers, (e) title documents, (f) contracts and agreements, (g) ledgers, and (h) any other documentation underpinning a transaction.</p>
                        </TooltipContent>
                      </Tooltip>
                      <FormControl className="md:w-fit sm:w-full">
                        <Button size="sm" onClick={() => onOpenDialog("accountingRecords")} type="button" disabled={isSubmitting}>
                          <Plus className="size-4 mr-2" />
                          {`${watchAccountingRecords ? "File uploaded" : "  Upload file"}`}
                        </Button>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <span className="text-xs text-primary my-auto">Kindly note that the default reporting currency in this portal is in US Dollars (USD).</span>
              </>
            )}

          </div>
        </RemixForm>
      </Form>
    </>
  )
}
