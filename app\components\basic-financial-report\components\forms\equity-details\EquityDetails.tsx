import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON>ton, Combobox, Dialog, Dialog<PERSON>ontent, <PERSON>alog<PERSON>ooter, DialogHeader, DialogTitle, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input, Label, RadioGroup, RadioGroupItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { PropertyDialog } from "../../dialogs/equity-details/PropertyDialog";
import { PropertyTable } from "../../tables/equity-details/PropertyTable";
import type { EquityDetailsSchemaType, PropertySchemaType } from "~/lib/basic-financial-report/types/equity-details-schema";
import { InitialPaymentType, equityDetailsSchema, propertySchema } from "~/lib/basic-financial-report/types/equity-details-schema";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { BASIC_FINANCIAL_REPORT_FORM_ID } from "~/lib/basic-financial-report/utilities/constants";
import { Currency } from "~/lib/basic-financial-report/utilities/currencies";

export function EquityDetails(): ReactNode {
  const { submissionData } = useSubmission();
  const fetcher = useFetcher()
  const data = useMemo(() => submissionData[Pages.EQUITY_DETAILS] as EquityDetailsSchemaType, [submissionData]);
  const [open, setOpen] = useState(false);
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [propertyIndex, setPropertyIndex] = useState<number | undefined>();
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const form = useForm<EquityDetailsSchemaType>({
    resolver: zodResolver(equityDetailsSchema),
    shouldFocusError: false,
    defaultValues: {
      noAuthorizedShares: "",
      noIssuedShares: "",
      parValuePerShare: "",
      initialPaymentType: [],
      paidInCash: "false",
      cashAmount: "",
      propertyTransfer: "false",
      properties: [],
      securitiesTransfer: "false",
      securitiesValue: "",
    },
  });
  const propertyForm = useForm<PropertySchemaType>({
    resolver: zodResolver(propertySchema),
    defaultValues: { type: "", otherType: "", value: "" },

  });
  const {
    fields,
    append,
    remove,
    update,
  } = useFieldArray({
    control: form.control,
    name: "properties",
    keyName: "formArrayId",
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  function addProperty(): void {
    propertyForm.reset();
    setPropertyIndex(undefined);
    setOpen(true);
  }

  function onSubmitProperty(data: PropertySchemaType): void {
    if (propertyIndex !== undefined) {
      update(propertyIndex, data);
    } else {
      append(data);
    }

    setOpen(false);
  }

  function onSelect(activity: PropertySchemaType, index: number): void {
    propertyForm.reset(activity, { keepDefaultValues: true });
    setPropertyIndex(index);
    setOpen(true);
  }

  function onDelete(): void {
    remove(propertyIndex);
    form.trigger();
    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(index: number): void {
    setPropertyIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setPropertyIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  function onSubmit(data: EquityDetailsSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const { initialPaymentType: watchInitialPaymentType, paidInCash: watchPaidInCash, propertyTransfer: watchPropertyTransfer, securitiesTransfer: watchSecuritiesTransfer } = form.watch()
  const initialPaymentTypeOptions = Object.values(InitialPaymentType).map(value => ({
    label: value,
    value,
  }));
  const handleInitialPaymentTypeChange = (value: string[] | undefined): void => {
    if (value?.includes(InitialPaymentType.CASH)) {
      form.setValue("paidInCash", "true")
    } else {
      form.setValue("paidInCash", "false")
      form.setValue("cashAmount", undefined)
    }

    if (value?.includes(InitialPaymentType.PROPERTY)) {
      form.setValue("propertyTransfer", "true")
    } else {
      form.setValue("propertyTransfer", "false")
      form.setValue("properties", [])
    }

    if (value?.includes(InitialPaymentType.SECURITY_INVESTMENT)) {
      form.setValue("securitiesTransfer", "true")
    } else {
      form.setValue("securitiesTransfer", "false")
      form.setValue("securitiesValue", undefined)
    }
  }
  const handlePaidInCashChange = (value: string): void => {
    const currentPaymentType = watchInitialPaymentType || [];
    if (value === "true") {
      form.setValue("initialPaymentType", [
        ...currentPaymentType,
        InitialPaymentType.CASH,
      ]);
    } else {
      form.setValue(
        "initialPaymentType",
        currentPaymentType.filter(type => type !== InitialPaymentType.CASH),
      );
      form.setValue("cashAmount", undefined)
    }
  }
  const handlePropertyTransferChange = (value: string): void => {
    const currentPaymentType = watchInitialPaymentType || [];
    if (value === "true") {
      form.setValue("initialPaymentType", [
        ...currentPaymentType,
        InitialPaymentType.PROPERTY,
      ]);
    } else {
      form.setValue(
        "initialPaymentType",
        currentPaymentType.filter(type => type !== InitialPaymentType.PROPERTY),
      );
      form.setValue(
        "properties",
        undefined,
      );
      remove()
    }
  }
  const handleSecuritiesTransferChange = (value: string): void => {
    const currentPaymentType = watchInitialPaymentType || [];
    if (value === "true") {
      form.setValue("initialPaymentType", [
        ...currentPaymentType,
        InitialPaymentType.SECURITY_INVESTMENT,
      ]);
    } else {
      form.setValue(
        "initialPaymentType",
        currentPaymentType.filter(type => type !== InitialPaymentType.SECURITY_INVESTMENT),
      );
      form.setValue(
        "securitiesValue",
        undefined,
      );
    }
  }

  return (
    <>
      <PropertyDialog
        setOpen={setOpen}
        open={open}
        form={propertyForm}
        onSubmit={onSubmitProperty}
      />
      <Dialog open={openDeletedConfirmation} onOpenChange={setOpenDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete the property?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation}>Cancel</Button>
            <Button type="button" variant="destructive" onClick={onDelete}>Yes, delete this property</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={BASIC_FINANCIAL_REPORT_FORM_ID} className="space-y-5">
          <Tooltip delayDuration={0}>
            <Label>
              <p className="flex gap-1">
                Equity
                <TooltipTrigger asChild>
                  <Info className="flex shrink-0 size-4" />
                </TooltipTrigger>
              </p>
            </Label>
            <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
              <p>
                The equity meaning in accounting refers to a company's book value, which is the difference between liabilities and assets on the balance sheet. This is also called the owner's equity, as it's the value that an owner of a business has left over after liabilities are deducted.
              </p>
            </TooltipContent>
          </Tooltip>
          <div className="flex-col space-y-7">
            <FormField
              control={form.control}
              name="noAuthorizedShares"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        What is the number of authorized shares?*
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      <p>
                        Authorized shares of a company is the MAXIMUM amount of shares that the Company is authorised to issue to its Shareholders.
                      </p>
                      <p>
                        This information may be found in the Memorandun and Articles of Association.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="noIssuedShares"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        What is the number of issued shares?*
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      <p>
                        Issued shares are the actual number of shares issued or subscribed by the Shareholders. Issued shares CANNOT exceed the authorized shares.
                      </p>
                      <p>
                        This may be found in the Register of Members or Share Register.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="parValuePerShare"
              render={({ field, fieldState }) => (
                <FormItem>
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        What is the par value per share in currency?*
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      <p>
                        This information may be found in the Memorandun and Articles of Association, together with the Authorized Shares.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <Input
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="initialPaymentType"
              render={({ field, fieldState }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>
                    How did you pay the initial capital investment for the issued shares?*
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <Combobox
                      placeholder="Select one or multiple "
                      searchText="Search..."
                      noResultsText="No results found."
                      multiple
                      items={initialPaymentTypeOptions}
                      onChange={(value) => {
                        field.onChange(value)
                        handleInitialPaymentTypeChange(value)
                      }}
                      value={field.value}
                      invalid={!!fieldState.error}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="paidInCash"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p className="flex gap-1">
                      Did you pay in Cash, as the incorporation of the Company? - To settle the value of the issued share capital*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value)
                        handlePaidInCashChange(value)
                      }}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                      disabled={isSubmitting}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchPaidInCash === "true" && (
              <FormField
                control={form.control}
                name="cashAmount"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Please indicate the cash amount assumed to have received by the Company in the bank.*</FormLabel>
                    <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                      <CurrencyInput
                        currencyName={Currency.USD}
                        invalid={!!fieldState.error}
                        {...field}
                        type="number"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <FormField
              control={form.control}
              name="propertyTransfer"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Did you set-up/transfer Property (e.g. Land, Building)? - To settle the value of the issued share capital*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value)
                        handlePropertyTransferChange(value)
                      }}
                      value={field.value}
                      invalid={!!fieldState.error}
                      disabled={isSubmitting}
                      className="flex flex-row space-x-2"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchPropertyTransfer === "true" && (
              <>
                <FormField
                  name="properties"
                  control={form.control}
                  render={({ fieldState }) => (
                    <FormItem>
                      <FormLabel>Please indicate the value of the transferred property*</FormLabel>
                      {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                      <FormControl>
                        <PropertyTable
                          properties={fields}
                          onSelect={onSelect}
                          onDelete={onOpenDeleteConfirmation}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Button size="sm" onClick={addProperty} type="button" disabled={isSubmitting}>
                    <Plus className="mr-2 size-4 text-white" />
                    Add New Property
                  </Button>
                </div>
              </>
            )}
            <FormField
              control={form.control}
              name="securitiesTransfer"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Did you set-up/transfer Securities Investments? - To settle the value of the issued share capital*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value)
                        handleSecuritiesTransferChange(value)
                      }}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                      disabled={isSubmitting}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchSecuritiesTransfer === "true" && (
              <FormField
                control={form.control}
                name="securitiesValue"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Please indicate the value of the Transferred Securities Investments - market value on the date of transfer*</FormLabel>
                    <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                      <CurrencyInput
                        currencyName={Currency.USD}
                        invalid={!!fieldState.error}
                        {...field}
                        type="number"
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>
        </RemixForm>
      </Form>
    </>
  )
}
