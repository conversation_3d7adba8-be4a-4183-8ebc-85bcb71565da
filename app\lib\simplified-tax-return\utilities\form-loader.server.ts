import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { getPreviousStep } from "../hooks/use-form-steps";
import type { Submission } from "~/features/submissions/api/get-submission";
import { getSubmission } from "~/features/submissions/api/get-submission";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { middleware } from "~/lib/middlewares.server";
import type { PageSlug } from "~/lib/simplified-tax-return/utilities/form-pages";

export async function getFormLoader({ request, params }: LoaderFunctionArgs, page: PageSlug): Promise<TypedResponse<never> | {
  submission: Submission
  page: PageSlug
}> {
  const { userId, accessToken } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireStrModule"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const submission = await getSubmission({
    id,
    accessToken,
    userId,
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found");
  }

  if (submission.statusText !== SubmissionStatusNames.Draft && submission.statusText !== SubmissionStatusNames.Revision) {
    session.flash("notification", { title: "Error!", message: "Submission was already submitted", variant: "error" });

    return redirect("/simplified-tax-return/new", { headers: { "Set-Cookie": await commitSession(session) } });
  }

  const submissionData = getUnflattenedDataSet(submission);
  // Validate previous step was filled in
  const previousPage = getPreviousStep(submissionData, submission.financialYear, page);
  if (previousPage && !submissionData[previousPage]) {
    // Redirect to the previous page
    return redirect(`/simplified-tax-return/${id}/${previousPage}`);
  }

  return { submission, page };
}
