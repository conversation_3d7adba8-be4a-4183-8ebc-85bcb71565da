import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";

export type BeneficialOwnerType = "INDIVIDUAL_BO" | "CORPORATE_BO";
export type DirectorType = "INDIVIDUAL_DIRECTOR" | "CORPORATE_DIRECTOR";

export const BeneficialOwnerColumns: Record<BeneficialOwnerType, {
  columns: Partial<Record<keyof BeneficialOwnerDTO, string>>
  detailedColumns: Partial<Record<keyof BeneficialOwnerDTO, string>>
}> = {
  INDIVIDUAL_BO: { // Visible columns for the normal view
    columns: {
      name: "Full Name",
      countryOfBirth: "Country of Birth",
      dateOfBirth: "Date of Birth",
    },
    detailedColumns: { // Visible columns for the detailed view
      name: "Full Name",
      officerTypeName: "Type of Owner",
      dateOfBirth: "Date of Birth",
      countryOfBirth: "Country of Birth",
      nationality: "Nationality",
      tin: "TIN",
      residentialAddress: "Residential Address",
    },
  },
  CORPORATE_BO: { // Visible columns for the normal view
    columns: {
      name: "Corporation Name",
      countryOfFormation: "Country of Formation",
      incorporationNumber: "Incorporation Number",
    },
    detailedColumns: {
      officerTypeName: "Type",
      name: "Corporation Name",
      incorporationNumber: "Incorporation Number",
      dateOfIncorporation: "Incorporation Date",
      address: "Address",
      countryOfFormation: "Country of Formation",
      jurisdictionOfRegulator: "Jurisdiction of Regulator",
      nameOfRegulator: "Name of Regulator",
      sovereignState: "Sovereign State",
      tin: "TIN",
      stockCode: "Stock Code",
      stockExchange: "Stock Exchange",
    },
  },
};

export const DirectorColumns: Record<DirectorType, {
  columns: Partial<Record<keyof DirectorDTO, string>>
  detailedColumns: Partial<Record<keyof DirectorDTO, string>>
}> = {
  INDIVIDUAL_DIRECTOR: { // Visible columns for the normal view
    columns: {
      name: "Full Name",
      dateOfBirth: "Date of Birth",
      countryOfBirth: "Place of Birth",
      nationality: "Nationality",
      id: "Identification Number",
      appointmentDate: "Appointment Date",
      officerTypeName: "Type of Officer",
    },
    detailedColumns: { // Visible columns for the detailed view
      name: "Full Name",
      officerTypeName: "Type of Officer",
      dateOfBirth: "Date of Birth",
      countryOfBirth: "Place of Birth",
      nationality: "Nationality",
      appointmentDate: "Appointment Date",
      residentialAddress: "Residential Address",
      serviceAddress: "Service Address",
    },
  },
  CORPORATE_DIRECTOR: { // Visible columns for the normal view
    columns: {
      name: "Corporate Name",
      appointmentDate: "Appointment Date",
      officerTypeName: "Type of Officer",
      incorporationCountry: "Country of Formation",
      incorporationNumber: "Incorporation Number",
    },
    detailedColumns: { // Visible columns for the detailed view
      name: "Corporate Name",
      officerTypeName: "Type of Officer",
      incorporationNumber: "Incorporation Number",
      dateOfIncorporation: "Incorporation Date",
      incorporationCountry: "Country of Formation",
      appointmentDate: "Appointment Date",
      address: "Registered Office Address",
      serviceAddress: "Service Address",
    },
  },
}
