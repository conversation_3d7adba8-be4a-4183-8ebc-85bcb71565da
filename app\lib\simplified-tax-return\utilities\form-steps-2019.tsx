import type { JS<PERSON> } from "react";
import type { FormStep } from "../hooks/use-form-steps";
import { addressOfHeadOfficeSchema } from "../types/address-of-head-office/2019/address-of-head-office-schema";
import { contactInformationSchema } from "../types/contact-information/2019-2024/contact-information-schema";
import { businessActivitiesFormSchema } from "../types/business-activity/2019-2024/business-activity-schema";
import { taxResidentSchema } from "../types/tax-resident/2019-2021/tax-resident-schema";
import { intellectualPropertiesSchema } from "../types/intellectual-property/2019/intellectual-property-schema";
import { corporateAccountingInformationSchema } from "../types/corporate-accounting-records/2019-2020/corporate-accounting-records-schema";
import { corporateMultinationalEnterpriseSchema } from "../types/corporate-multinational-enterprise/2019-2024/corporate-multinational-enterprise-schema";
import { finalizeSchema } from "../types/finalize/2019-2024/finalize-schema";
import { Pages } from "./form-pages";
import { ContactInformation } from "~/features/simplified-tax-return/components/forms/contact-information/2019-2024/ContactInformation";
import { AddressOfHeadOffice } from "~/features/simplified-tax-return/components/forms/address-of-head-office/2019/AddressOfHeadOffice";
import { BusinessActivities } from "~/features/simplified-tax-return/components/forms/business-activities/2019-2024/BusinessActivities";
import { TaxResident } from "~/features/simplified-tax-return/components/forms/tax-resident/2019-2021/TaxResident";
import { IntellectualProperties } from "~/features/simplified-tax-return/components/forms/intellectual-properties/2019/IntellectualProperties";
import { CorporateAccountingRecords } from "~/features/simplified-tax-return/components/forms/corporate-accounting-records/2019/CorporateAccountingRecords";
import { CorporateMultinationalEnterprise } from "~/features/simplified-tax-return/components/forms/corporate-multinational-enterprise/2019-2024/CorporateMultinationalEnterprise";
import { Finalize } from "~/features/simplified-tax-return/components/forms/finalize/2019-2024/Finalize";

export const formSteps2019: FormStep[] = [
  {
    name: "Address of Head Office",
    page: Pages.ADDRESS_OF_HEAD_OFFICE,
    component: (): JSX.Element => (
      <AddressOfHeadOffice />
    ),
    validationSchema: addressOfHeadOfficeSchema,
    previousPage: null,
    nextPage: Pages.CONTACT_INFORMATION,
  },
  {
    name: "Contact Information",
    page: Pages.CONTACT_INFORMATION,
    component: (): JSX.Element => (
      <ContactInformation />
    ),
    validationSchema: contactInformationSchema,
    previousPage: Pages.ADDRESS_OF_HEAD_OFFICE,
    nextPage: Pages.BUSINESS_ACTIVITIES,
  },
  {
    name: "Business Activities",
    page: Pages.BUSINESS_ACTIVITIES,
    component: (): JSX.Element => (
      <BusinessActivities />
    ),
    validationSchema: businessActivitiesFormSchema,
    previousPage: Pages.CONTACT_INFORMATION,
    nextPage: Pages.TAX_RESIDENT,
  },
  {
    name: "Tax Residency",
    page: Pages.TAX_RESIDENT,
    component: (): JSX.Element => (
      <TaxResident />
    ),
    validationSchema: taxResidentSchema,
    previousPage: Pages.BUSINESS_ACTIVITIES,
    nextPage: (submission: Record<string, any>): string | null => {
      if (submission[Pages.TAX_RESIDENT].incorporatedBefore2019 === "true") {
        return Pages.INTELLECTUAL_PROPERTIES;
      }

      if (submission[Pages.TAX_RESIDENT].incorporatedBefore2019 === "false" && submission[Pages.TAX_RESIDENT].nonTaxResident === "false") {
        return Pages.FINALIZE;
      }

      return null;
    },
  },
  {
    name: "Intellectual Properties",
    page: Pages.INTELLECTUAL_PROPERTIES,
    component: (): JSX.Element => (
      <IntellectualProperties />
    ),
    validationSchema: intellectualPropertiesSchema,
    previousPage: Pages.TAX_RESIDENT,
    nextPage: Pages.CORPORATE_ACCOUNTING_RECORDS,
  },
  {
    name: "Corporate Accounting Records",
    page: Pages.CORPORATE_ACCOUNTING_RECORDS,
    component: (): JSX.Element => (
      <CorporateAccountingRecords />
    ),
    validationSchema: corporateAccountingInformationSchema,
    previousPage: Pages.INTELLECTUAL_PROPERTIES,
    nextPage: Pages.CORPORATE_MULTINATIONAL_ENTERPRISE,
  },
  {
    name: "MNE Group",
    page: Pages.CORPORATE_MULTINATIONAL_ENTERPRISE,
    component: (): JSX.Element => (
      <CorporateMultinationalEnterprise />
    ),
    validationSchema: corporateMultinationalEnterpriseSchema,
    previousPage: Pages.CORPORATE_ACCOUNTING_RECORDS,
    nextPage: Pages.FINALIZE,
  },
  {
    name: "Declaration",
    page: Pages.FINALIZE,
    component: (): JSX.Element => (
      <Finalize />
    ),
    validationSchema: finalizeSchema,
    previousPage: (submission: Record<string, any>): string | null => {
      if (submission[Pages.TAX_RESIDENT].incorporatedBefore2019 === "true") {
        return Pages.CORPORATE_MULTINATIONAL_ENTERPRISE;
      }

      if (submission[Pages.TAX_RESIDENT].incorporatedBefore2019 === "false" && submission[Pages.TAX_RESIDENT].nonTaxResident === "false") {
        return Pages.TAX_RESIDENT;
      }

      return null;
    },
    nextPage: Pages.CONFIRMATION,
  },
];
