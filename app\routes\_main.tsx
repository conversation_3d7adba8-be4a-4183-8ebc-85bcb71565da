import type { JS<PERSON> } from "react";
import { Outlet, json } from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { Sidebar } from "~/components/layout/sidebar/Sidebar";
import { getInboxInfo } from "~/services/api-generated";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const { userId } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const { data: inboxData, error: inboxError } = await getInboxInfo({
    headers: await authHeaders(request),
    query: { userId },
  });

  if (inboxError || !inboxData) {
    throw new Response("Failed to fetch inbox info", { status: 500 });
  }

  return json(inboxData);
}

export default function MainLayout(): JSX.Element {
  return (
    <Sidebar>
      <Outlet />
    </Sidebar>
  );
}
