import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Label, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { IncomeTable } from "../../tables/IncomeTable";
import { IncomeDialog } from "../../dialogs/IncomeDialog";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { BASIC_FINANCIAL_REPORT_FORM_ID } from "~/lib/basic-financial-report/utilities/constants";
import type { IncomeDetailsSchemaType } from "~/lib/basic-financial-report/types/income-details-schema";
import { incomeDetailsSchema } from "~/lib/basic-financial-report/types/income-details-schema";
import { Currency } from "~/lib/basic-financial-report/utilities/currencies";
import type { IncomeSchemaType } from "~/lib/basic-financial-report/types/income-schema";
import { incomeSchema } from "~/lib/basic-financial-report/types/income-schema";

type FieldNameType = "otherIncomes" | "otherEndingInterestReceivable" | "otherBeginningInterestReceivable"

export function IncomeDetails(): ReactNode {
  const { submissionData } = useSubmission();
  const data = useMemo(() => submissionData[Pages.INCOME_DETAILS] as IncomeDetailsSchemaType, [submissionData]);
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const [open, setOpen] = useState(false);
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [incomeIndex, setIncomeIndex] = useState<number | undefined>();
  const [fieldName, setFieldName] = useState< FieldNameType | undefined>()
  const form = useForm<IncomeDetailsSchemaType>({
    resolver: zodResolver(incomeDetailsSchema),
    shouldFocusError: false,
    defaultValues: {
      totalInterestIncome: "",
      totalDividendIncome: "",
      otherIncome: "",
      otherIncomes: [],
      endingInterestReceivable: "",
      otherEndingInterestReceivable: [],
      beginningInterestReceivable: "",
      otherBeginningInterestReceivable: [],
      totalProceeds: "",
      totalMarketValue: "",
      totalPurchaseCost: "",
    },
  });
  const incomeForm = useForm<IncomeSchemaType>({
    resolver: zodResolver(incomeSchema),
    defaultValues: { description: "", amount: "" },
  });
  const otherIncomesArray = useFieldArray({
    control: form.control,
    name: "otherIncomes",
    keyName: "formArrayId",
  });
  const otherEndingInterestReceivableArray = useFieldArray({
    control: form.control,
    name: "otherEndingInterestReceivable",
    keyName: "formArrayId",
  });
  const otherBeginningInterestReceivableArray = useFieldArray({
    control: form.control,
    name: "otherBeginningInterestReceivable",
    keyName: "formArrayId",
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  function addIncome(fieldName: FieldNameType): void {
    incomeForm.reset();
    setFieldName(fieldName)
    setIncomeIndex(undefined);
    setOpen(true);
  }

  function onSubmitIncome(data: IncomeSchemaType): void {
    if (fieldName === "otherIncomes") {
      if (incomeIndex !== undefined) {
        otherIncomesArray.update(incomeIndex, data);
      } else {
        otherIncomesArray.append(data);
      }
    }

    if (fieldName === "otherEndingInterestReceivable") {
      if (incomeIndex !== undefined) {
        otherEndingInterestReceivableArray.update(incomeIndex, data);
      } else {
        otherEndingInterestReceivableArray.append(data);
      }
    }

    if (fieldName === "otherBeginningInterestReceivable") {
      if (incomeIndex !== undefined) {
        otherBeginningInterestReceivableArray.update(incomeIndex, data);
      } else {
        otherBeginningInterestReceivableArray.append(data);
      }
    }

    setOpen(false);
  }

  function onSelect(fieldName: FieldNameType, income: IncomeSchemaType, index: number): void {
    setFieldName(fieldName)
    incomeForm.reset(income, { keepDefaultValues: true });
    setIncomeIndex(index);
    setOpen(true);
  }

  function onDelete(): void {
    if (fieldName === "otherIncomes") {
      otherIncomesArray.remove(incomeIndex);
    }

    if (fieldName === "otherEndingInterestReceivable") {
      otherEndingInterestReceivableArray.remove(incomeIndex);
    }

    if (fieldName === "otherBeginningInterestReceivable") {
      otherBeginningInterestReceivableArray.remove(incomeIndex);
    }

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(fieldName: FieldNameType, index: number): void {
    setFieldName(fieldName)
    setIncomeIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setIncomeIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  const fetcher = useFetcher();

  function onSubmit(data: IncomeDetailsSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  return (
    <>
      <IncomeDialog
        open={open}
        setOpen={setOpen}
        form={incomeForm}
        onSubmit={onSubmitIncome}
      />
      <Dialog open={openDeletedConfirmation} onOpenChange={setOpenDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete the property?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation} disabled={isSubmitting}>Cancel</Button>
            <Button type="button" variant="destructive" onClick={onDelete} disabled={isSubmitting}>Yes, delete this property</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={BASIC_FINANCIAL_REPORT_FORM_ID} className="space-y-5">
          <div className="flex-col space-y-7">
            <div>
              <Tooltip delayDuration={0}>
                <Label>
                  <p className="flex gap-1">
                    Income
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                </Label>
                <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                  <p>
                    Accounting income shows the results of all operational and financial activities engaged in by a business.
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div>
              <Tooltip delayDuration={0}>
                <Label>
                  <p className="flex gap-1">
                    Invest Income
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                </Label>
                <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                  <p>
                    Invest income is money that is received in interest payments, dividends, capital gains realized with the sale of stock or other assets, and any other profit made through an investment vehicle
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <FormField
              control={form.control}
              name="totalInterestIncome"
              render={({ field, fieldState }) => (
                <FormItem>
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        Please indicate the TOTAL interest income received for the period.*
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      <p>
                        Interest income is the amount paid to an entity for lending its money or letting another entity use its funds. On a larger scale, interest income is the amount earned by an investor's money that he places in an investment or project.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="totalDividendIncome"
              render={({ field, fieldState }) => (
                <FormItem>
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        Please indicate the TOTAL dividend income received for the period.*
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      <p>
                        A dividend is the distribution of corporate profits to eligible shareholders. · Dividend payments and amounts are determined by a company's board of directors.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div>
              <Tooltip delayDuration={0}>
                <Label>
                  <p className="flex gap-1">
                    Please indicate the TOTAL other income received for the period. Kindly specify, if any :*
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                </Label>
                <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                  <p>
                    A Other income - gains indicate the net money made from other activities, like the sale of long-term assets. These include the net income realized from one-time non-business activities, like a company selling its old transportation van, unused land, or a subsidiary company.
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>

            <FormField
              control={form.control}
              name="otherIncome"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Other income received</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="otherIncomes"
              control={form.control}
              render={({ fieldState }) => (
                <FormItem>
                  <FormLabel>Other income, please specify*</FormLabel>
                  {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                  <FormControl>
                    <IncomeTable
                      disabled={isSubmitting}
                      incomes={otherIncomesArray.fields}
                      onSelect={(income, index) => onSelect("otherIncomes", income, index)}
                      onDelete={index => onOpenDeleteConfirmation("otherIncomes", index)}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button size="sm" onClick={() => addIncome("otherIncomes")} type="button" disabled={isSubmitting}>
                <Plus className="mr-2 size-4 text-white" />
                Add Other Income
              </Button>
            </div>
            <Label>Please indicate the TOTAL income earned this period but not yet received (Accrued Interest Receivable):*</Label>
            <FormField
              control={form.control}
              name="endingInterestReceivable"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Ending interest receivable*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="otherEndingInterestReceivable"
              control={form.control}
              render={({ fieldState }) => (
                <FormItem>
                  <FormLabel>Other income, please specify*</FormLabel>
                  {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                  <FormControl>
                    <IncomeTable
                      disabled={isSubmitting}
                      incomes={otherEndingInterestReceivableArray.fields}
                      onSelect={(income, index) => onSelect("otherEndingInterestReceivable", income, index)}
                      onDelete={index => onOpenDeleteConfirmation("otherEndingInterestReceivable", index)}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button size="sm" onClick={() => addIncome("otherEndingInterestReceivable")} type="button" disabled={isSubmitting}>
                <Plus className="mr-2 size-4 text-white" />
                Add Other Income
              </Button>
            </div>
            <Label>Please indicate prior period income received this period:*</Label>
            <FormField
              control={form.control}
              name="beginningInterestReceivable"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Beginning interest receivable*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="otherBeginningInterestReceivable"
              control={form.control}
              render={({ fieldState }) => (
                <FormItem>
                  <FormLabel>Other income, please specify*</FormLabel>
                  {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                  <FormControl>
                    <IncomeTable
                      disabled={isSubmitting}
                      incomes={otherBeginningInterestReceivableArray.fields}
                      onSelect={(income, index) => onSelect("otherBeginningInterestReceivable", income, index)}
                      onDelete={index => onOpenDeleteConfirmation("otherBeginningInterestReceivable", index)}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button size="sm" onClick={() => addIncome("otherBeginningInterestReceivable")} type="button" disabled={isSubmitting}>
                <Plus className="mr-2 size-4 text-white" />
                Add Other Income
              </Button>
            </div>
            <Label>Sale of Securities Investment*</Label>
            <FormField
              control={form.control}
              name="totalProceeds"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Please indicate the total proceeds from the sale of securities investments for the period.</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Label>Please indicate the total market value and purchase costs at the end of the period.</Label>
            <FormField
              control={form.control}
              name="totalMarketValue"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>TOTAL Securities Investments Market value, end - to calculate the Unrealised gains(losses)*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="totalPurchaseCost"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>TOTAL Securities Investments Purchase cost, end - to calculate the Unrealised gains(losses)*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </RemixForm>
      </Form>
    </>
  )
}
