import { <PERSON><PERSON>, <PERSON>rollA<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { useNavigation } from "@remix-run/react";
import { Pencil, X } from "lucide-react";
import type { JSX } from "react";
import { type PropertySchemaType, PropertyType } from "~/lib/basic-financial-report/types/equity-details-schema";

type Props = {
  properties: (PropertySchemaType & { formArrayId: string })[]
  onSelect: (activity: PropertySchemaType, index: number) => void
  onDelete: (index: number) => void
}

export function PropertyTable({
  properties,
  onSelect,
  onDelete,
}: Props): JSX.Element {
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"

  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Property Type</TableHead>
              <TableHead>Property Value</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!properties.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No properties available
                </TableCell>
              </TableRow>
            )}
            {properties.length > 0 && properties.map((property, index) => (
              <TableRow key={property.formArrayId}>
                <TableCell>{property.type === PropertyType.OTHER ? property.otherType : property.type}</TableCell>
                <TableCell>{`$ ${property.value}`}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(property, index)} disabled={isSubmitting}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={isSubmitting}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell className="font-semibold">TOTAL</TableCell>
              <TableCell className="font-semibold">{`$ ${properties.reduce((acc, cur) => acc + Number(cur.value), 0)}`}</TableCell>
              <TableCell />
            </TableRow>
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
