import { z } from "zod";
import { nonNullDate, stringBoolean } from "~/lib/utilities/zod-validators";

export const financialPeriodSchema = z.object({
  hasFinancialPeriodChanged: stringBoolean(),
  startDate: nonNullDate("Financial Period start date"),
  endDate: nonNullDate("Financial Period end date"),
  previousEndDate: z.coerce.date(),
  applicationDateToITA: z.coerce.date(),
});

export type FinancialPeriodSchemaType = z.infer<typeof financialPeriodSchema>;
