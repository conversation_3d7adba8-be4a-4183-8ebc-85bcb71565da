import type { LoaderFunctionArgs } from "@remix-run/node";
import { ArrowBigLeftDash } from "lucide-react";
import type { JSX } from "react";
import { middleware } from "~/lib/middlewares.server";

export async function loader({ request }: LoaderFunctionArgs): Promise<null> {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);

  return null;
}

export default function Dashboard(): JSX.Element {
  return (
    <main className="flex justify-center items-center min-h-screen lg:pl-[260px] bg-gray-50 top-[349px]">
      <div className="flex flex-col items-center w-96 gap-5 fixed top-80">
        <div className="flex py-2 mb-2 gap-4">
          <ArrowBigLeftDash size={42} className="text-blue-600" />
          <h2 className="text-4xl font-semibold font-inter mb-4 text-blue-800">Choose activity</h2>
        </div>
        <p className="text-2xl text-center font-inter">Choose an activity from the left menu to get started.</p>
      </div>
    </main>
  );
}
