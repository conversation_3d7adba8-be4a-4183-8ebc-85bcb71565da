import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, RadioGroup, RadioGroupItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Link, Form as RemixForm, useFetcher } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import { type JSX, useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { ContactAgentDetails } from "../../ContactAgentDetails";
import { CorporateAccountingActivitiesDialog } from "./CorporateAccountingActivitiesDialog";
import { CorporateAccountingActivitiesTable } from "./CorporateAccountingActivitiesTable";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import type { AccountingActivityType, CorporateAccountingInformationType } from "~/lib/simplified-tax-return/types/corporate-accounting-records/2021/corporate-accounting-records-schema";
import { accountingActivitySchema, corporateAccountingInformationSchema } from "~/lib/simplified-tax-return/types/corporate-accounting-records/2021/corporate-accounting-records-schema";

export function CorporateAccountingRecords(): JSX.Element {
  const { submissionData, setCanContinue } = useSubmission();
  const data = useMemo(() => {
    const pageData = submissionData[Pages.CORPORATE_ACCOUNTING_RECORDS];
    if (pageData?.accountingActivities === "") {
      pageData.accountingActivities = [];
    }

    return pageData as CorporateAccountingInformationType;
  }, [submissionData]);
  const [open, setOpen] = useState(false);
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [activityIndex, setActivityIndex] = useState<number | undefined>();
  const form = useForm<CorporateAccountingInformationType>({
    resolver: zodResolver(corporateAccountingInformationSchema),
    shouldFocusError: false,
    defaultValues: {
      accountingActivities: [],
    },
  });
  const accountingActivityForm = useForm<AccountingActivityType>({
    resolver: zodResolver(accountingActivitySchema),
    defaultValues: {
      description: "",
    },
  });
  const {
    fields,
    append,
    remove,
    update,
  } = useFieldArray({
    control: form.control,
    name: "accountingActivities",
    keyName: "formArrayId",
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  function addAccountingActivity(): void {
    accountingActivityForm.reset();
    setActivityIndex(undefined);
    setOpen(true);
  }

  function onSubmitActivity(data: AccountingActivityType): void {
    if (activityIndex !== undefined) {
      update(activityIndex, data);
    } else {
      append(data);
    }

    form.trigger();
    setOpen(false);
  }

  function onSelect(activity: AccountingActivityType, index: number): void {
    accountingActivityForm.reset(activity, { keepDefaultValues: true });
    setActivityIndex(index);
    setOpen(true);
  }

  function onDelete(): void {
    remove(activityIndex);
    form.trigger();
    setOpenDeleteConfirmation(false);
  }

  const fetcher = useFetcher();

  function onSubmit(data: CorporateAccountingInformationType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  function onOpenDeleteConfirmation(index: number): void {
    setActivityIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setActivityIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  const assessableIncomeGenerated = form.watch("assessableIncomeGenerated");
  const activitiesCondition = form.watch("activitiesCondition");

  useEffect(() => {
    if (assessableIncomeGenerated === "true" && activitiesCondition === "true") {
      setCanContinue(false);
    } else {
      setCanContinue(true);
    }
  }, [assessableIncomeGenerated, activitiesCondition, setCanContinue]);

  return (
    <>
      <CorporateAccountingActivitiesDialog
        setOpen={setOpen}
        open={open}
        form={accountingActivityForm}
        onSubmit={onSubmitActivity}
      />
      <Dialog open={openDeletedConfirmation} onOpenChange={setOpenDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete the activity?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation}>Cancel</Button>
            <Button type="button" variant="destructive" onClick={onDelete}>Yes, delete this activity</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id="str-form">
          <div className="flex-col space-y-5">
            <FormField
              control={form.control}
              name="assessableIncomeGenerated"
              render={({ field, fieldState }) => (
                <FormItem className="max-w-2xl">
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        Have you generated any income in the financial reporting period that would be
                        assessable in the absence of the exemptions conferred under
                        Section 224 of the Companies Act, Section 136 of the Nevis Business Corporations Ordinance or
                        Section 96 of the Nevis Limited Liability Company Ordinance? *
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 font-inter" side="bottom">
                      <span className="font-bold">
                        Assessable income:
                      </span>
                      <p>
                        "Assessable income" means the income from the sources described in
                        section 3 computed in accordance with the provisions of parts II, III, IV and
                        V of this Act; Income Tax Act Cap 20.22 (Section 2).
                      </p>
                      <Button variant="link" asChild>
                        <Link to="/documents/Ch-20-22-income-tax-act-2020.pdf" target="_blank">INCOME TAX ACT and Subsidiary Legislation</Link>
                      </Button>
                      <Button variant="link" asChild>
                        <Link to="/documents/income-tax-mendment-act-draft.pdf" target="_blank">Income Tax (Amendment) Bill, 2020</Link>
                      </Button>
                    </TooltipContent>
                  </Tooltip>

                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      invalid={!!fieldState.error}
                      value={field.value}
                      orientation="horizontal"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {assessableIncomeGenerated === "true" && (
              <FormField
                control={form.control}
                name="activitiesCondition"
                render={({ field, fieldState }) => (
                  <FormItem className="max-w-2xl">
                    <FormLabel className="flex flex-col space-y-2">
                      <span>
                        i) has there been a change in your activities since December 31, 2018; or
                      </span>
                      <span>
                        ii) have you acquired new assets after December 31, 2018? *
                      </span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        invalid={!!fieldState.error}
                        value={field.value}
                        orientation="horizontal"
                      >
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Yes
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            No
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {assessableIncomeGenerated === "true" && (
              <>
                {activitiesCondition === "false" && (
                  <>
                    <FormField
                      name="accountingActivities"
                      control={form.control}
                      render={({ fieldState }) => (
                        <FormItem>
                          <FormLabel>Activities</FormLabel>
                          {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                          <FormControl>
                            <CorporateAccountingActivitiesTable
                              activities={fields}
                              onSelect={onSelect}
                              onDelete={onOpenDeleteConfirmation}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <div className="flex justify-end pt-5">
                      <Button size="sm" onClick={addAccountingActivity} type="button">
                        <Plus className="mr-2 size-4 text-white" />
                        Add Activity
                      </Button>
                    </div>
                  </>
                )}
                {activitiesCondition === "true" && <ContactAgentDetails />}
              </>
            )}
          </div>
        </RemixForm>
      </Form>
    </>
  )
}
