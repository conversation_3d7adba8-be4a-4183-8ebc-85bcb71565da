import type { JSX, ReactNode } from "react";
import { cn } from "@netpro/design-system";
import type { ClassValue } from "clsx";

/**
 * A container that is 1/2 width on medium screens and full width on small screens.
 * It can be used to display wide content that should not take up the full width of the screen.
 */
export function WideContainer({ children, className }: { className?: ClassValue, children: ReactNode }): JSX.Element {
  return (
    <div className={cn("xl:w-1/2 lg:w-2/3 md:mx-auto md:w-2/3 sm:w-full", className)}>
      {children}
    </div>
  )
}
