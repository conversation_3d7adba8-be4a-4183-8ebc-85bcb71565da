import type { ActionFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { middleware } from "~/lib/middlewares.server";

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  // This validation is executed first since the session could have been destroyed on a logout. Middleware will not be executed in this case.
  if (!session.get("queuedMasterClientUpdate")) {
    return json({ success: true });
  }

  await middleware(["auth", "mfa", "terms"], request);

  session.unset("queuedMasterClientUpdate");

  return json({ success: true }, {
    status: 200,
    headers: {
      "Set-Cookie": await commitSession(session),
    },
  });
}
