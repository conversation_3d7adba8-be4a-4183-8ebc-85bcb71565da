import { redirect } from "@remix-run/react";
import type { TypedResponse } from "@remix-run/node";
import { authHeaders } from "../auth/utils/auth-headers";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";
import type { BasicMasterClient } from "~/features/master-clients/types/generic";
import { clientGetMasterClients } from "~/services/api-generated";

type ReturnData = {
  masterClient: BasicMasterClient
};

export default async function requireMcc({ request }: MiddlewareProps): MiddlewareResponse<TypedResponse<never> | ReturnData> {
  const { currentMasterClient } = await getSessionData(request);
  const session = await getSession(request.headers.get("Cookie"));
  if (!currentMasterClient) {
    const { data, error } = await clientGetMasterClients({ headers: await authHeaders(request) });
    if (error) {
      console.error("API request to fetch master clients failed", error);
      throw new Response("Failed to retrieve Master Clients", { status: 412 });
    }

    if (!data?.masterClients || data.masterClients.length === 0) {
      throw new Response("No Master Clients found for this account", { status: 404 });
    }

    const { masterClients } = data;
    const basicMasterClients: BasicMasterClient[] = masterClients.map(client => ({
      masterClientId: client.masterClientId,
      masterClientCode: client.masterClientCode,
    }))

    // redirect to select companies if the user only has one MasterClient
    session.set("totalMasterClients", basicMasterClients.length);

    if (masterClients?.length === 1) {
      session.set("currentMasterClient", basicMasterClients[0]);

      throw redirect("/companies", {
        headers: {
          "Set-Cookie": await commitSession(session),
        },
      });
    }

    throw redirect("/master-clients", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    });
  }

  return { masterClient: currentMasterClient };
}
