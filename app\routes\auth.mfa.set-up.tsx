import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import type { JSX } from "react";
import { useSubmit } from "@remix-run/react";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RadioGroup,
  RadioGroupItem,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@netpro/design-system";
import { Form as RemixForm } from "@remix-run/react/dist/components";
import { Info } from "lucide-react";
import { Method } from "~/lib/auth/utils/mfa";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import { mfaUpdateFormSchema, updateMfaMethod } from "~/features/mfa/api/update-mfa-method";
import type { mfaUpdateFormType } from "~/features/mfa/api/update-mfa-method";
import { middleware } from "~/lib/middlewares.server";

const title = "Set up multi-factor authentication" as const;

export const meta: MetaFunction = () => [
  { title },
];

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse | null> {
  // You should be redirected here if you just logged in and don't have a MFA type set up
  await middleware(["auth"], request);
  const { mfaMethod, mfaCompleted } = await getSessionData(request);
  if (mfaMethod !== Method.None || mfaCompleted) {
    // Already has MFA set up, redirect to the dashboard
    return redirect("/dashboard");
  }

  return null;
}

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse<null> | null | undefined> {
  const { mfaMethod, accessToken, userId, mfaCompleted } = await getSessionData(request);
  if (mfaMethod !== Method.None || mfaCompleted) {
    // Already has MFA set up, redirect to the dashboard
    return redirect("/dashboard");
  }

  const session = await getSession(request.headers.get("Cookie"));

  try {
    const formData = await request.formData();
    const data = JSON.parse(formData.get("data") as string) as mfaUpdateFormType;
    mfaUpdateFormSchema.parse(data);
    const { mfaMethod: mfaMethodForm } = data;
    const mfaMethodData = await updateMfaMethod({ data: {
      mfaMethod: mfaMethodForm,
      userId: userId as string,
    }, accessToken: accessToken as string, userId: userId as string });

    if (mfaMethodForm === Method.Email) {
      session.flash("notification", { title: "MFA method set up", message: "MFA method set up successfully", variant: "success" });
      session.set("mfaMethod", Method.Email);

      return redirect("/auth/mfa/email", { headers: { "Set-Cookie": await commitSession(session) } });
    } else {
      session.flash("mfaAuthenticatorQRUrl", mfaMethodData.mfaAuthenticatorQRUrl);
      session.flash("mfaAuthenticatorSecret", mfaMethodData.mfaAuthenticatorSecret);

      return redirect("/auth/mfa/authenticator/set-up", { headers: { "Set-Cookie": await commitSession(session) } });
    }
  } catch (error) {
    const err = error as Response;
    const errorMessage = await err.text();

    if (errorMessage) {
      session.flash("notification", { title: "An error has occurred", message: errorMessage, variant: "error" });

      return json(null, { status: err.status, headers: { "Set-Cookie": await commitSession(session) } });
    }
  }
}

export default function MFASetUp(): JSX.Element {
  const submit = useSubmit();
  const form = useForm<z.infer<typeof mfaUpdateFormSchema>>({
    resolver: zodResolver(mfaUpdateFormSchema),
  });

  function onSubmit(data: z.infer<typeof mfaUpdateFormSchema>): void {
    submit({ data: JSON.stringify(data) }, { method: "POST" });
  }

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit)} className="mt-4" noValidate>
        <FormField
          control={form.control}
          name="mfaMethod"
          render={({ field, fieldState }) => (
            <FormItem className="space-y-3">
              <FormLabel>Please select your authentication method:</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  invalid={!!fieldState.error}
                  className="flex flex-col space-y-1"
                >
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value={Method.Authenticator} />
                    </FormControl>
                    <FormLabel className="font-normal flex">
                      Use an Authenticator app
                      <Tooltip delayDuration={0}>
                        <TooltipTrigger asChild>
                          <Info className="size-4 ml-2" />
                        </TooltipTrigger>
                        <TooltipContent className="w-96" side="right">
                          <p>
                            Two-factor authentication is available through the use of an authenticator app.
                            Download a compatible app (e.g., Google Authenticator, Microsoft Authenticator, 1Password)
                            from the Google Play or Apple App Store to your smartphone. During the initial login,
                            scan the displayed QR code using the app. A six-digit code will be generated by the
                            app for each login attempt.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value={Method.Email} />
                    </FormControl>
                    <FormLabel className="font-normal flex">
                      Send a verification code to my email
                      <Tooltip delayDuration={0}>
                        <TooltipTrigger asChild>
                          <Info className="size-4 ml-2" />
                        </TooltipTrigger>
                        <TooltipContent className="w-96" side="right">
                          <p>
                            Upon initial account registration and for each subsequent login attempt, a
                            verification code will be sent to your registered email address. This code
                            will be valid for 5 minutes.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex flex-row justify-end w-full mt-6">
          <Button type="submit" className="rounded-none h-8 bg-[#0067b8] font-normal">Confirm method</Button>
        </div>
      </RemixForm>
    </Form>
  )
}
