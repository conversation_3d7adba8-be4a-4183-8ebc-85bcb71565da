import { z } from "zod";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export const createSubmissionSchema = z.object({
  companyId: z.string().min(1, "Required"),
  moduleId: z.string().min(1, "Required"),
  year: z.number().int().min(2019, "Invalid year"),
});

export type createSubmissionType = z.infer<typeof createSubmissionSchema>;

export type CreatedSubmission = {
  id: string
  name: string
  financialYear: number
  createdAt: string
  status: number
  statusText: string
  formDocument: any
}

export function createSubmission({ data, accessToken, userId }: { data: createSubmissionType } & ClientRequestHeaders): Promise<CreatedSubmission> {
  return client.post(
    `/client/companies/${data.companyId}/modules/${data.moduleId}/submissions`,
    accessToken,
    userId,
    null,
    { params: { year: data.year } },
  );
}
