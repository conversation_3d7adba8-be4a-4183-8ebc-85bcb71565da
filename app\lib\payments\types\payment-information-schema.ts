import { z } from "zod";
import { nonEmptyString } from "~/lib/utilities/zod-validators";

export const paymentInformationSchema = z.object({
  firstName: nonEmptyString("First name"),
  lastName: nonEmptyString("Last name"),
  address: nonEmptyString("Address"),
  city: nonEmptyString("City"),
  state: nonEmptyString("State"),
  zipCode: z.string()
    .min(4, { message: "Zip code must be at least 4 characters." })
    .max(10, { message: "Zip code must be no more than 10 characters." })
    .regex(/^\d+$/, { message: "Zip code must contain only digits." }),
  country: nonEmptyString("Country"),
  phoneNumber: z.string()
    .min(10, { message: "Phone number must be at least 10 digits." })
    .max(15, { message: "Phone number must be no more than 15 digits." })
    .regex(/^\d+$/, { message: "Phone number must contain only digits." }),
  creditCardNumber: z.string()
    .min(13, { message: "Credit card number must be at least 13 digits." })
    .max(19, { message: "Credit card number must be no more than 19 digits." })
    .regex(/^\d+$/, { message: "Credit card number must contain only digits." })
    .refine((num) => {
      let sum = 0;
      let shouldDouble = false;
      // Luhn algorithm to validate the credit card number
      for (let i = num.length - 1; i >= 0; i--) {
        let digit = Number.parseInt(num.charAt(i), 10);
        if (shouldDouble) {
          digit *= 2;
          if (digit > 9) {
            digit -= 9;
          }
        }

        sum += digit;
        shouldDouble = !shouldDouble;
      }

      return sum % 10 === 0;
    }, { message: "Invalid credit card number." }),
  expirationMonth: z.string()
    .length(2, { message: "Expiration month is required." })
    .regex(/^(0[1-9]|1[0-2])$/, { message: "Expiration month must be between 01 and 12." }),
  expirationYear: z.string()
    .length(4, { message: "Expiration year is required." })
    .regex(/^\d{4}$/, { message: "Expiration year must contain only digits." })
    .refine((year) => {
      const currentYear = new Date().getFullYear();

      return Number(year) >= currentYear;
    }, { message: "Expiration year cannot be in the past." }),
  cvv: z.string()
    .min(3, { message: "CVV must be at least 3 digits." })
    .max(4, { message: "CVV must be no more than 4 digits." })
    .regex(/^\d+$/, { message: "CVV must contain only digits." }),
});

export type PaymentInformationType = z.infer<typeof paymentInformationSchema>;
