import type { TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/react";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";

type AuthenticatedSessionData = {
  userId: string
  accessToken: string
};

export default async function auth({ request }: MiddlewareProps): MiddlewareResponse<TypedResponse<never> | AuthenticatedSessionData> {
  const { userId, accessToken, accessTokenExpiresOn } = await getSessionData(request);
  const session = await getSession(request.headers.get("Cookie"));
  if (!userId || !accessToken) {
    throw redirect("/login");
  }

  // If the access token is expired, redirect to application auth, which redirects back to the requested route
  if (accessTokenExpiresOn && new Date(accessTokenExpiresOn) < new Date()) {
    const url = new URL(request.url);

    // Redirect to dashboard on Resource Routes requests
    session.flash("redirect", url.pathname.startsWith("/api/") ? "/dashboard" : url.pathname);

    throw redirect("/auth/application", { headers: { "Set-Cookie": await commitSession(session) } });
  }

  return { userId, accessToken }
}
