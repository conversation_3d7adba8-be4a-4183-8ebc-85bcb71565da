import { <PERSON><PERSON>, <PERSON>rollArea, <PERSON><PERSON>B<PERSON>, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Penci<PERSON>, X } from "lucide-react";
import type { ReactNode } from "react";
import type { CigaOutsourcingProvidersSchemaType } from "~/lib/economic-substance/types/bahamas/ciga-schema";

type Props = {
  providers: (CigaOutsourcingProvidersSchemaType & { formArrayId: string })[]
  onSelect: (income: CigaOutsourcingProvidersSchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function CigaOutsourcingProvidersTable({
  providers,
  onSelect,
  onDelete,
  disabled,
}: Props): ReactNode {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Entity Name</TableHead>
              <TableHead>Details of Resources</TableHead>
              <TableHead>Number of Staff</TableHead>
              <TableHead>Monitoring and Control</TableHead>
              <TableHead>Physical Address</TableHead>
              <TableHead>Monitoring Control Explanation</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!providers.length && (
              <TableRow>
                <TableCell colSpan={7} className="text-center text-gray-500">
                  No providers available
                </TableCell>
              </TableRow>
            )}
            {providers.length > 0 && providers.map((item, index) => (
              <TableRow key={item.formArrayId}>
                <TableCell>{item.entityName}</TableCell>
                <TableCell>{item.detailsOfResources}</TableCell>
                <TableCell>{item.numberOfStaff}</TableCell>
                <TableCell>{item.monitoringAndControl === "true" ? "Yes" : "No"}</TableCell>
                <TableCell>{item.physicalAddress}</TableCell>
                <TableCell>{item.monitoringControlExplanation}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(item, index)} disabled={disabled}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
