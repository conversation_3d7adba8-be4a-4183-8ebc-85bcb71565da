import type { ActionFunctionArgs, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { z } from "zod";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import type { ErrorResponse } from "~/lib/types/error-response";
import { middleware } from "~/lib/middlewares.server";
import { clientRequestAssistance } from "~/services/api-generated";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

const assistanceRequestSchema = z.object({
  legalEntityId: z.string().uuid({
    message: "Legal entity ID must be a valid UUID.",
  }),
  assistanceRequestType: z.enum(["NoBeneficialOwner", "NoDirector", "NoShareholder"]),
  assistanceRequestComments: z.string().min(1, {
    message: "Assistance request comments are required.",
  }),
})

export async function action({ request, params }: ActionFunctionArgs): Promise<TypedResponse<{ success: boolean, data?: any, error?: string }>> {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireBoDirModule"], request);
  const { accessToken, userId } = await getSessionData(request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id: companyId } = params;

  if (!accessToken || !userId) {
    return redirect("/login");
  }

  if (!companyId) {
    return json({ success: false, error: "No company selected" }, { status: 400 });
  }

  const formData = await request.formData();
  const { data: validatedData, error: parseError } = assistanceRequestSchema.safeParse({
    legalEntityId: formData.get("legalEntityId"),
    assistanceRequestType: formData.get("assistanceRequestType"),
    assistanceRequestComments: formData.get("assistanceRequestComments"),
  });

  if (parseError) {
    const errorMessages = parseError.errors.map(err => err.message).join(", ");
    session.flash("notification", { title: "Validation Error", message: errorMessages, variant: "error" });

    return json({
      success: false,
      error: errorMessages,
    }, {
      headers: { "Set-Cookie": await commitSession(session) },
      status: 400,
    });
  }

  const { data, error } = await clientRequestAssistance({
    headers: await authHeaders(request),
    path: { companyId },
    body: validatedData,
  });

  if (error) {
    console.error("Error sending assistance request:", error);
    session.flash("notification", { title: "Error", message: "An error occurred while processing your request", variant: "error" });

    return json<ErrorResponse>({
      success: false,
      error: "An error occurred while processing your request",
    }, {
      headers: { "Set-Cookie": await commitSession(session) },
      status: 500,
    });
  }

  session.flash("notification", { title: "Success", message: "We have received your request for assistance. A Trident Trust Representative will be in touch shortly.", variant: "success" });

  return json({ success: true, data }, { headers: { "Set-Cookie": await commitSession(session) }, status: 200 });
}
