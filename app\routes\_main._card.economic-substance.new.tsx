import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/react";
import { getESModule } from "~/features/modules/api/get-modules";
import { middleware } from "~/lib/middlewares.server";
import { clientCreateSubmission } from "~/services/api-generated";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<never>> {
  const session = await getSession(request.headers.get("Cookie"));
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireEsModule"], request);
  const esModule = await getESModule(company, request);
  const { data: newSubmission, error } = await clientCreateSubmission({
    headers: await authHeaders(request),
    path: {
      companyId: company.companyId,
      moduleId: esModule.id as string,
    },
  });

  if (error) {
    session.flash("notification", { title: "Error!", message: error.exceptionMessage, variant: "error" });

    return redirect("/economic-substance/drafts", {
      headers: { "Set-Cookie": await commitSession(session) },
    });
  }

  if (!newSubmission) {
    throw new Response("Submission not found", { status: 404 })
  }

  return redirect(`/economic-substance/${newSubmission.id}/${Pages.FINANCIAL_PERIOD}`);
}
