import { BaseActivities } from "./relevant-activities/BaseActivities";
import { BusinessActivities } from "./relevant-activities/BusinessActivities";
import { type PageSlug, Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import type {
  RelevantActivityDeclarationSchemaType,
} from "~/lib/economic-substance/types/bahamas/relevant-activity-declaration-schema";

export function getActivitySummary(activity: NonNullable<RelevantActivityDeclarationSchemaType["relevantActivities"]>[0]) {
  if (!activity) {
    throw new Error("An activity is required to retrieve the summary")
  }

  const { page } = activity

  return (
    <>
      <BaseActivities label={activity.label} page={page as PageSlug} />
      {page !== Pages.HOLDING_BUSINESS && (
        <BusinessActivities page={page as PageSlug} />
      )}

    </>
  )
}
