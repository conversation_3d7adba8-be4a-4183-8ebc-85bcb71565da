<p align="center">
  <a href="https://www.netprogroup.com/">
    <img alt="NetPro Group" src="https://www.netprogroup.com/assets/files/logo-blauw.svg" width="200" />
  </a>
</p>

# Trident Trust - Client portal PCP

This is the repository for the client portal Private Client Portal (PCP) project for Trident Trust.

## Documentation & references

- 📖 [Remix docs](https://remix.run/docs)

## Instructions to run project

1. **This first step is for Windows users only:**

    Connect to the NetPro internal NPM registry by running the following command from the project root:

    ```sh
    # Only needed if you run on Windows
    npx vsts-npm-auth -config .npmrc
    ```

    This will install the vsts-npm-auth package and run it to authenticate with the NetPro NPM registry.
1. Run the following command to install all necessary dependencies:

    ```sh
    npm install
    ```

1. Run the following command to start the project in development mode:

    ```sh
    npm run dev
    ```

## Styling

This project comes with [Tailwind CSS](https://tailwindcss.com/) already configured for a simple default starting experience.

# Notes for Developers

## Function naming conventions

- **Function names should be descriptive and follow the camelCase naming convention.**
- **Function names should start with a verb.**
- **Function names should be in the present tense.**
- **Function names that start with "get" return the expected value or undefined.**
- **Function names that start with "find" return the expected value or null.**
- **Function names that start with "is" return a boolean value.**
- **Function names that start with "has" return a boolean value.**
- **Function names that start with "require" return the expected value or throw an error.**

## Timezone handling

For timezone handling, we use the `formatDate` function from `app/lib/utilities/format.ts`. This function takes a UTC date and formats it into the correct timezone. The function also handles invalid dates and returns a fallback message.

To make sure all of the dates are in the correct format, the following rules must be followed:

- **All dates should be saved in UTC.**
- **All dates should be in ISO format.**
- **Sending a date with no time to the API should ommit the timezone offset. (e.g. 2025-07-09T05:00:00Z -> 2025-07-09T00:00:00Z). This must be done on the frontend using the `formatDateForAPI` function.**
- **When displaying a date to the user, use the `formatDate` function. Make sure to pass the correct timezone based on the jurisdiction.**
  - `getTimezoneFromJurisdiction` can be used to get the correct timezone from the jurisdiction name.
  - `useContext(SessionContext)` can be used to get the current company and jurisdiction.
  - e.g `formatDate(date, { timezone: getTimezoneFromJurisdiction(company.jurisdictionName) })`
- **When displaying a date that comes from a DatePicker or in general, any date where we ignore the time (e.g. date of birth, incorporation date, etc.) and it's saved as 2025-07-09T00:00:00Z in the database, you should use the following format:**
  - `formatDate(date)`. This will format the date on the default format and assume UTC timezone, which is the correct behavior for displaying (PDFs, tables, etc.). Keep in mind this will not work for datepickers.
- **When loading a date from the API to a datepicker, you should use the following structure:**
  - `new Date(formatDate(apiDate, { formatStr: "yyyy-MM-dd 00:00:00" }))`
  - This will convert a UTC 00:00:00Z hour to local 00:00:00 time, this is important since the datepicker works with local time.
- **Formating a date in the server side should be avoided since users timezones are no longer available.**

# Release management

See [wiki page in this project](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_wiki/wikis/Trident-Trust---Private-Client-Portal.wiki/151/Release-Management-(Client-Management-portal))

# API Integration

Backend services are automatically generated using `hey-api`. Unfortunately, due to the constraints of the Azure environment, services cannot be automatically generated in production and must be manually generated by the developer. If you find that some backend services are missing in your project, follow these steps:

1. Ensure your local backend is up-to-date and running. Confirm that the required service(s) are available in the [Swagger documentation](https://localhost:7108/swagger/index.html), as this will be used to generate the services for the frontend.
2. Run the command: `npm run generate-api-service`.
3. **Important:** Do not reformat or manually modify the code in `app/services/api-generated/**` as it will be overwritten by future runs.

### Handling Merge Conflicts

Merge conflicts may occur due to this process. If this happens, simply delete the existing generated services and regenerate them, ensuring you are using the latest version of the local API.
