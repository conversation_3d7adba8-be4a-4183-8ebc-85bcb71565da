<p align="center">
  <a href="https://www.netprogroup.com/">
    <img alt="NetPro Group" src="https://www.netprogroup.com/assets/files/logo-blauw.svg" width="200" />
  </a>
</p>

# Trident Trust - Client portal PCP

This is the repository for the client portal Private Client Portal (PCP) project for Trident Trust.

## Documentation & references

- 📖 [Remix docs](https://remix.run/docs)

## Instructions to run project

1. **This first step is for Windows users only:**

    Connect to the NetPro internal NPM registry by running the following command from the project root:

    ```sh
    # Only needed if you run on Windows
    npx vsts-npm-auth -config .npmrc
    ```

    This will install the vsts-npm-auth package and run it to authenticate with the NetPro NPM registry.
1. Run the following command to install all necessary dependencies:

    ```sh
    npm install
    ```

1. Run the following command to start the project in development mode:

    ```sh
    npm run dev
    ```

## Styling

This project comes with [Tailwind CSS](https://tailwindcss.com/) already configured for a simple default starting experience.

# Notes for Developers

## Function naming conventions

- **Function names should be descriptive and follow the camelCase naming convention.**
- **Function names should start with a verb.**
- **Function names should be in the present tense.**
- **Function names that start with "get" return the expected value or undefined.**
- **Function names that start with "find" return the expected value or null.**
- **Function names that start with "is" return a boolean value.**
- **Function names that start with "has" return a boolean value.**
- **Function names that start with "require" return the expected value or throw an error.**

# Release management

See [wiki page in this project](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_wiki/wikis/Trident-Trust---Private-Client-Portal.wiki/151/Release-Management-(Client-Management-portal))

# API Integration

Backend services are automatically generated using `hey-api`. Unfortunately, due to the constraints of the Azure environment, services cannot be automatically generated in production and must be manually generated by the developer. If you find that some backend services are missing in your project, follow these steps:

1. Ensure your local backend is up-to-date and running. Confirm that the required service(s) are available in the [Swagger documentation](https://localhost:7108/swagger/index.html), as this will be used to generate the services for the frontend.
2. Run the command: `npm run generate-api-service`.
3. **Important:** Do not reformat or manually modify the code in `app/services/api-generated/**` as it will be overwritten by future runs.

### Handling Merge Conflicts

Merge conflicts may occur due to this process. If this happens, simply delete the existing generated services and regenerate them, ensuring you are using the latest version of the local API.
