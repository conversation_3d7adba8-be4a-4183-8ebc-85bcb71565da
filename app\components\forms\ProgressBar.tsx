import { ScrollArea, ScrollBar } from "@netpro/design-system";

type ProgressBarItemProps = {
  value: number
  label: string
  isActive: boolean
  isSelected: boolean
};

function ProgressBarItem({ value, label, isActive, isSelected }: ProgressBarItemProps): JSX.Element {
  const text = `${value.toString()}. ${isSelected ? label : ""}`.trimEnd();

  return (
    <span className={`text-xs font-semibold text-blue-800 px-2 py-3 mr-[2px]
      text-nowrap border-b-2 ${isActive ? "border-teal-600" : "border-teal-900"}`}
    >
      {text}
    </span>
  );
}

type ProgressBarProps = {
  items: string[]
  activeIndex: number
};

export function ProgressBar({ items, activeIndex }: ProgressBarProps): JSX.Element {
  return (
    <ScrollArea>
      <div className="flex pb-5">
        {items.map((item, index) => (
          <ProgressBarItem
            key={item}
            value={index + 1}
            label={item}
            isActive={index <= activeIndex}
            isSelected={index === activeIndex}
          />
        ))}
        <ScrollBar orientation="horizontal" className="" />
      </div>
    </ScrollArea>
  );
}
