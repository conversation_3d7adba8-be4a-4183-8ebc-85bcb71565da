import type { BoDirOfficerType } from "~/lib/types/bo-dir-officer-type";
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";

type DirectorType = "INDIVIDUAL" | "CORPORATE";

export function getVisibleBOColumns(officerType: BoDirOfficerType): Array<keyof BeneficialOwnerDTO> {
  switch (officerType) {
    // Nevis BO
    case "KNTP01": // Individual BO
      return ["name", "officerTypeName", "dateOfBirth", "countryOfBirth", "nationality", "tin", "residentialAddress"];
    case "KNTP02":
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address"];
    case "KNTP03":
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address"];
    case "KNTP04":
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address"];
    case "KNTP05":
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address"];
    case "KNTP06":
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address"];
    // BVI BO
    case "VGTP01": // Individual BO
      return ["name", "officerTypeName", "dateOfBirth", "countryOfBirth", "nationality", "tin", "residentialAddress"];
    case "VGTP02":
    case "VGTP06": // Corporate BO, VGTP02 and VGTP06
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address"];
    case "VGTP03": // Corporate BO VGTP03
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address", "jurisdictionOfRegulator", "nameOfRegulator"];
    case "VGTP04": // Corporate BO VGTP04
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address", "sovereignState"];
    case "VGTP05": // Corporate BO VGTP05
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address", "stockExchange", "stockCode"];
    default:
      return ["name", "officerTypeName", "incorporationNumber", "dateOfIncorporation", "countryOfFormation", "tin", "address"];
  }
}

export function getVisibleDirColumns(directorType: DirectorType): Array<keyof DirectorDTO> {
  switch (directorType) {
    case "INDIVIDUAL":
      return [
        "name",
        "officerTypeName",
        "dateOfBirth",
        "countryOfBirth",
        "nationality",
        "appointmentDate",
        "residentialAddress",
        "serviceAddress",
      ];
    case "CORPORATE":
      return [
        "name",
        "officerTypeName",
        "incorporationNumber",
        "dateOfIncorporation",
        "incorporationCountry",
        "appointmentDate",
        "address",
        "serviceAddress",
      ];
  }
}
