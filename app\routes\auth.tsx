import type { JS<PERSON> } from "react";
import { Link, Outlet } from "@remix-run/react";
import { Ellipsis } from "lucide-react";
import { Logo } from "~/components/ui/branding/Logo";

export default function Layout(): JSX.Element {
  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex grow items-center justify-center bg-cover bg-auth">
        <div className="w-[458px] bg-white shadow-md p-11 mx-auto mb-7">
          <Logo className="h-6 mb-2" />
          <Outlet />
        </div>
      </main>

      <footer className="flex justify-end bg-blue-800 text-white h-7 fixed bottom-0 left-0 w-full">
        <div className="flex justify-between w-auto">
          <div className="flex items-center">
            <Link
              to="/privacy-policy"
              target="_blank"
              className="w-auto text-xs/7 mx-2 hover:underline"
            >
              Privacy
            </Link>
            <Link
              to="/terms"
              target="_blank"
              className="w-auto text-xs/7 mx-2 hover:underline"
            >
              Terms
            </Link>
          </div>

          <div className="flex items-center mx-2">
            <Link to="#">
              <Ellipsis size={22} strokeWidth={1.5} />
            </Link>
          </div>
        </div>
      </footer>
    </div>
  )
}
