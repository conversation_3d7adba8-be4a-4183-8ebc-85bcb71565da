import { z } from "zod";

export const fileErrorMessage = "The file is required and should not be empty";

export function createFileSchema(options?: { optional?: boolean }) {
  if (options?.optional) {
    // For optional files, allow empty arrays or arrays with files
    return z.object({
      files: z.array(z.instanceof(File)).default([]),
    });
  }

  // For required files, ensure they are provided and not empty
  return z.object({
    files: z.array(z.instanceof(File), { required_error: fileErrorMessage })
      .min(1, { message: fileErrorMessage }),
  });
}

// Default required schema for backward compatibility
export const fileSchema = createFileSchema({ optional: false });

export type FileSchemaType = z.infer<typeof fileSchema>;

// Create a type that works for both optional and required
export type OptionalFileSchemaType = z.infer<ReturnType<typeof createFileSchema>>;
