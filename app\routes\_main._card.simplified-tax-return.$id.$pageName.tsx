import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import type { JSX } from "react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { requireValidPage, useCurrentStep } from "~/lib/simplified-tax-return/hooks/use-form-steps";
import { getFormAction } from "~/lib/simplified-tax-return/utilities/form-action.server";
import { getFormLoader } from "~/lib/simplified-tax-return/utilities/form-loader.server";
import type { PageSlug } from "~/lib/simplified-tax-return/utilities/form-pages";
import type { Submission } from "~/features/submissions/api/get-submission";

const title = "Submission for" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Simplified Tax Returns",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function action(actionArgs: ActionFunctionArgs): Promise<TypedResponse<null> | undefined> {
  const { page } = requireValidPage(actionArgs.params);

  return getFormAction(actionArgs, page);
}

export async function loader(loaderArgs: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  submission: Submission
  page: PageSlug
}> {
  const { page } = requireValidPage(loaderArgs.params);

  return getFormLoader(loaderArgs, page);
}

export default function STRFormContainer(): JSX.Element | never {
  const { submission: { financialYear }, page } = useLoaderData<typeof loader>();
  const currentStep = useCurrentStep(financialYear, page);

  if (!currentStep) {
    throw new Error("Current step is not available");
  }

  return currentStep.component();
}
