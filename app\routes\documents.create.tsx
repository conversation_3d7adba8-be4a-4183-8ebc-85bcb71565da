import type {
  ActionFunctionArgs,
  TypedResponse,
} from "@remix-run/node";
import {
  unstable_createMemoryUploadHand<PERSON> as createMemoryUpload<PERSON>andler,
  unstable_parseMultipartFormData as parseMultipartFormData,
} from "@remix-run/node";
import { redirect } from "@remix-run/react";
import { middleware } from "~/lib/middlewares.server";
import { createDocument } from "~/services/api-generated";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { getDocumentType } from "~/lib/utilities/files";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function action({ request }: ActionFunctionArgs): Promise<string | string[] | TypedResponse<never>> {
  const session = await getSession(request.headers.get("Cookie"));
  await middleware(["auth", "terms", "requireCompany"], request);

  const maxPartSize = 5 * 1024 * 1024; // 5MB in bytes
  const uploadHandler = createMemoryUploadHandler({
    maxPartSize,
  });
  const formData = await parseMultipartFormData(request, uploadHandler);
  // Handling files
  const files = formData.getAll("files") as File[]; // Case for multiple files
  const location = formData.get("location") as string || "/"; // used to redirect the user and show the error notification in case of error
  // Validate whether it's a single file or multiple files

  if (!files || files.length === 0) {
    // If no files were uploaded
    session.flash("notification", { title: "Error!", message: "No files uploaded.", variant: "error" });

    return redirect(location, {
      headers: { "Set-Cookie": await commitSession(session) },
    });
  }

  const results: string[] = [];

  for (const currentFile of files) {
    // Process each file individually
    const { data, error } = await createDocument({
      headers: await authHeaders(request),
      body: { File: currentFile, Type: getDocumentType(currentFile) },
    });

    if (error) {
      // Handle upload error for the current file
      session.flash("notification", {
        title: "Error!",
        message: `Error uploading file ${currentFile.name}: ${error.exceptionMessage}`,
        variant: "error",
      });

      return redirect(location, {
        headers: { "Set-Cookie": await commitSession(session) },
      });
    }

    // Push the successful result into the results array
    results.push(data);
  }

  // Return an array of data for multiple files
  return results;
}
