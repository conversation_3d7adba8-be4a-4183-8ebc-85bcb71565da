import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Tit<PERSON> } from "@netpro/design-system";
import { useNavigate } from "@remix-run/react";
import type { SubmissionModel } from "~/routes/_main._card.economic-substance.submissions._index";

type Props = {
  submission: SubmissionModel
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ReopenSubmissionDialog({ submission, open, onOpenChange }: Props) {
  const navigate = useNavigate();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Re-Opened Submission</DialogTitle>
        </DialogHeader>
        <div>
          <p>
            Please note the following message from your Trident Trust
            Officer:
          </p>
          <p>
            {submission.reopenRequestComments}
          </p>
        </div>
        <DialogFooter className="pt-4">
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button type="button" onClick={() => navigate(`/economic-substance/${submission.id}`)}>
            Continue
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
