import { z } from "zod";
import { nonEmptyString, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";

export const entityDetailsSchema = z.object({
  entityId: stringNumber({ invalidTypeMessage: "The Entity Unique ID is required." }),
  entityTin: z.string().optional(),
  sameAddress: stringBoolean(),
  streetNumberNameCity: nonEmptyString("The Street Number/Name & City"),
  aptUnit: z.string().optional(),
  country: nonEmptyString("Country"),
})

export type EntityDetailsSchemaType = z.infer<typeof entityDetailsSchema>;
