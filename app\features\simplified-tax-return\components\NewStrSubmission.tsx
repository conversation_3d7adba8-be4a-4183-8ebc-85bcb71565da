import { <PERSON><PERSON> } from "@netpro/design-system";
import { Link, useFetcher } from "@remix-run/react";
import { ChevronRight, FileText } from "lucide-react";
import type { JSX } from "react";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import { SearchResultsList } from "~/components/searchable-list/SearchResultsList";
import { WideContainer } from "~/components/ui/utilities/WideContainer";

type NewStrSubmissionProps = {
  availableYears: number[] | null
}

export default function NewStrSubmission({
  availableYears,
}: NewStrSubmissionProps): JSX.Element {
  const fetcher = useFetcher();
  const handleSelectYear = (year: string): void => {
    const formData = { year };
    fetcher.submit({ data: JSON.stringify(formData) }, {
      method: "post",
    });
  };

  return (
    <div className="flex w-full justify-center pt-5">
      { availableYears && availableYears.length > 0 && (
        <WideContainer>
          <div className="py-2">
            <h2 className="text-gray-600 font-semibold text-xl">Select Financial Year for new submission</h2>
          </div>
          <div className="p-4 bg-gray-100 rounded-md">
            <SearchResultsList
              items={availableYears?.map((year) => {
                return {
                  id: year.toString(),
                  title: year.toString(),
                  subtitle: `Prepare a Simplified Tax Return for the year ${year}`,
                }
              }) || []}
              onSelect={handleSelectYear}
            />
          </div>
        </WideContainer>
      )}
      { (!availableYears || availableYears.length === 0) && (
        <CenteredMessage title="No new submissions available." IconComponent={FileText}>
          <Button asChild variant="outline">
            <Link to="/simplified-tax-return/drafts">
              View drafts
              <ChevronRight className="w-6" />
            </Link>
          </Button>
          <span className="font-semibold">or</span>
          <Button asChild variant="default" className="inline-flex gap-1">
            <Link to="/simplified-tax-return/submissions">
              View submissions
              <ChevronRight className="w-6" />
            </Link>
          </Button>
        </CenteredMessage>
      )}
    </div>
  )
}
