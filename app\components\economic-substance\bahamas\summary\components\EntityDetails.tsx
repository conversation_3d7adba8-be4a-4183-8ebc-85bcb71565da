import { useLoaderData } from "@remix-run/react";
import type { FinancialPeriodSchemaType } from "~/lib/economic-substance/types/bahamas/financial-period-schema";
import { dayMonthYearFormat, formatDate } from "~/lib/utilities/format";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.$id.summary";

export function EntityDetails() {
  const { submissionData, entityDetails } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { legalEntityName, companyIdentityCode, masterClientCode, status, submittedAt } = entityDetails
  // Since this is Bahamas economic substance, use Bahamas timezone
  const submissionDate = formatDate(submittedAt, { timezone: "Bahamas", formatStr: dayMonthYearFormat })
  const { startDate, endDate } = submissionData[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType
  const financialPeriodStart = formatDate(startDate, { timezone: "Bahamas", formatStr: dayMonthYearFormat })
  const financialPeriodEnd = formatDate(endDate, { timezone: "Bahamas", formatStr: dayMonthYearFormat })

  return (
    <div>
      <h2 className="text-blue-700 font-bold mb-4">Entity Details</h2>
      <div className="border-2 border-blue-200 p-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <span>Company Name: </span>
              <span className="font-semibold">
                {legalEntityName}
              </span>
            </div>
            <div>
              <span>Company Number: </span>
              <span className="font-semibold">
                {companyIdentityCode}
              </span>
            </div>
            <div>
              <span>Master Client Code: </span>
              <span className="font-semibold">
                {masterClientCode}
              </span>
            </div>
            <div>
              <span>Registered Agent: </span>
              <div className="mt-1">
                <span className="font-semibold">
                  Trident Trust Company (Bahamas) Limited
                </span>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <span>Reporting period:</span>
              <div className="mt-3 font-semibold">
                {`${financialPeriodStart} - ${financialPeriodEnd}`}
              </div>
            </div>
            <div className="mt-4">
              <span>Date submitted: </span>
              <span className="font-semibold">
                {submissionDate}
              </span>
            </div>
            <div className="mt-4">
              <span>Status: </span>
              <span className="font-bold">{status}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

  )
}
