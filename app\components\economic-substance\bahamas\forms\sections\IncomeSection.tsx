import { FormControl, FormField, FormItem, FormLabel, FormMessage, Textarea } from "@netpro/design-system";
import { useNavigation } from "@remix-run/react";
import { useFormContext } from "react-hook-form";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import type { IncomeSchemaType } from "~/lib/economic-substance/types/bahamas/income-schema";
import { Currency } from "~/lib/economic-substance/utilities/currencies";

export function IncomeSection() {
  const form = useFormContext<IncomeSchemaType>()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"

  return (
    <>
      <p className="text-md font-bold">Income</p>
      <FormField
        control={form.control}
        name="totalGrossIncome"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Total Gross Income for the relevant activity during the financial period.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <CurrencyInput
                currencyName={Currency.USD}
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="netBookValuesAssets"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Net book values of tangible assets,
                equipment or physical assets held in the course of carrying out the relevant activity.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <CurrencyInput
                currencyName={Currency.USD}
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="assetsDescriptionBahamas"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Please provide a description of nature of any equipment
                and other tangible or physical assets located within the Bahamas used in connection with the relevant activity.*
              </p>
            </FormLabel>
            <FormControl className="md:w-2/3 sm:w-full">
              <Textarea
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  )
}
