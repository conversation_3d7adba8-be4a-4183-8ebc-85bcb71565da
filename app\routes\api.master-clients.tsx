import type { ActionFunction, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { getCompanies } from "~/features/companies/api/get-companies";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import type { MasterClientsSearchResultsDTO } from "~/services/api-generated";
import { clientGetMasterClients } from "~/services/api-generated";
import type { BasicMasterClient } from "~/features/master-clients/types/generic";
import { middleware } from "~/lib/middlewares.server";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<MasterClientsSearchResultsDTO | undefined>> {
  await middleware(["auth", "mfa", "terms"], request);
  const url = new URL(request.url);
  const search = url.searchParams.get("search") || "";
  const { data: masterClients, error } = await clientGetMasterClients({ headers: await authHeaders(request), query: {
    search,
  } });

  if (error || !masterClients) {
    return json({ masterClients: [] }, {
      status: error ? 500 : 404,
    });
  }

  return json(masterClients);
}

export const action: ActionFunction = async ({ request }) => {
  await middleware(["auth", "mfa", "terms"], request);
  const formData = new URLSearchParams(await request.text());
  const masterClientId = formData.get("id");
  const action = formData.get("actionType");
  if (!masterClientId) {
    return json({ error: "No Master Client selected" }, { status: 400 });
  }

  const session = await getSession(request.headers.get("Cookie"));
  const userId = session.get("userId") as string;
  const accessToken = session.get("accessToken") as string;
  const { data, error } = await clientGetMasterClients({ headers: await authHeaders(request) });

  if (error || !data?.masterClients || !data.masterClients.length) {
    return json({ error: "Failed to get Master Clients" }, {
      status: error ? 500 : 400,
    });
  }

  const existMasterClient: BasicMasterClient | undefined = data.masterClients
    .map(client => ({
      masterClientId: client.masterClientId,
      masterClientCode: client.masterClientCode,
    }))
    .find(mcc => mcc.masterClientId === masterClientId);

  if (!existMasterClient || !existMasterClient.masterClientId) {
    return json({ error: "Invalid Master Client selected" }, { status: 400 });
  }

  const companiesData = await getCompanies({
    accessToken,
    userId,
    masterClientId: existMasterClient.masterClientId,
    params: { search: "", includeInactive: true },
  });

  if (!action || action === "select") {
    session.set("currentMasterClient", {
      masterClientId: existMasterClient.masterClientId,
      masterClientCode: existMasterClient.masterClientCode,
    } as BasicMasterClient);
    session.set("currentCompany", undefined);
    session.set("companyModules", undefined);
    // TODO: Could be simplified to data.masterClients.companies.length ?
    session.set("totalMasterClientCompanies", companiesData?.companies?.length)

    return redirect("/companies", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    });
  }

  // Update was initiated by master client dialog. Queue the update to guarantee the user selects a company selected.
  session.set("queuedMasterClientUpdate", {
    masterClientId: existMasterClient.masterClientId,
    masterClientCode: existMasterClient.masterClientCode,
  } as BasicMasterClient);

  return json({ success: true, masterClientSelected: existMasterClient }, {
    status: 200,
    headers: {
      "Set-Cookie": await commitSession(session),
    },
  });
}
