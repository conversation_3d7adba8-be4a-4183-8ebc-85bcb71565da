import { <PERSON><PERSON>, Separator } from "@netpro/design-system";
import { useLocation, useNavigate, useNavigation } from "@remix-run/react";
import { Check, ChevronRight } from "lucide-react";
import type { ReactNode } from "react";
import { ProgressBar } from "~/components/forms/ProgressBar";
import { usePreviousStep } from "~/lib/economic-substance/hooks/use-form-steps";
import { ECONOMIC_SUBSTANCE_FORM_ID } from "~/lib/economic-substance/utilities/constants";
import { Pages as PagesBahamas } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { Pages as PagesBVI } from "~/lib/economic-substance/utilities/form-pages-bvi";
import { useSubmission } from "~/lib/submission/context/use-submission";

type FormLayoutProps = {
  children: ReactNode
  jurisdictionName: string
  formSteps: {
    name: string
    page: string
  }[]
};

export function FormLayout({ formSteps, jurisdictionName, children }: FormLayoutProps): ReactNode {
  const location = useLocation();
  const navigate = useNavigate();
  const navigation = useNavigation();
  const { submissionData, canContinue, setCanContinue, isSubmitting: contextIsSubmitting } = useSubmission();
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading" || contextIsSubmitting;
  const currentLocation = location.pathname;
  const previousPage = usePreviousStep(submissionData, currentLocation.split("/").pop()!, jurisdictionName)
  const stepPages = formSteps.map(step => step.page);
  const formStepIndex = stepPages.indexOf(currentLocation.split("/").pop()!);

  function handleBack(): void {
    setCanContinue(true)
    if (!previousPage) {
      return;
    }

    navigate(currentLocation.replace(stepPages[formStepIndex], previousPage));
  }

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 pt-1 pb-5">
        {formStepIndex >= 0
        && <ProgressBar items={formSteps.map(step => step.name)} activeIndex={formStepIndex} />}
        {children}
      </div>
      <div>
        <Separator orientation="horizontal" />
        <div className="flex justify-between mt-2">
          <span className="text-xs text-gray-500 my-auto">A draft will automatically be saved</span>
          <div className="flex space-x-2">
            {formStepIndex > 0 && (
              <Button variant="outline" onClick={handleBack} disabled={isSubmitting}>
                Back
              </Button>
            )}
            {currentLocation.endsWith(jurisdictionName === "BVI" ? PagesBVI.FINALIZE : PagesBahamas.FINALIZE)
              ? (
                  <Button
                    type="submit"
                    form={ECONOMIC_SUBSTANCE_FORM_ID}
                    disabled={!canContinue || isSubmitting}
                  >
                    Finalize submission
                    <Check className="ml-2 size-4 text-white" />
                  </Button>
                )
              : (
                  <Button
                    type="submit"
                    form={ECONOMIC_SUBSTANCE_FORM_ID}
                    disabled={!canContinue || isSubmitting}
                  >
                    Next
                    <ChevronRight className="ml-2 size-4 text-white" />
                  </Button>
                )}
          </div>
        </div>
      </div>
    </div>
  )
}
