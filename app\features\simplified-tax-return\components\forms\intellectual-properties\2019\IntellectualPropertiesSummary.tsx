import { Table, TableBody, Table<PERSON>ell, TableHead, TableH<PERSON>er, TableRow } from "@netpro/design-system";
import type { JSX } from "react";
import type { IntellectualPropertiesType } from "~/lib/simplified-tax-return/types/intellectual-property/2019/intellectual-property-schema";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { formatDate, formatYesNoBoolean } from "~/lib/utilities/format";

export function IntellectualPropertiesSummary(): JSX.Element {
  const { submissionData } = useSubmission();
  const intellectualProperties = submissionData[Pages.INTELLECTUAL_PROPERTIES] as IntellectualPropertiesType;

  return (
    <section id="intellectual-properties-section">
      <Table className="border border-blue-600 pointer-events-none">
        <TableBody>
          <TableRow>
            <TableCell className="w-2/3 py-1">
              <span>
                Did you acquire or otherwise obtain Intellectual Property assets from either a related party
                or non-related party during the period 2019?
              </span>
            </TableCell>
            <TableCell className="w-1/3 text-center py-1">
              <span className="font-semibold ">{formatYesNoBoolean(intellectualProperties?.intellectualPropertyAcquired)}</span>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      {intellectualProperties?.intellectualPropertyAcquired === "true" && (
        <Table className="border border-blue-600 pointer-events-none ">
          <TableHeader>
            <TableRow className="text-center">
              <TableHead className="w-1/3 text-center font-semibold text-black border border-blue-600 py-1 ">
                Description of new asset or activity
              </TableHead>
              <TableHead className="w-1/3 text-center font-semibold text-black border border-blue-600 py-1 ">
                Date asset acquired or new activity start date
              </TableHead>
              <TableHead className="w-1/3 text-center font-semibold text-black border border-blue-600 py-1 ">
                Income in the period from 2019
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {
              intellectualProperties?.assetsAcquired?.map(asset => (
                <TableRow key={asset.description}>
                  <TableCell className="w-1/3 text-center border border-blue-600 py-1 ">
                    <span>{asset.description}</span>
                  </TableCell>
                  <TableCell className="w-1/3 text-center border border-blue-600 py-1">
                    <span>{formatDate(asset.acquisitionDate as Date)}</span>
                  </TableCell>
                  <TableCell className="w-1/3 text-center border border-blue-600 py-1">
                    <span>
                      $
                      {" "}
                      {asset.income}
                    </span>
                  </TableCell>
                </TableRow>
              ))
            }
          </TableBody>
        </Table>
      )}

    </section>
  );
}
