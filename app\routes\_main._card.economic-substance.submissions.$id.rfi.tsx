import type { ReactNode } from "react";
import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eader,
  CardTitle,
  FileList,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Label,
  Form as NetProForm,
  Spinner,
  Textarea,
} from "@netpro/design-system";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import {
  unstable_createMemoryUploadHandler as createMemoryUploadHandler,
  unstable_parseMultipartFormData as parseMultipartFormData,
} from "@remix-run/node";
import {
  Form as RemixForm,
  json,
  redirect,
  useLoaderData,
  useNavigate,
  useNavigation,
  useSubmit,
} from "@remix-run/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type {
  ClientRfiDocumentCreateData,
  CompleteRequestForInformationDTO,
  GetApiV1CommonDocumentsResponse,
} from "~/services/api-generated";
import {
  clientGetSubmissionRfiDetails,
  clientRfiCcomplete,
  clientRfiDocumentCreate,
  getApiV1CommonDocuments,
} from "~/services/api-generated";
import { middleware } from "~/lib/middlewares.server";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import type { RfiSchemaType } from "~/lib/economic-substance/types/bahamas/rfi-schema";
import { rfiSchema } from "~/lib/economic-substance/types/bahamas/rfi-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { FileUploader } from "~/components/economic-substance/bahamas/dropzones/FileUploader";
import { fileDataToUrl } from "~/lib/utilities/files";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";

const title = "Submissions for" as const;
const breadCrumbList = [
  {
    href: "/economic-substance",
    name: "Economic Substance",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

const MAX_FILES = 4

export async function loader({ request, params }: LoaderFunctionArgs) {
  await middleware(["auth", "requireEsModule"], request)
  const { id } = params
  if (!id) {
    throw new Response("The Id is required to get the Request for Information details", { status: 404 });
  }

  // RFI Logic
  const { data: rfiData, error: rfiError } = await clientGetSubmissionRfiDetails({
    headers: await authHeaders(request),
    query: { submissionId: id },
  })

  if (!rfiData) {
    throw new Response("Request for information data not found", { status: 404 })
  }

  const status = rfiData.status

  if (status !== SubmissionStatusNames.InformationRequested) {
    throw new Response("Status should be request for information", { status: 403 })
  }

  const requestsForInformation = rfiData.requestsForInformation

  if (!requestsForInformation) {
    throw new Response("Request for information not found", { status: 404 })
  }

  if (rfiError) {
    throw new Response(rfiError, { status: 500 })
  }

  const documentIds = requestsForInformation[0].documents?.filter(doc => doc.createdByManagement === true).map(doc => doc.documentId)
  let rfiDocuments: GetApiV1CommonDocumentsResponse | undefined
  // Documents logic
  if (documentIds && documentIds.length > 0) {
    const {
      data: documentsData,
      error: documentsError,
    } = await getApiV1CommonDocuments({
      headers: await authHeaders(request),
      query: { documentIds: documentIds as string[], includeData: true },
    })

    if (!documentsData) {
      throw new Response("Documents data not found", { status: 404 })
    }

    if (documentsError) {
      throw new Response(documentsError, { status: 500 })
    }

    rfiDocuments = documentsData
  }

  return json({ rfiData: requestsForInformation, rfiDocuments });
}

export async function action({ request, params }: ActionFunctionArgs) {
  await middleware(["auth", "requireEsModule"], request)
  const session = await getSession(request.headers.get("Cookie"));
  const { id } = params
  if (!id) {
    throw new Response("The Id is required to complete the Request for Information", { status: 404 });
  }

  const maxPartSize = 5 * 1024 * 1024; // 5MB in bytes
  const uploadHandler = createMemoryUploadHandler({
    maxPartSize,
  });
  const formData = await parseMultipartFormData(request, uploadHandler);
  const { response } = Object.fromEntries(formData) as CompleteRequestForInformationDTO
  const files = formData.getAll("files") as File[];
  const headers = await authHeaders(request);
  // Processing of info to get the requestForInformationId
  const { data: rfiData, error: rfiError } = await clientGetSubmissionRfiDetails({
    headers,
    path: { submissionId: id },
  })

  if (!rfiData) {
    throw new Response("Request for information data not found", { status: 404 })
  }

  const requestsForInformation = rfiData.requestsForInformation

  if (!requestsForInformation) {
    throw new Response("Request for information not found", { status: 404 })
  }

  if (rfiError) {
    throw new Response(rfiError, { status: 500 })
  }

  const { error: rfiCompleteError } = await clientRfiCcomplete({
    headers,
    path: { requestForInformationId: requestsForInformation[0].id as string },
    body: {
      response,
      includeAttachments: files.length > 0,
    },
  })

  if (rfiCompleteError) {
    throw new Response(rfiCompleteError.exceptionMessage as string, { status: 500 })
  }

  if (files.length > 0) {
    const formattedFiles: ClientRfiDocumentCreateData["body"][] = files.map((file, index) => ({
      File: file,
      UploadComplete: index === files.length - 1,
    }));
    // Create an array of promises for all file uploads
    const uploadPromises = formattedFiles.map(formattedFile =>
      clientRfiDocumentCreate({
        headers,
        path: { requestForInformationId: requestsForInformation[0].id as string },
        body: formattedFile,
      }),
    );
    // Wait for all uploads to complete concurrently
    await Promise.all(uploadPromises);
  }

  session.flash("notification", { title: "The requested information has been uploaded", variant: "success" });

  return redirect("/economic-substance/submissions", { headers: { "Set-Cookie": await commitSession(session) } });
}

export default function EconomicSubstanceBahamasRfi(): ReactNode {
  const { rfiData, rfiDocuments } = useLoaderData<typeof loader>()
  const { comments } = rfiData[0]
  const navigate = useNavigate();
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting"
  const submit = useSubmit()
  const [files, setFiles] = useState<{ fileName: string, url: string }[]>([])
  const form = useForm<RfiSchemaType>({
    resolver: zodResolver(rfiSchema),
    defaultValues: {
      response: "",
      files: [],
    },
  })
  const onSubmit = (data: RfiSchemaType) => {
    const formData = new FormData()

    formData.append("response", data.response)
    const filesData = data.files
    if (filesData && filesData.length > 0) {
      filesData.forEach((fileData) => {
        formData.append("files", fileData)
      });
    }

    submit(formData, { encType: "multipart/form-data", method: "post" })
  }

  useEffect(() => {
    if (rfiDocuments) {
      const urls: { fileName: string, url: string }[] = []
      rfiDocuments.forEach((doc) => {
        const url = fileDataToUrl(doc)
        urls.push({ fileName: doc.filename as string, url })
      });

      setFiles(urls)
    }
  }, [rfiDocuments])

  return (
    <CardContent className="w-full">
      <CardHeader className="px-0">
        <CardTitle>
          Information Requested regarding your submission
        </CardTitle>

      </CardHeader>
      <NetProForm {...form}>
        <RemixForm
          onSubmit={form.handleSubmit(onSubmit)}
          method="post"
          noValidate
          className="space-y-7"
        >
          <div>
            <Label>Request details</Label>
            <Textarea
              defaultValue={comments as string}
              disabled
            />
          </div>
          <div>
            <Label>Attachments</Label>
            <ul className="list-disc text-blue-500 pl-10">
              {files.map(file => (
                <li key={file.fileName}>
                  <LinkButton
                    linkProps={{
                      to: file.url,
                      reloadDocument: true,
                      target: "_blank",
                      rel: "noopener noreferrer",
                    }}
                    buttonProps={{
                      disabled: isSubmitting,
                      variant: "link",
                      type: "button",
                    }}
                  >
                    {file.fileName}
                  </LinkButton>
                </li>
              ))}
            </ul>
          </div>
          <FormField
            control={form.control}
            name="response"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>Please provide your response*</FormLabel>
                <FormControl>
                  <Textarea
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="files"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Optional: Add document(s)
                </FormLabel>
                <FormControl>
                  <>
                    <FileUploader
                      maxFiles={MAX_FILES}
                      files={field.value}
                      allowedTypes={["application/pdf"]}
                      setFiles={field.onChange}
                      disabled={isSubmitting}
                    >
                      <div className="flex flex-col gap-1">
                        <p>
                          Drag and drop some files here, or click to select files
                        </p>
                        <p className="text-sm text-gray-400">
                          Maximum of 4 File(s), PDF only, File must not be password protected.
                          Files will be automatically uploaded
                        </p>
                      </div>
                    </FileUploader>
                    <FileList
                      files={field.value}
                      setFiles={field.onChange}
                    />
                  </>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <CardFooter className="pt-4 flex justify-end gap-2">
            <Button
              disabled={isSubmitting}
              variant="outline"
              type="button"
              onClick={() => navigate("/economic-substance/submissions")}
            >
              Cancel
            </Button>
            <Button
              disabled={isSubmitting}
              type="submit"
            >
              {isSubmitting ? <Spinner className="size-4 mx-0 text-white" /> : "Confirm"}
            </Button>
          </CardFooter>
        </RemixForm>
      </NetProForm>
    </CardContent>

  )
}
