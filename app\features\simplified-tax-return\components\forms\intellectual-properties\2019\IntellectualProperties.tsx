import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alog<PERSON>eader, DialogTitle, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, RadioGroup, RadioGroupItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import { type JSX, useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { IntellectualPropertyAssetDialog } from "./IntellectualPropertyAssetDialog";
import { IntellectualPropertyAssetsList } from "./IntellectualPropertyAssetsList";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import type { IntellectualPropertiesType, IntellectualPropertyAssetType } from "~/lib/simplified-tax-return/types/intellectual-property/2019/intellectual-property-schema";
import { intellectualPropertiesSchema, intellectualPropertyAssetSchema } from "~/lib/simplified-tax-return/types/intellectual-property/2019/intellectual-property-schema";
import { WideContainer } from "~/components/ui/utilities/WideContainer";

export function IntellectualProperties(): JSX.Element {
  const { submissionData, financialYear } = useSubmission();
  const data = useMemo(() => {
    const pageData = submissionData[Pages.INTELLECTUAL_PROPERTIES];
    if (pageData?.assetsAcquired === "") {
      pageData.assetsAcquired = [];
    }

    return pageData as IntellectualPropertiesType;
  }, [submissionData]);
  const [open, setOpen] = useState(false);
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [acquiredAsset, setAcquiredAsset] = useState<IntellectualPropertyAssetType | undefined>();
  const [assetIndex, setAssetIndex] = useState<number | undefined>();
  const form = useForm<IntellectualPropertiesType>({
    resolver: zodResolver(intellectualPropertiesSchema),
    shouldFocusError: false,
    defaultValues: {
      assetsAcquired: [],
    },
  });
  const acquiredAssetForm = useForm<IntellectualPropertyAssetType>({
    resolver: zodResolver(intellectualPropertyAssetSchema),
    defaultValues: {
      description: "",
    },
  });
  const {
    fields,
    append,
    remove,
    update,
  } = useFieldArray({
    control: form.control,
    name: "assetsAcquired",
    keyName: "formArrayId",
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  function addAcquiredAsset(): void {
    setAcquiredAsset(undefined);
    acquiredAssetForm.reset();
    setOpen(true);
  }

  function onSubmitAsset(data: IntellectualPropertyAssetType): void {
    if (acquiredAsset && assetIndex !== undefined) {
      update(assetIndex, data);
    } else {
      append(data);
    }

    form.trigger();
    setOpen(false);
  }

  function onSelect(asset: IntellectualPropertyAssetType, index: number): void {
    setAcquiredAsset(asset);
    acquiredAssetForm.reset(asset, { keepDefaultValues: true });
    setAssetIndex(index);
    setOpen(true);
  }

  function onDelete(): void {
    remove(assetIndex);
    form.trigger();
    setOpenDeleteConfirmation(false);
  }

  const fetcher = useFetcher();

  function onSubmit(data: IntellectualPropertiesType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  function onOpenDeleteConfirmation(): void {
    setOpen(false);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setOpen(true);
    setOpenDeleteConfirmation(false);
  }

  const intellectualPropertyAcquired = form.watch("intellectualPropertyAcquired");

  return (
    <>
      <IntellectualPropertyAssetDialog
        setOpen={setOpen}
        open={open}
        form={acquiredAssetForm}
        onSubmit={onSubmitAsset}
        asset={acquiredAsset}
        onDelete={onOpenDeleteConfirmation}
        year={financialYear!}
      />
      <Dialog open={openDeletedConfirmation} onOpenChange={setOpenDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete the asset?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation}>Cancel</Button>
            <Button type="button" variant="destructive" onClick={onDelete}>Yes, delete this asset</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} id="str-form" noValidate>
          <div className="flex-col space-y-5">
            <FormField
              control={form.control}
              name="intellectualPropertyAcquired"
              render={({ field, fieldState }) => (
                <FormItem className="w-full">
                  <Tooltip delayDuration={0}>
                    <FormLabel className="flex">
                      Did you acquire or otherwise obtain Intellectual Property assets from either a related party or non-related party during the period 2019? *
                      <TooltipTrigger asChild>
                        <Info size={16} className="ml-1" />
                      </TooltipTrigger>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 font-inter" side="bottom">
                      <span className="font-bold">
                        Intellectual Property:
                      </span>
                      <p>
                        A category of property that includes intangible creations of the human intellect.
                      </p>
                      <p>
                        Examples include but are not limited to copyrights, trademarks, patents, geographical indications, trade secrets etcetera.
                      </p>
                    </TooltipContent>
                  </Tooltip>

                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      invalid={!!fieldState.error}
                      value={field.value}
                      orientation="horizontal"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {intellectualPropertyAcquired === "true" && (
              <WideContainer className="md:mx-0">
                <FormField
                  name="assetsAcquired"
                  control={form.control}
                  render={({ fieldState }) => (
                    <FormItem>
                      <FormLabel>Assets Acquired</FormLabel>
                      {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                      <FormControl>
                        <IntellectualPropertyAssetsList
                          assets={fields}
                          onSelect={onSelect}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="flex justify-end pt-5">
                  <Button size="sm" onClick={addAcquiredAsset} type="button">
                    <Plus className="mr-2 size-4 text-white" />
                    Add Asset
                  </Button>
                </div>
              </WideContainer>
            )}
          </div>
        </RemixForm>
      </Form>
    </>
  )
}
