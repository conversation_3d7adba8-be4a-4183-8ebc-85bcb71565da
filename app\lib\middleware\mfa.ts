import type { TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { Method } from "../auth/utils/mfa";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";
import { getMfaMethod } from "~/features/mfa/api/get-mfa-method";

type MFAData = {
  mfaCompleted: boolean
};

export default async function mfa({ request }: MiddlewareProps): MiddlewareResponse<TypedResponse<never> | MFAData> {
  const { mfaCompleted, userId, accessToken } = await getSessionData(request);
  const session = await getSession(request.headers.get("Cookie"));

  if (!userId || !accessToken) {
    throw redirect("/login");
  }

  if (!mfaCompleted) {
    // Check MFA method and redirect to the appropriate route
    const mfa = await getMfaMethod({ userId, accessToken });
    session.set("mfaMethod", mfa.mfaMethod);
    if (mfa.mfaMethod === Method.None) {
      // No MFA configured yet, redirect to MFA setup
      throw redirect("/auth/mfa/set-up", { headers: { "Set-Cookie": await commitSession(session) } });
    }

    if (mfa.mfaMethod === Method.Email) {
      throw redirect("/auth/mfa/email", { headers: { "Set-Cookie": await commitSession(session) } });
    }

    if (mfa.mfaMethod === Method.Authenticator) {
      throw redirect("/auth/mfa/authenticator/code", { headers: { "Set-Cookie": await commitSession(session) } });
    }
  }

  return { mfaCompleted: Boolean(mfaCompleted) };
}
