import { z } from "zod";
import { preprocessArray, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";

export enum PropertyType {
  AIRCRAFT = "Aircraft",
  BUILDING = "Building",
  EQUIPMENT = "Equipment",
  LAND = "Land",
  MOTOR_VEHICLE = "Motor Vehicle",
  VESSEL = "Vessel",
  OTHER = "Other, please specify",
}

export const InitialPaymentType = {
  CASH: "Cash",
  PROPERTY: "Property",
  SECURITY_INVESTMENT: "Security Investment",
}

export const propertySchema = z.object({
  type: z.enum(Object.values(PropertyType) as [string, ...string[]], {
    message: "Please select a property type",
  }),
  otherType: z.string({
    required_error: "Please specify the other property type",
  }).optional(),
  value: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0 }),
}).refine(data => !(data.otherType === PropertyType.OTHER && (!(data.otherType && data.otherType.trim() === ""))), {
  message: "Please specify the other property type",
  path: ["otherType"],
})

export const equityDetailsSchema = z.object({
  noAuthorizedShares: stringNumber({ invalidTypeMessage: "Number of authorized shares is required.", greaterThan: 0, allowDecimal: false }),
  noIssuedShares: stringNumber({ invalidTypeMessage: "Number of issued shares is required.", greaterThan: 0, allowDecimal: false }),
  parValuePerShare: stringNumber({ invalidTypeMessage: "Par value per shares is required.", greaterThan: 0 }),
  initialPaymentType: z.array(z.enum(Object.values(InitialPaymentType) as [string, ...string[]])).refine(data => data.length > 0, { message: "At least one payment type must be provided." }),
  paidInCash: stringBoolean().optional(),
  cashAmount: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0 }).optional(),
  propertyTransfer: stringBoolean().optional(),
  properties: preprocessArray(z.array(propertySchema)).optional(),
  securitiesTransfer: stringBoolean().optional(),
  securitiesValue: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0 }).optional(),
})
  .refine(data => !(data.initialPaymentType.includes(InitialPaymentType.CASH) && data.paidInCash && !data.cashAmount), {
    message: "Please specify an amount",
    path: ["cashAmount"],
  })
  .refine(data => !(data.initialPaymentType.includes(InitialPaymentType.PROPERTY) && data.propertyTransfer && !data.properties?.length), {
    message: "Please add at least one item",
    path: ["properties", 0],
  })
  .refine(data => !(data.initialPaymentType.includes(InitialPaymentType.SECURITY_INVESTMENT) && data.securitiesTransfer && !data.securitiesValue), {
    message: "Please specify an amount",
    path: ["securitiesValue"],
  })

export type PropertySchemaType = z.infer<typeof propertySchema>;
export type EquityDetailsSchemaType = z.infer<typeof equityDetailsSchema>;
