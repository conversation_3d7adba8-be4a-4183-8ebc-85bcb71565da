import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>ooter, DialogHeader, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input, ScrollArea, ScrollBar, Textarea } from "@netpro/design-system";
import type { UseFormReturn } from "react-hook-form";
import type { JSX } from "react";
import type { IntellectualPropertyAssetType } from "~/lib/simplified-tax-return/types/intellectual-property/2019/intellectual-property-schema";

type IntellectualPropertyAssetsDialogProps = {
  asset?: IntellectualPropertyAssetType
  open: boolean
  setOpen: (open: boolean) => void
  onDelete?: () => void
  onSubmit: (data: IntellectualPropertyAssetType) => void
  form: UseFormReturn<IntellectualPropertyAssetType>
  year: number
};

export function IntellectualPropertyAssetDialog({ asset, open, setOpen, onSubmit, onDelete, form, year }: IntellectualPropertyAssetsDialogProps): JSX.Element {
  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-md"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="p-2" noValidate id="asset-dialog-form">
              <DialogHeader>
                <p>Intellectual Property Asset</p>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Description of new asset or activity *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter description..."
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="acquisitionDate"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Date asset acquired or new activity start date *</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          onChange={field.onChange}
                          invalid={!!fieldState.error}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="income"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>{`Income in the period from ${year} *`}</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          type="number"
                          onChange={field.onChange}
                          defaultValue={field.value}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="pt-4">
                <div className={`flex w-full ${asset && onDelete ? "justify-between" : "justify-end"}`}>
                  {asset && onDelete && (
                    <Button size="sm" variant="destructive" onClick={onDelete} type="button">Delete</Button>
                  )}
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form="asset-dialog-form">Save Asset</Button>
                  </div>
                </div>
              </DialogFooter>
            </form>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
