import mime from "mime";
import type { DocumentDTO, DocumentType } from "~/services/api-generated";

/**
 * Decodes a base64-encoded file data and returns a Blob, filename, and MIME type.
 * @param {DocumentDTO} fileData  - The file data object.
 * @returns An object containing the Blob, filename, and MIME type.
 */
function decodeFileData(fileData: DocumentDTO) {
  const { documentData, filename, id } = fileData;

  if (!documentData || !filename || !id) {
    throw new Error("file data must have document data, a filename, and an ID");
  }

  // Decode base64 string (ensure documentData is in correct base64 format)
  const byteCharacters = atob(documentData.split(",")[1] || documentData);
  const byteNumbers: number[] = Array.from(byteCharacters, char => char.charCodeAt(0));
  const byteArray = new Uint8Array(byteNumbers);
  // Get MIME type from filename using mime package
  const mimeType = mime.getType(filename) || "application/octet-stream";
  const blob = new Blob([byteArray], { type: mimeType });

  return { blob, filename, mimeType, id };
}

/**
 * Converts decoded file data to a File object.
 * @param {DocumentDTO} fileData - The file data object.
 * @returns A File object created from the decoded file data.
 */
export function fileDataToFile(fileData: DocumentDTO): File {
  const { blob, filename, mimeType, id } = decodeFileData(fileData);

  return new File([blob], filename || `no-name-file-${id}`, { type: mimeType });
}

/**
 * Converts a mapped object of DocumentDTOs to an object of Files.
 * @param {Record<string, DocumentDTO>} mappedDocuments - Object mapping keys to DocumentDTOs.
 * @returns {Record<string, File>} An object with the same keys, but where values are Files.
 */
export function convertMappedDocumentsToFileObject(mappedDocuments: Record<string, DocumentDTO>): Record<string, File> {
  if (typeof mappedDocuments !== "object" || mappedDocuments === null) {
    throw new Error("The input must be a non-null object of type Record<string, DocumentDTO>.");
  }

  const fileObject: Record<string, File> = {};
  for (const [key, document] of Object.entries(mappedDocuments)) {
    fileObject[key] = fileDataToFile(document);
  }

  return fileObject;
}

/**
 * Generates a URL from the decoded file data.
 * @param {DocumentDTO} fileData - The file data object.
 * @returns A URL created from the decoded file data.
 */
export function fileDataToUrl(fileData: DocumentDTO): string {
  const { blob } = decodeFileData(fileData);
  // Generate a URL from the Blob
  const url = URL.createObjectURL(blob);

  return url;
}

/**
 * Determines the document type based on the file's MIME type.
 * @param {File} file - The file whose type is to be determined.
 * @returns {DocumentType} The determined document type:
 * "Image" for image files, "Pdf" for PDF files, "Xls" for Excel files,
 * or "Unknown" for unsupported file types.
 */
export function getDocumentType(file: File): DocumentType {
  const mimeType = file.type;

  if (mimeType.startsWith("image/")) {
    return "Image";
  } else if (mimeType === "application/pdf") {
    return "Pdf";
  } else if (mimeType === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
    return "Xls";
  } else {
    return "Unknown";
  }
}

// Function to group documents by prefix
export function groupDocuments(prefix: string, documentsArray: Record<string, File>) {
  return Object.keys(documentsArray)
    .filter(key => key.includes(prefix)) // Filter keys by the prefix
    .sort() // Sort keys to maintain index order
    .map(key => documentsArray[key]); // Map the corresponding values
}

/**
 * Removes all keys from the input object that belong to the same category
 * as the provided reference key. A category is determined by the prefix
 * of the key, which is separated by underscores (`_`), excluding the last segment.
 *
 * Expected format:
 * - Keys must be separated by underscores (`_`).
 * - The last segment of the key indicates the index or differentiator (e.g., `documentId_businessLicense_0`).
 * - The category is everything before the last underscore (e.g., `documentId_businessLicense`).
 *
 * Example:
 * ```typescript
 * const inputObject = {
 *   "documentId_businessLicense_0": "b72a533f-701e-4cfc-9ff1-954250aaf39c",
 *   "documentId_businessLicense_1": "d83b533f-701e-4cfc-9ff1-954250aaf39d",
 *   "documentId_taxPayerEvidence_0": "c72a533f-701e-4cfc-9ff1-954250aaf39e",
 *   "documentId_asdasdasdasdsa_sdasxddd_0": "a72a533f-701e-4cfc-9ff1-954250aaf39f",
 *   "documentId_asdasdasdasdsa_sdasxddd_1": "a82a533f-701e-4cfc-9ff1-954250aaf39g"
 * };
 *
 * const updatedObject = removeCategoryKeys(inputObject, "documentId_businessLicense_0");
 *
 * console.log(updatedObject);
 * // Output:
 * // {
 * //   "documentId_taxPayerEvidence_0": "c72a533f-701e-4cfc-9ff1-954250aaf39e",
 * //   "documentId_asdasdasdasdsa_sdasxddd_0": "a72a533f-701e-4cfc-9ff1-954250aaf39f",
 * //   "documentId_asdasdasdasdsa_sdasxddd_1": "a82a533f-701e-4cfc-9ff1-954250aaf39g"
 * // }
 * ```
 *
 * @param input - An object where keys are in the format `prefix_category_index`.
 * @param referenceKey - A key to identify the category to remove.
 * @returns A new object with keys from the same category as the reference key removed.
 */
export function removeCategoryKeys(input: Record<string, string> | null | undefined, referenceKey: string,
): Record<string, string> | null | undefined {
  // If the input is null or undefined, return it as is
  if (!input) {
    return input;
  }

  // Extract the base category from the reference key
  const baseCategory = referenceKey.split("_").slice(0, -1).join("_");

  return Object.fromEntries(
    Object.entries(input).filter(([key]) => {
      // Keep keys that don't match the base category
      return !key.startsWith(baseCategory);
    }),
  );
}
