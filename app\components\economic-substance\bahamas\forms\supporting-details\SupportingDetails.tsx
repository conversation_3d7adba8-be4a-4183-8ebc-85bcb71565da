import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Textarea,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useLoaderData, useNavigation, useParams } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { FileUploadDialog } from "../../dialogs/financial-period/FileUploadDialog";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { type OptionalFileSchemaType, createFileSchema } from "~/lib/economic-substance/types/bahamas/file-schema";
import type {
  RelevantActivityDeclarationSchemaType,
} from "~/lib/economic-substance/types/bahamas/relevant-activity-declaration-schema";
import type { SupportingDetailsSchemaType } from "~/lib/economic-substance/types/bahamas/supporting-details-schema";
import { supportingDetailsSchema } from "~/lib/economic-substance/types/bahamas/supporting-details-schema";
import { ECONOMIC_SUBSTANCE_FORM_ID } from "~/lib/economic-substance/utilities/constants";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { convertMappedDocumentsToFileObject, groupDocuments } from "~/lib/utilities/files";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import type { EconomicSubstanceData } from "~/routes/_main._card.economic-substance.$id.$pageName";

export type FileFieldName = keyof Pick<SupportingDetailsSchemaType, "files_supportingAttachments">;

export function SupportingDetails(): ReactNode {
  const { id } = useParams()
  const loader = useLoaderData<EconomicSubstanceData>()
  const { submissionData } = useSubmission();
  const navigation = useNavigation()
  const fetcher = useFetcher()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading" || fetcher.state === "submitting"
  const data = useMemo(() => submissionData[Pages.SUPPORTING_DETAILS] as SupportingDetailsSchemaType, [submissionData]);
  const relevantActivitiesDeclarationPage = useMemo(() => submissionData[Pages.RELEVANT_ACTIVITY_DECLARATION] as RelevantActivityDeclarationSchemaType, [submissionData]);
  const { relevantActivities } = relevantActivitiesDeclarationPage
  const isNoneSelected = relevantActivities?.some(activity => activity.id === "none" && activity.selected === "true")
  const form = useForm<SupportingDetailsSchemaType>({
    resolver: zodResolver(supportingDetailsSchema(isNoneSelected ? "true" : "false")),
    shouldFocusError: false,
    defaultValues: {
      additionalComments: data?.additionalComments ?? "",
    },
  });
  const { formState, setValue, watch } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  function onSubmit(data: SupportingDetailsSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  // FILE HANDLING SECTION
  const documentsArray = convertMappedDocumentsToFileObject(loader.mappedDocuments)
  const [fileFieldName, setFileFieldName] = useState<FileFieldName | undefined>()
  const fetcherCreateDocument = useFetcher<string[]>();
  const fetcherUpdateDocument = useFetcher();
  const [openUploadFile, setOpenUploadFile] = useState(false);
  const fileForm = useForm<OptionalFileSchemaType>({
    resolver: zodResolver(createFileSchema({ optional: true })),
    shouldFocusError: false,
  });

  function onSubmitFile(data: OptionalFileSchemaType): void {
    if (fileFieldName) {
      const hasFiles = data.files && data.files.length > 0;

      // Set form value - undefined if no files, otherwise the data
      form.setValue(fileFieldName, hasFiles ? data : undefined);

      if (hasFiles) {
        const formData = new FormData();
        const filesData = data.files
        filesData.forEach((fileData) => {
          formData.append("files", fileData);
        });
        formData.append("location", location.pathname);
        fetcherCreateDocument.submit(formData, {
          action: "/documents/create",
          encType: "multipart/form-data",
          method: "post",
        });
      } else {
        // Clear both the form data and the submission's documentIds
        fetcherUpdateDocument.submit({
          data: JSON.stringify({
            [`${fileFieldName}`]: undefined,
            documentIds: [],
          }),
          location: location.pathname,
        }, {
          action: `/economic-substance/${id}/pages/${Pages.SUPPORTING_DETAILS}/documents/update`,
          method: "post",
        });
      }
    }

    setOpenUploadFile(false);
  }

  const onOpenUploadDialog = (fieldName: FileFieldName): void => {
    setFileFieldName(fieldName)
    const files = form.getValues(fieldName)
    fileForm.reset(files, { keepDefaultValues: true })
    setOpenUploadFile(true)
  }

  useEffect(() => {
    const documentIds = fetcherCreateDocument.data

    if (documentIds && fileFieldName) {
      const data: Record<string, string> = {}
      documentIds.forEach((documentId, index) => {
        /*
         * To ensure proper document handling, all document keys must:
         * - Begin with "documentId"
         * - Be separated by underscores ("_")
         * - End with an index to uniquely identify each document
         */
        const keyName = `documentId_${fileFieldName}_${index}`
        data[keyName] = documentId
      });
      fetcherUpdateDocument.submit({
        data: JSON.stringify(data),
        location: location.pathname,
      }, { action: `/economic-substance/${id}/pages/${Pages.SUPPORTING_DETAILS}/documents/update`, method: "post" })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcherCreateDocument.data])

  useEffect(() => {
    if (documentsArray) {
      // Group documents by type
      const supportingAttachmentsFiles = groupDocuments("files_supportingAttachments", documentsArray);

      // Update form values
      setValue("files_supportingAttachments", supportingAttachmentsFiles.length > 0 ? { files: supportingAttachmentsFiles } : undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setValue]);

  // Watch form values
  const { files_supportingAttachments } = watch()

  return (
    <div className="relative">
      {fileFieldName && (
        <FileUploadDialog
          form={fileForm}
          open={openUploadFile}
          setOpen={setOpenUploadFile}
          onSubmit={onSubmitFile}
        />
      )}

      <Form {...form}>
        <RemixForm
          onSubmit={form.handleSubmit(onSubmit, onError)}
          noValidate
          id={ECONOMIC_SUBSTANCE_FORM_ID}
          className="space-y-5"
        >
          <div className="flex-col space-y-7">
            <FormField
              control={form.control}
              name="additionalComments"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    {
                      isNoneSelected
                        ? (
                            <div className="flex">
                              <p className="flex gap-1">
                                Please provide any comment to support your Economic Substance
                                Declaration
                              </p>
                            </div>
                          )
                        : (
                            <p className="flex gap-1">
                              Optional: Please provide any comment to support your Economic Substance
                              Declaration
                            </p>
                          )
                    }
                  </FormLabel>
                  <FormControl className="md:w-1/2 sm:w-full">
                    <Textarea
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="files_supportingAttachments"
              render={() => (
                <FormItem className="flex flex-col">
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        Supporting Attachments
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      <p>
                        Supporting documentation refers to items which validate the entity’s
                        activity(ies) are reported for the fiscal year.
                        This may include: Bank statements, Portfolio Statement, Recent Property
                        Valuation, Share/Ownership Certificate or another relevant document.
                        The Competent Authority reserves the right to request additional
                        information/documentation to satisfy the filing.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  <FormControl className="md:w-fit sm:w-full">
                    <Button
                      size="sm"
                      onClick={() => onOpenUploadDialog("files_supportingAttachments")}
                      type="button"
                      disabled={isSubmitting}
                    >
                      <Plus className="size-4 mr-2" />
                      {`${files_supportingAttachments ? "File uploaded" : "  Upload file"}`}
                    </Button>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </RemixForm>
      </Form>
      <LoadingState
        isLoading={isSubmitting}
        message="Saving..."
      />
    </div>
  )
}
