import { flatten, unflatten } from "flat";
import type { Submission } from "~/features/submissions/api/get-submission";

export function getDataSet(submission: Submission): Record<string, string> {
  return submission.formDocument.revisions[0].formBuilder.form.dataSet;
}

export function getUnflattenedDataSet(submission: Submission): Record<string, any> {
  return unflatten(getDataSet(submission)) as Record<string, any>;
}

export function getFlattenedSubmission(submission: Record<string, unknown>): Record<string, string> {
  const flattenedData = flatten(submission) as Record<string, string | number | boolean | Date>;

  for (const key in flattenedData) {
    if (flattenedData[key] instanceof Date) {
      flattenedData[key] = flattenedData[key].toISOString();
    } else {
      flattenedData[key] = String(flattenedData[key]);
    }
  }

  return flattenedData as Record<string, string>;
}
