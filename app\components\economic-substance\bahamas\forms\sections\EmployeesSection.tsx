import { Button, FormControl, FormField, FormItem, FormLabel, FormMessage, Input } from "@netpro/design-system";
import { useNavigation } from "@remix-run/react";
import { useFieldArray, useForm, useFormContext } from "react-hook-form";
import { Plus } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { EmployeesTable } from "../../tables/sections/EmployeesTable";
import { EmployeeDialog } from "../../dialogs/sections/EmployeeDialog";
import { DeletePropertyDialog } from "../../dialogs/DeletePropertyDialog";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { type EmployeeSchemaType, type EmployeesSchemaType, employeeSchema } from "~/lib/economic-substance/types/bahamas/employee-schema";

export type ArrayFieldName = keyof Pick<EmployeesSchemaType, "employees">;
export function EmployeesSection() {
  const form = useFormContext<EmployeesSchemaType>()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  // Employees table logic
  const [index, setIndex] = useState<number | undefined>();
  const [arrayFieldName, setArrayFieldName] = useState< ArrayFieldName | undefined>()
  const [openDialog, setOpenDialog] = useState(false)
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const employeeForm = useForm<EmployeeSchemaType>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      fullName: "",
      qualification: "",
      yearsOfExperience: "",
      contractType: "",
    },
  });
  const employeeArray = useFieldArray({
    control: form.control,
    name: "employees",
    keyName: "formArrayId",
  });
  function addEmployee(fieldName: ArrayFieldName): void {
    employeeForm.reset();
    setArrayFieldName(fieldName)
    setIndex(undefined);
    setOpenDialog(true);
  }

  function onSelect(fieldName: ArrayFieldName, employee: EmployeeSchemaType, index: number): void {
    setArrayFieldName(fieldName)
    employeeForm.reset(employee, { keepDefaultValues: true });
    setIndex(index);
    setOpenDialog(true);
  }

  function onDelete(): void {
    if (arrayFieldName === "employees") {
      employeeArray.remove(index);
    }

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(fieldName: ArrayFieldName, index: number): void {
    setArrayFieldName(fieldName)
    setIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  function onSubmitEmployee(data: EmployeeSchemaType): void {
    if (arrayFieldName === "employees") {
      if (index !== undefined) {
        employeeArray.update(index, data);
      } else {
        employeeArray.append(data);
      }
    }

    setOpenDialog(false);
  }

  return (
    <>
      {arrayFieldName && (
        <EmployeeDialog
          open={openDialog}
          setOpen={setOpenDialog}
          form={employeeForm}
          onSubmit={onSubmitEmployee}
        />
      )}
      <DeletePropertyDialog
        open={openDeletedConfirmation}
        onOpenChange={setOpenDeleteConfirmation}
        onCloseDeleteConfirmation={onCloseDeleteConfirmation}
        onDelete={onDelete}
        isSubmitting={isSubmitting}
      />
      <p className="text-md font-bold">Employees</p>
      <FormField
        control={form.control}
        name="totalEmployeesEntity"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Total number of employees of the entity.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <Input
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
                placeholder="0.000"
                step={0.5}
                min={0}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="totalEmployeesRelevantActivity"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Total number of employees engaged in the relevant activity.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <Input
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
                placeholder="0.000"
                step={0.5}
                min={0}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="totalEmployeesBahamas"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Total number of employees engaged in the relevant activity physically present in the Bahamas.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <Input
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
                placeholder="0.000"
                step={0.5}
                min={0}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="employees"
        control={form.control}
        render={({ fieldState }) => (
          <FormItem>
            {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
            <FormLabel>Provide details on qualifications of the employees</FormLabel>
            <FormControl>
              <EmployeesTable
                disabled={isSubmitting}
                employees={employeeArray.fields}
                onSelect={(income, index) => onSelect("employees", income, index)}
                onDelete={index => onOpenDeleteConfirmation("employees", index)}
              />
            </FormControl>
          </FormItem>
        )}
      />
      <div className="flex justify-end">
        <Button size="sm" onClick={() => addEmployee("employees")} type="button" disabled={isSubmitting}>
          <Plus className="mr-2 size-4 text-white" />
          Add Employee
        </Button>
      </div>
    </>
  )
}
