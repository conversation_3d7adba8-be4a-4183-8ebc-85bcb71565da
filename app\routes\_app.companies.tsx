import { Button, Checkbox, Label } from "@netpro/design-system";
import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { <PERSON>, useFetcher, useLoaderData } from "@remix-run/react";
import { Repeat as IconRepeat } from "lucide-react";
import { type JSX, useEffect, useState } from "react";
import type { Company } from "~/features/companies/api/get-companies";
import { getCompanies } from "~/features/companies/api/get-companies";
import CompaniesSearch from "~/features/companies/components/CompaniesSearch";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { middleware } from "~/lib/middlewares.server";
import { getCompanyModuleState } from "~/features/companies/api/get-company-module-state";
import type { BasicMasterClient } from "~/features/master-clients/types/generic";

type CompaniesLoaderResponseProps = {
  companies: Company[]
  masterClient: BasicMasterClient
}

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<CompaniesLoaderResponseProps | never>> {
  const { accessToken, userId, masterClient } = await middleware(["auth", "mfa", "terms", "requireMcc"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { companies } = await getCompanies({
    accessToken,
    userId,
    masterClientId: masterClient.masterClientId as string,
    params: {},
  });
  if (companies?.length === 1) {
    const company = companies[0];
    session.set("currentCompany", company);
    session.set("companyModules", await getCompanyModuleState({ accessToken, userId, companyId: company.companyId }));

    return redirect("/dashboard", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    });
  }

  return json({
    companies,
    masterClient,
  }, {
    status: 200,
    headers: {
      "Set-Cookie": await commitSession(session),
    },
  });
}

export default function Companies(): JSX.Element {
  const { companies: initialCompanies, masterClient } = useLoaderData<typeof loader>();
  const [companies, setCompanies] = useState<Company[]>(initialCompanies ?? []);
  const [includeInactiveCompanies, setIncludeInactiveCompanies] = useState<boolean>(false);
  const fetcher = useFetcher<any>();
  const handleSelectCompany = (selectedCompany: Company): void => {
    fetcher.submit(
      {
        id: selectedCompany.companyId,
        isActive: selectedCompany.isActive,
      },
      { method: "post", action: "/api/companies" },
    );
  }

  useEffect(() => {
    if (fetcher.data) {
      setCompanies(fetcher.data?.companies || []);
    }
  }, [fetcher.data]);

  const handleSearchCompany = (searchTerm: string): void => {
    fetcher.load(`/api/companies?search=${encodeURIComponent(searchTerm)}&inactive=${encodeURIComponent(includeInactiveCompanies)}`);
  }

  return (
    <div className="w-full max-w-3xl">
      <div className="mb-5">
        <h2 className="text-2xl font-semibold font-inter gap-5">
          Account:
          {" "}
          <span className="text-blue-500">
            {masterClient.masterClientCode}
          </span>
        </h2>
      </div>

      <CompaniesSearch
        initialValues={companies}
        onSelect={handleSelectCompany}
        isSearching={fetcher.state === "loading"}
        onSearchCompany={handleSearchCompany}
      />

      <div className="flex justify-between">
        <div className="flex items-center space-x-3">
          <Checkbox
            id="include-inactive-companiex-check"
            onCheckedChange={() => setIncludeInactiveCompanies(!includeInactiveCompanies)}
          />
          <Label
            className="peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            htmlFor="include-inactive-companiex-check"
          >
            Include inactive companies
          </Label>
        </div>

        <Button type="button" variant="outline" size="sm" asChild>
          <Link to="/master-clients" className="gap-1.5">
            <IconRepeat size={16} />
            Change Master Client
          </Link>
        </Button>
      </div>

    </div>
  );
}
