import { But<PERSON> } from "@netpro/design-system";
import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import { Link, useLoaderData, useNavigation } from "@remix-run/react";
import { ChevronRight } from "lucide-react";
import type { JSX } from "react";
import { useState } from "react";
import type { SubmissionModel } from "./_main._card.economic-substance.submissions._index";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { SubmissionRow } from "~/components/economic-substance/SubmissionRow";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import { getESModule } from "~/features/modules/api/get-modules";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { middleware } from "~/lib/middlewares.server";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import type { SubmissionDTO } from "~/services/api-generated";
import { clientGetCompanyModuleSubmissions } from "~/services/api-generated";

const title = "Drafts for" as const;
const breadCrumbList = [
  {
    href: "/economic-substance/drafts",
    name: "Economic Substance",
  },
];

type LoaderResponse = {
  submissions: SubmissionDTO[]
}

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<LoaderResponse | never>> {
  const { company } = await middleware(["auth", "mfa", "terms", "redirectMcc", "requireCompany"], request);
  const esModule = await getESModule(company, request);
  const { data: submissionData, error } = await clientGetCompanyModuleSubmissions({
    headers: await authHeaders(request),
    path: {
      companyId: company.companyId,
      moduleId: esModule.id as string,
    },
  })

  if (error) {
    throw new Response(error.exceptionMessage as string, { status: 500 })
  }

  if (!submissionData?.data) {
    throw new Response("Submissions not found", { status: 404 })
  }

  const submissions = submissionData?.data?.filter(
    submission => submission.status === SubmissionStatusNames.Draft || submission.status === SubmissionStatusNames.Revision,
  );

  return json({ submissions });
}

export default function ESDraftSubmissions(): JSX.Element {
  const { submissions } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const [isCreatingSubmission, setIsCreatingSubmission] = useState(false);
  const isLoading = isCreatingSubmission && navigation.state === "loading";
  const handleNewSubmissionClick = () => {
    setIsCreatingSubmission(true);
  };

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 py-2.5">
        {submissions && submissions.length > 0
          ? (
              submissions.map(submission => (
                <SubmissionRow
                  submission={submission as SubmissionModel}
                  key={submission.id}
                  deleteDraftAction
                  moduleUrl="economic-substance"
                  continuePageName={Pages.FINANCIAL_PERIOD}
                />
              ))
            )
          : (
              <CenteredMessage title="No draft submissions">
                <Button asChild variant="outline">
                  <Link to="/economic-substance/submissions">
                    View submissions
                    <ChevronRight className="w-6" />
                  </Link>
                </Button>
                <span className="font-semibold">or</span>
                <Button asChild variant="default" className="inline-flex gap-1" disabled={isLoading}>
                  <Link to="/economic-substance/new" onClick={handleNewSubmissionClick}>
                    {isLoading ? "Creating submission..." : "File new submission"}
                    <ChevronRight className="w-6" />
                  </Link>
                </Button>
              </CenteredMessage>
            )}
      </div>
    </div>
  );
}
