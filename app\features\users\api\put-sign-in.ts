import { z } from "zod";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export const signInSchema = z.object({
  email: z.string().optional(),
  objectId: z.string().min(1, "Required"),
});

export type User = {
  id: string
  name: string
  surname: string
  username: string
  displayName: string
  email: string
  roleNames: string[]
  roleIds: string[]
  isActive: boolean
  isBlocked: boolean
  applicationUserRoles: {
    userId: string
    roleId: string
  }[]
  objectId: string
};

export type signInInput = z.infer<typeof signInSchema>;

export function signIn({ data, accessToken, userId }: { data: signInInput } & ClientRequestHeaders): Promise<User> {
  return client.put("/security/users/signed-in", accessToken, userId, {}, {
    params: data,
  });
}
