import { <PERSON><PERSON>, <PERSON>rollA<PERSON>, <PERSON><PERSON>B<PERSON>, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Pencil, X } from "lucide-react";
import type { JSX } from "react";
import type { IncomeSchemaType } from "~/lib/basic-financial-report/types/income-schema";

type Props = {
  incomes: (IncomeSchemaType & { formArrayId: string })[]
  onSelect: (income: IncomeSchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function IncomeTable({
  incomes,
  onSelect,
  onDelete,
  disabled,
}: Props): JSX.Element {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Description</TableHead>
              <TableHead>Value</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!incomes.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No incomes available
                </TableCell>
              </TableRow>
            )}
            {incomes.length > 0 && incomes.map((income, index) => (
              <TableRow key={income.formArrayId}>
                <TableCell>{income.description}</TableCell>
                <TableCell>{`$ ${income.amount}`}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(income, index)} disabled={disabled}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell className="font-semibold">TOTAL</TableCell>
              <TableCell className="font-semibold">{`$ ${incomes.reduce((acc, cur) => acc + Number(cur.amount), 0)}`}</TableCell>
              <TableCell />
            </TableRow>
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
