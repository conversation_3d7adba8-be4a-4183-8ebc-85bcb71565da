import { useLoaderData } from "@remix-run/react";
import type { SupportingDetailsSchemaType } from "~/lib/economic-substance/types/bahamas/supporting-details-schema";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.$id.summary";

export function SupportingDetails() {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { additionalComments } = submissionData[Pages.SUPPORTING_DETAILS] as SupportingDetailsSchemaType

  return (
    <div>
      <h2 className="text-blue-700 font-bold mb-4">Supporting Details</h2>
      <div className="border-2 border-blue-200 p-4 whitespace-normal break-words">
        {additionalComments}
      </div>
    </div>

  )
}
