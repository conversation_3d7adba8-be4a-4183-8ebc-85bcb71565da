import { <PERSON><PERSON>, DialogContent, Scroll<PERSON>rea, ScrollBar, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { Outlet, json, useLoaderData, useNavigate } from "@remix-run/react";
import { MailSearch } from "lucide-react";
import { useState } from "react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import { formatDate } from "~/lib/utilities/format";
import InboxMessageDetails from "~/routes/_main._card.inbox.$id";
import type { InboxMessageListItemDTOPaginatedResponse } from "~/services/api-generated";
import { getInboxMessages } from "~/services/api-generated";

const title = "Inbox" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Inbox",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<InboxMessageListItemDTOPaginatedResponse>> {
  await middleware(["auth", "terms"], request);
  const { data: inboxMessages, error } = await getInboxMessages({
    headers: await authHeaders(request),
  });

  if (error) {
    throw new Response("Messages cannot be displayed", { status: 500 });
  }

  return json(inboxMessages);
}

export default function Inbox(): JSX.Element {
  const navigate = useNavigate();
  const { data } = useLoaderData<typeof loader>() || { data: [] };
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null);
  const closeModal = (): void => {
    setSelectedMessageId(null);
  };

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="mt-4">
        {data?.length === 0
          ? (
              <CenteredMessage title="No messages found." IconComponent={MailSearch}></CenteredMessage>
            )
          : (
              <ScrollArea>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>New</TableHead>
                      <TableHead>Received at</TableHead>
                      <TableHead>Subject</TableHead>
                      <TableHead>Master Client</TableHead>
                      <TableHead>Jurisdiction</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data && data.length > 0 && data?.map(message => (
                      <TableRow key={message.id} onClick={() => navigate(`/inbox/${message.id}`)}>
                        <TableCell>{message.fromUserName}</TableCell>
                        <TableCell>{formatDate(message.createdAt ?? "N/A")}</TableCell>
                        <TableCell>{message.subject}</TableCell>
                        <TableCell>{message?.masterClients}</TableCell>
                        <TableCell>{message?.jurisdictions}</TableCell>
                        <TableCell>{message.isRead ? "Read" : "Unread"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <ScrollBar orientation="horizontal" />
              </ScrollArea>
            )}
        <Dialog modal open={!!selectedMessageId} onOpenChange={closeModal}>
          <DialogContent className="flex min-w-[800px]">
            {selectedMessageId && <InboxMessageDetails />}
          </DialogContent>
        </Dialog>
      </div>
      <Outlet />
    </div>
  );
}
