import { redirect } from "@remix-run/react";
import { getSessionData } from "~/lib/auth/utils/session.server";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";
import type { Company } from "~/features/companies/api/get-companies";

type ReturnData = {
  company: Company
};

export default async function requireCompany(
  { request }: MiddlewareProps,
): MiddlewareResponse<never | ReturnData> {
  const { currentCompany } = await getSessionData(request);
  if (!currentCompany) {
    throw redirect("/companies");
  }

  return { company: (currentCompany as Company) };
}
