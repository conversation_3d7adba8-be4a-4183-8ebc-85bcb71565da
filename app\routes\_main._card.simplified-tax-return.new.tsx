import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { redirect, useLoaderData } from "@remix-run/react";
import type { JSX } from "react"
import { createSubmission } from "~/features/companies/api/create-submission";
import { getSubmissionYears, requireYear } from "~/features/companies/api/get-submission-years";
import { requireActiveModule } from "~/features/modules/api/get-modules";
import NewStrSubmission from "~/features/simplified-tax-return/components/NewStrSubmission";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { formSteps } from "~/lib/simplified-tax-return/hooks/use-form-steps";
import { Modules } from "~/lib/utilities/modules";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { middleware } from "~/lib/middlewares.server";

const title = "Create a submission for" as const;
const breadCrumbList = [
  {
    href: "/simplified-tax-return/new",
    name: "Simplified Tax Returns",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse<never> | undefined> {
  const { userId, accessToken, company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const { module } = await requireActiveModule({ request, key: Modules.SIMPLIFIED_TAX_RETURN, companyId: company.companyId });
  const { year } = await requireYear({ request });
  const session = await getSession(request.headers.get("Cookie"));
  if (!Object.keys(formSteps).includes(String(year))) {
    session.flash("notification", {
      variant: "error",
      title: "Error",
      message: "Selected year is not available for submission.",
    });

    return redirect("/simplified-tax-return/new", { headers: { "Set-Cookie": await commitSession(session) } });
  }

  if (!company.isActive) {
    session.flash("notification", {
      variant: "error",
      title: "Company is closed",
      message: "Your company status is closed, please contact your Trident Officer if you believe this is incorrect.",
    });

    return redirect("/simplified-tax-return/new", { headers: { "Set-Cookie": await commitSession(session) } });
  }

  // Create STR submission
  const newSubmission = await createSubmission({
    data: {
      companyId: company.companyId,
      moduleId: module.id as string,
      year,
    },
    accessToken,
    userId,
  });

  return redirect(`/simplified-tax-return/${newSubmission.id}/address-of-head-office`);
}

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  companyName: string
  availableYears: number[]
}> {
  const { userId, company, accessToken } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const { module } = await requireActiveModule({ request, key: Modules.SIMPLIFIED_TAX_RETURN, companyId: company.companyId });
  const availableYears = await getSubmissionYears({ company, module, accessToken, userId });

  return {
    companyName: company.companyName,
    availableYears,
  };
}

export default function NewSubmission(): JSX.Element {
  const { availableYears } = useLoaderData<typeof loader>();

  return (
    <NewStrSubmission availableYears={availableYears} />
  );
}
