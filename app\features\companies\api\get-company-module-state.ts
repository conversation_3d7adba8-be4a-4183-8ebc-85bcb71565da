import { Modules } from "~/lib/utilities/modules";
import { getCompanyModules } from "~/features/companies/api/get-company-modules";

type CompanyModuleEnabled = {
  enabled: boolean
}

export type CompanyModuleState = {
  simplifiedTaxReturn: CompanyModuleEnabled
  boDirectors: CompanyModuleEnabled
  basicFinancialReport: CompanyModuleEnabled
  economicSubstanceBahamas: CompanyModuleEnabled
  economicSubstanceBVI: CompanyModuleEnabled
}

export const DEFAULT_MODULE_STATE: CompanyModuleState = {
  simplifiedTaxReturn: {
    enabled: false,
  },
  boDirectors: {
    enabled: false,
  },
  basicFinancialReport: {
    enabled: false,
  },
  economicSubstanceBahamas: {
    enabled: false,
  },
  economicSubstanceBVI: {
    enabled: false,
  },
} as const;

export async function getCompanyModuleState({
  companyId,
  accessToken,
  userId,
}: {
  companyId: string
  accessToken: string
  userId: string
}): Promise<CompanyModuleState> {
  const modules = await getCompanyModules({ companyId, accessToken, userId });

  // console.log("modules--------------------------------", modules);

  // const companyModuleResponse = await clientGetCompanyModules({ path: { companyId } });

  if ("errors" in modules) {
    return DEFAULT_MODULE_STATE
  }

  const findModuleByKey = (key: string) => modules.modules.find(m => m.key === key);

  return {
    simplifiedTaxReturn: {
      enabled: findModuleByKey(Modules.SIMPLIFIED_TAX_RETURN)?.isEnabled ?? false,
    },
    boDirectors: {
      enabled: findModuleByKey(Modules.BO_DIRECTORS)?.isEnabled ?? false,
    },
    basicFinancialReport: {
      enabled: findModuleByKey(Modules.BASIC_FINANCIAL_REPORT)?.isEnabled ?? false,
    },
    economicSubstanceBahamas: {
      enabled: findModuleByKey(Modules.ECONOMIC_SUBSTANCE_BAHAMAS)?.isEnabled ?? false,
    },
    economicSubstanceBVI: {
      enabled: findModuleByKey(Modules.ECONOMIC_SUBSTANCE_BVI)?.isEnabled ?? false,
    },
  };
}
