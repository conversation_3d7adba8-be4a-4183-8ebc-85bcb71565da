import {
  Button,
  Combobox,
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem,
  ScrollArea,
  ScrollBar,
} from "@netpro/design-system";
import type { ReactNode } from "react";
import { type UseFormReturn, useFormContext } from "react-hook-form";
import { Form as RemixForm } from "@remix-run/react"
import { FORM_ID } from "~/lib/economic-substance/types/bahamas/employee-schema";
import type { DirectionManagementSchemaType, DirectorSchemaType } from "~/lib/economic-substance/types/bahamas/direction-management-schema";

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (data: DirectorSchemaType) => void
  form: UseFormReturn<DirectorSchemaType>
}

function generateArray(n: number) {
  return Array.from({ length: n }, (_, i) => {
    const valor = (i + 1).toString();

    return { label: valor, value: valor };
  });
}

export function DirectorDialog({
  open,
  setOpen,
  onSubmit,
  form,
}: Props): ReactNode {
  // Parent form validations
  const { watch: parentWatch } = useFormContext<DirectionManagementSchemaType>()
  const { numberOfMeetings, numberOfMeetingsInBahamas } = parentWatch()
  const canViewMeetingsAttendedField = Number(numberOfMeetingsInBahamas) > 0
  const isDisableMeetingsAttendedNoRadioButton = (Number(numberOfMeetings) > 0) && (Number(numberOfMeetingsInBahamas) === 0)
  const meetings = generateArray(Number(numberOfMeetingsInBahamas))
  // Form
  const { watch, setValue } = form
  const { meetingsAttended } = watch()
  function handleFormSubmit(e: React.FormEvent) {
    // avoid to trigger parent form
    e.preventDefault();
    e.stopPropagation()
    form.handleSubmit(onSubmit)();
  }

  const handleMeetingsAttendedChange = (value: DirectorSchemaType["meetingsAttended"]) => {
    // clear table fields if not selected
    setValue("meetingsAttended", value)
    if (value === "false") {
      setValue("meetingNumber", undefined)
      setValue("physicallyPresentInBahamas", undefined)
      setValue("qualification", undefined)
      setValue("yearsOfExperience", undefined)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <RemixForm onSubmit={handleFormSubmit} className="p-2" noValidate id={FORM_ID}>
              <DialogHeader>
                <DialogTitle>Director Details</DialogTitle>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Director Full Name*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="isResidentInBahamas"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>
                        <p className="flex gap-1">
                          Resident in Bahamas?*
                        </p>
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          invalid={!!fieldState.error}
                          className="flex flex-row space-x-2"
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Yes
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              No
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="relationToEntity"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Relation to entity*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {canViewMeetingsAttendedField && (
                  <FormField
                    control={form.control}
                    name="meetingsAttended"
                    render={({ field, fieldState }) => (
                      <FormItem>
                        <FormLabel>
                          <p className="flex gap-1">
                            Did the director attend any of the board meetings in bahamas?*
                          </p>
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={handleMeetingsAttendedChange}
                            value={field.value}
                            invalid={!!fieldState.error}
                            className="flex flex-row space-x-2"
                          >
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="true" disabled={isDisableMeetingsAttendedNoRadioButton} />
                              </FormControl>
                              <FormLabel className="font-normal">
                                Yes
                              </FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="false" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                No
                              </FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                {meetingsAttended === "true" && (
                  <>
                    <FormField
                      control={form.control}
                      name="meetingNumber"
                      render={({ field, fieldState }) => (
                        <FormItem className="md:w-1/2 sm:w-full">
                          <FormLabel>Meeting Number*</FormLabel>
                          <FormControl>
                            <Combobox
                              multiple
                              placeholder="Select the attended meetings"
                              searchText="Search..."
                              noResultsText="Meeting not found"
                              items={meetings}
                              onChange={field.onChange}
                              value={field.value}
                              invalid={!!fieldState.error}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="physicallyPresentInBahamas"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>
                            <p className="flex gap-1">
                              Physically present in the Bahamas?*
                            </p>
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              value={field.value}
                              invalid={!!fieldState.error}
                              className="flex flex-row space-x-2"
                            >
                              <FormItem className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="true" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Yes
                                </FormLabel>
                              </FormItem>
                              <FormItem className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value="false" />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  No
                                </FormLabel>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="qualification"
                      render={({ field, fieldState }) => (
                        <FormItem className="md:w-1/2 sm:w-full">
                          <FormLabel>Qualification*</FormLabel>
                          <FormControl>
                            <Input
                              invalid={!!fieldState.error}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="yearsOfExperience"
                      render={({ field, fieldState }) => (
                        <FormItem className="md:w-1/2 sm:w-full">
                          <FormLabel>Years of Experience*</FormLabel>
                          <FormControl>
                            <Input
                              invalid={!!fieldState.error}
                              {...field}
                              type="number"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                  </div>
                </div>
              </DialogFooter>
            </RemixForm>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
