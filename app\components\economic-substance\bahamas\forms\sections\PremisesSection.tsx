import { Button, FormControl, FormField, FormItem, FormLabel, FormMessage, RadioGroup, RadioGroupItem } from "@netpro/design-system";
import { useNavigation } from "@remix-run/react";
import { useFieldArray, useForm, useFormContext } from "react-hook-form";
import { Plus } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { DeletePropertyDialog } from "../../dialogs/DeletePropertyDialog";
import { PremiseDialog } from "../../dialogs/sections/PremiseDialog";
import { PremisesTable } from "../../tables/sections/PremisesTable";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { type PremiseSchemaType, type PremisesSchemaType, premiseSchema } from "~/lib/economic-substance/types/bahamas/premises-schema";

export type ArrayFieldName = keyof Pick<PremisesSchemaType, "premises">;
export function PremisesSection() {
  const form = useFormContext<PremisesSchemaType>()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  // Premises table logic
  const [index, setIndex] = useState<number | undefined>();
  const [arrayFieldName, setArrayFieldName] = useState< ArrayFieldName | undefined>()
  const [openDialog, setOpenDialog] = useState(false)
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const premiseForm = useForm<PremiseSchemaType>({
    resolver: zodResolver(premiseSchema),
    defaultValues: {
      addressLine1: "",
      addressLine2: "",
      country: "BHS",
    },
  });
  const premiseArray = useFieldArray({
    control: form.control,
    name: "premises",
    keyName: "formArrayId",
  });
  function addPremise(fieldName: ArrayFieldName): void {
    premiseForm.reset();
    setArrayFieldName(fieldName)
    setIndex(undefined);
    setOpenDialog(true);
  }

  function onSelect(fieldName: ArrayFieldName, premise: PremiseSchemaType, index: number): void {
    setArrayFieldName(fieldName)
    premiseForm.reset(premise, { keepDefaultValues: true });
    setIndex(index);
    setOpenDialog(true);
  }

  function onDelete(): void {
    if (arrayFieldName === "premises") {
      premiseArray.remove(index);
    }

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(fieldName: ArrayFieldName, index: number): void {
    setArrayFieldName(fieldName)
    setIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  function onSubmitPremise(data: PremiseSchemaType): void {
    if (arrayFieldName === "premises") {
      if (index !== undefined) {
        premiseArray.update(index, data);
      } else {
        premiseArray.append(data);
      }
    }

    setOpenDialog(false);
  }

  const watchBahamasPremisesOwnership = form.watch("bahamasPremisesOwnership")
  const handleBahamasPremisesOwnershipChange = (value: PremisesSchemaType["bahamasPremisesOwnership"]) => {
    // clear table fields if not selected
    form.setValue("bahamasPremisesOwnership", value)
    if (value === "false") {
      premiseArray.remove()
    }
  }

  return (
    <>
      {arrayFieldName && (
        <PremiseDialog
          open={openDialog}
          setOpen={setOpenDialog}
          form={premiseForm}
          onSubmit={onSubmitPremise}
        />
      )}
      <DeletePropertyDialog
        open={openDeletedConfirmation}
        onOpenChange={setOpenDeleteConfirmation}
        onCloseDeleteConfirmation={onCloseDeleteConfirmation}
        onDelete={onDelete}
        isSubmitting={isSubmitting}
      />
      <p className="text-md font-bold">Premises</p>
      <FormField
        control={form.control}
        name="bahamasPremisesOwnership"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Does the entity own any premises in Bahamas?*
              </p>
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={handleBahamasPremisesOwnershipChange}
                value={field.value}
                invalid={!!fieldState.error}
                className="flex flex-row space-x-2"
                disabled={isSubmitting}
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="true" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Yes
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="false" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    No
                  </FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {watchBahamasPremisesOwnership === "true" && (
        <>
          <FormField
            name="premises"
            control={form.control}
            render={({ fieldState }) => (
              <FormItem>
                {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                <FormLabel>Provide addresses of (all) premises within Bahamas used in connection with the relevant activity</FormLabel>
                <FormControl>
                  <PremisesTable
                    disabled={isSubmitting}
                    premises={premiseArray.fields}
                    onSelect={(income, index) => onSelect("premises", income, index)}
                    onDelete={index => onOpenDeleteConfirmation("premises", index)}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <div className="flex justify-end">
            <Button size="sm" onClick={() => addPremise("premises")} type="button" disabled={isSubmitting}>
              <Plus className="mr-2 size-4 text-white" />
              Add Premise
            </Button>
          </div>
        </>
      )}

    </>
  )
}
