import { useLoaderData } from "@remix-run/react";
import { EntityRelation, type FinalizeSchemaType } from "~/lib/economic-substance/types/bahamas/finalize-schema";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.$id.summary";

export function Declaration() {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const {
    declarantName,
    entityRelation,
    otherEntityRelation,
    telephone,
  } = submissionData[Pages.FINALIZE] as FinalizeSchemaType
  const phoneNumber = `${telephone.prefix} ${telephone.number}`

  return (
    <div className="space-y-7">
      <h2 className="text-blue-700 font-bold mb-4">Declaration</h2>
      <div className="border-2 border-blue-200 p-4 whitespace-normal break-words">
        I hereby declare and confirm that:
        <ul className="list-disc pl-6">
          <li>
            the information provided above is true and accurate to the best of my knowledge and belief and
            that by
            submission of this information to the Registered Agent, I have provided all the information
            required to complete
            the economic substance self-assessment
          </li>
          <li>I have the authority to act on behalf of the company.</li>
          <li>
            the Registered Agent has a legitimate interest for processing any personal data provided above;
            specifically, in
            order to ensure the Registered Agent’s and the Entity’s compliance with relevant Bahamas law. I
            further
            acknowledge that the processing of such personal data may include its transfer to Bahamas
            competent
            authorities and that the Registered Agent’s processing of any personal data will be done in
            accordance with the
            Trident Trust Data Privacy Policy, which I have read and understood
          </li>
          <li>
            the US$200 submission fee is included within the annual licence fee invoice and that settlement
            of this invoice
            is due and payable in order to complete the submission process.
          </li>
        </ul>
      </div>

      <div className="border-2 border-blue-200 p-4 whitespace-normal break-words space-y-4">
        <div className="flex gap-2">
          Name of the person stating the declaration:
          <p className="font-semibold">{declarantName}</p>
        </div>
        <div className="flex gap-2">
          Relation to entity:
          <p className="font-semibold">{entityRelation === EntityRelation.OTHER ? otherEntityRelation : entityRelation}</p>
        </div>
        <div className="flex gap-2">
          Phone number:
          <p className="font-semibold">{phoneNumber}</p>
        </div>
      </div>
    </div>

  )
}
