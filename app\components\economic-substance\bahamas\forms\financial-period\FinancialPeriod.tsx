import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import {
  DatePicker,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  RadioGroup,
  RadioGroupItem,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { addDays, addYears, subDays } from "date-fns";
import { Info } from "lucide-react";
import type { ReactNode } from "react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import type { FinancialPeriodSchemaType } from "~/lib/economic-substance/types/bahamas/financial-period-schema";
import { financialPeriodSchema } from "~/lib/economic-substance/types/bahamas/financial-period-schema";
import { ECONOMIC_SUBSTANCE_FORM_ID } from "~/lib/economic-substance/utilities/constants";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { parseDateSafe } from "~/lib/utilities/format";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";

export function FinancialPeriod(): ReactNode {
  const { submissionData } = useSubmission();
  const navigation = useNavigation()
  const fetcher = useFetcher()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading" || fetcher.state === "submitting"
  // Get the submission data and parse dates from API
  const data = useMemo(() => {
    const formData = submissionData[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType;
    if (!formData) {
      return null;
    }

    // If we have API data (startsAt/endsAt), use those dates
    if (submissionData.startsAt && submissionData.endsAt) {
      return {
        ...formData,
        startDate: parseDateSafe(submissionData.startsAt),
        endDate: parseDateSafe(submissionData.endsAt),
      };
    }

    return formData;
  }, [submissionData]);
  const isFirstFiling = !data?.previousSubmissionEndDate;
  const newStartDate = data?.previousSubmissionEndDate ? addDays(parseDateSafe(data.previousSubmissionEndDate)!, 1) : undefined;
  const form = useForm<FinancialPeriodSchemaType>({
    resolver: zodResolver(financialPeriodSchema),
    shouldFocusError: false,
    defaultValues: data || {
      hasFinancialPeriodChanged: "true",
      startDate: undefined,
      endDate: undefined,
      isReclassifiedToPEH: "false",
    },
  });
  const { formState, setValue, watch } = form;
  const watchHasFinancialPeriodChanged = watch("hasFinancialPeriodChanged")
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  const handleStartDateChange = useCallback((value: Date | undefined) => {
    if (value) {
      setValue("startDate", value);
      // Calculate end date (1 year minus 1 day)
      const endDate = subDays(addYears(value, 1), 1);
      setValue("endDate", endDate);
    } else {
      setValue("startDate", value);
    }
  }, [setValue]);

  function onSubmit(data: FinancialPeriodSchemaType): void {
    // Let the server handle UTC/ISO conversion
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  useEffect(() => {
    if (newStartDate) {
      setValue("startDate", newStartDate);
      handleStartDateChange(newStartDate);
    }
  }, [newStartDate, setValue, handleStartDateChange]);

  return (
    <div className="relative">
      <Form {...form}>
        <RemixForm
          onSubmit={form.handleSubmit(onSubmit, onError)}
          noValidate
          id={ECONOMIC_SUBSTANCE_FORM_ID}
          className="space-y-5"
        >
          <div className="flex-col space-y-7">
            <FormField
              control={form.control}
              name="hasFinancialPeriodChanged"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Has an application been made and confirmed with Ministry of Finance to change
                      your financial period?*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                      disabled={isSubmitting}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="space-y-2">
              <FormLabel>
                <p className="flex gap-1">
                  Please state the financial period of your fiscal year*
                </p>
              </FormLabel>
              <div className="flex flex-col gap-5 sm:flex-row">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>Start Date*</FormLabel>
                      <FormControl className="md:w-1/2 sm:w-full">
                        <DatePicker
                          date={field.value ? parseDateSafe(field.value) : undefined}
                          onChange={value => handleStartDateChange(value as Date | undefined)}
                          invalid={!!fieldState.error}
                          disabled={isSubmitting || !isFirstFiling}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>End Date*</FormLabel>
                      <FormControl className="md:w-1/2 sm:w-full">
                        <DatePicker
                          date={field.value ? parseDateSafe(field.value) : undefined}
                          onChange={field.onChange}
                          invalid={!!fieldState.error}
                          disabled={isSubmitting || watchHasFinancialPeriodChanged === "false"}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <FormField
              control={form.control}
              name="isReclassifiedToPEH"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        Is the entity subject to reclassification of a non-included passive holding
                        entity to a pure equity holding (PEH) entity under the Act (CESRA 2023)?
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      <p>
                        A 'pure equity holding company' is defined in CESRA as a company that only
                        holds equity participations and only earns dividends and capital gains or
                        incidental income.
                      </p>
                    </TooltipContent>
                  </Tooltip>

                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                      disabled={isSubmitting}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </RemixForm>
      </Form>
      <LoadingState
        isLoading={fetcher.state === "submitting" || fetcher.state === "loading"}
        message="Saving..."
      />
    </div>
  )
}
