import { TableCell, cn } from "@netpro/design-system";
import type { TdHTMLAttributes } from "react";

type SummaryTableCellProps = {
  label: string
  value?: string
} & TdHTMLAttributes<HTMLTableCellElement>;

export function SummaryTableCell({ label, value, className, ...props }: SummaryTableCellProps): JSX.Element {
  return (
    <TableCell className={cn("last:border-0 py-1", className)} {...props}>
      <p>
        {label}
        {": "}
        <span className="font-semibold">{value ?? ""}</span>
      </p>
    </TableCell>
  );
}
