import type { ActionFunctionArgs, TypedResponse } from "@remix-run/node";

export async function loader(): Promise<TypedResponse> {
  return new Response("Method Not Allowed", { status: 405 });
}

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse> {
  const authorization = request.headers.get("Authorization") as string;
  if (!authorization) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (!request.headers.get("Content-Type")?.includes("application/json")) {
    return new Response("Unsupported Media Type", { status: 415 });
  }

  // TODO: Should validate the body contents before passing to the API.
  const body = await request.json();

  return fetch(`${process.env.API_BASE_URL}/api/v1/externalid/on-attribute-collection-submit`, {
    method: "POST",
    body: JSON.stringify(body),
    headers: {
      "Authorization": `${authorization}`,
      "Content-Type": "application/json",
      "x-userid": "",
    },
  });
}
