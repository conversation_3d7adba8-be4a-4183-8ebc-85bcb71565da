import { z } from "zod";
import { phoneSchema } from "~/lib/types/phone-schema";
import { nonEmptyString } from "~/lib/utilities/zod-validators";

export const contactInformationSchema = z.object({
  name: nonEmptyString("Name"),
  position: nonEmptyString("Position"),
  address1: nonEmptyString("Address #1"),
  address2: z.string().optional(),
  zipCode: nonEmptyString("Zip code"),
  country: nonEmptyString("Country"),
  telephone: phoneSchema({ required: true, inputName: "Telephone" }),
  fax: phoneSchema({ required: false, inputName: "Fax" }),
  email: z.string({ required_error: "Email is required." }).email({ message: "Invalid email address." }),
  companyRepresentativeName: nonEmptyString("Registered agent name"),
  companyRepresentativeTelephone: phoneSchema({ required: true, inputName: "Registered agent telephone" }),
  companyRepresentativeFax: phoneSchema({ required: false, inputName: "Registered agent fax" }),
  companyRepresentativeEmail: z.string({ required_error: "Registered agent email" }).email({ message: "Invalid email address." }),
});

export type ContactInformationType = z.infer<typeof contactInformationSchema>;
