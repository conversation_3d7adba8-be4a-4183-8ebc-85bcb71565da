import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export type Company = {
  companyId: string
  companyName: string
  incorporationNumber: string
  isActive: boolean
  jurisdictionId: string
  jurisdictionName: string
  masterClientId: string
  masterClientCode: string
  vpEntityStatus: string | null
  entityType: string
};

export type Companies = {
  companies: Company[]
}

type GetCompaniesParams = {
  search?: string
  includeInactive?: boolean
};

export function getCompanies(
  { accessToken, userId, masterClientId, params }: { masterClientId: string, params: GetCompaniesParams } & ClientRequestHeaders,
): Promise<Companies> {
  const clientParams = params?.includeInactive
    ? {
        search: params?.search,
      }
    : { search: params?.search, active: true }

  return client.get<Companies>(
    `/client/master-clients/${masterClientId}/companies`,
    accessToken,
    userId,
    { params: clientParams },
  );
}
