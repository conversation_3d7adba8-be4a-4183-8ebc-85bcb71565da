import type { ActionFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import { z } from "zod";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import type { ErrorResponse } from "~/lib/types/error-response";
import { middleware } from "~/lib/middlewares.server";
import { clientRequestBeneficialOwnerUpdate, clientRequestDirectorUpdate } from "~/services/api-generated";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

const updateRequestSchema = z.object({
  uniqueRelationId: z.string({
    required_error: "Unique relation ID is required.",
  }),
  updateRequestType: z.enum([
    "MissingBeneficialOwners",
    "MissingDirectors",
    "MissingShareholders",
    "ChangeOfBeneficialOwners",
    "ChangeOfBeneficialOwnersAddress",
    "ChangeOfBeneficialOwnersParticulars",
    "ChangeOfDirectors",
    "ChangeOfDirectorsAddress",
    "ChangeOfDirectorsParticulars",
    "ChangeOfShareholders",
    "ChangeOfShareholdersAddress",
    "ChangeOfShareholdersParticulars",
    "OtherUpdateOfBeneficialOwners",
    "OtherUpdateOfDirectors",
    "OtherUpdateOfShareholders",
  ]),
  updateRequestComments: z.string({
    required_error: "Update request comments are required.",
  }),
});

export async function action({ request, params }: ActionFunctionArgs): Promise<TypedResponse<{ success: boolean, data?: any, error?: string }>> {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireBoDirModule"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const formData = await request.formData();
  const { id, kind } = params;

  if (!id) {
    throw new Response("Missing id", { status: 400 });
  }

  if (!kind || !["beneficial-owner", "director"].includes(kind)) {
    throw new Response("Invalid officer type", { status: 400 });
  }

  let service;
  if (kind === "beneficial-owner") {
    service = clientRequestBeneficialOwnerUpdate;
  } else {
    service = clientRequestDirectorUpdate;
  }

  const { data: validatedData, error: parseError } = updateRequestSchema.safeParse({
    uniqueRelationId: formData.get("uniqueRelationId"),
    updateRequestType: formData.get("updateRequestType"),
    updateRequestComments: formData.get("updateRequestComments"),
  });

  if (parseError) {
    const errorMessages = parseError.errors.map(err => err.message).join(", ");
    session.flash("notification", { title: "Validation Error", message: errorMessages, variant: "error" });

    return json({
      success: false,
      error: "Validation error",
    }, {
      headers: { "Set-Cookie": await commitSession(session) },
      status: 400,
    });
  }

  const { data, error } = await service({
    headers: await authHeaders(request),
    body: validatedData,
    path: { relationId: validatedData.uniqueRelationId },
  });

  if (error) {
    console.error(`Error updating ${kind === "director" ? "Director" : "Beneficial Owner"}:`, error);
    session.flash("notification", { title: "Error", message: "An error occurred while processing your request", variant: "error" });

    return json<ErrorResponse>({
      success: false,
      error: "An error occurred while processing your request",
    }, {
      headers: { "Set-Cookie": await commitSession(session) },
      status: 500,
    });
  }

  session.flash("notification", {
    title: "Success",
    message: `${kind === "director" ? "Director" : "Beneficial Owner"} update request has been submitted successfully`,
    variant: "success",
  });

  return json({ success: true, data }, {
    headers: { "Set-Cookie": await commitSession(session) },
  });
}
