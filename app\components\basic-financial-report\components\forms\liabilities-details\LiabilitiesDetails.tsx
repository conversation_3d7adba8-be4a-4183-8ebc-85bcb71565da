import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Label, RadioGroup, RadioGroupItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { addYears } from "date-fns";
import { LiabilityDialog } from "../../dialogs/liabilities-details/LiabilityDialog";
import { LiabilityTable } from "../../tables/liabilities-details/LiabilityTable";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { BASIC_FINANCIAL_REPORT_FORM_ID } from "~/lib/basic-financial-report/utilities/constants";
import type { LiabilitiesDetailsSchemaType, LiabilitySchemaType } from "~/lib/basic-financial-report/types/liabilities-details-schema";
import { liabilitiesDetailsSchema, liabilitySchema } from "~/lib/basic-financial-report/types/liabilities-details-schema";
import { formatDate } from "~/lib/utilities/format";

type FieldNameType = "loans" | "accountPayableAccrual" | "otherLiabilities"

export function LiabilitiesDetails(): ReactNode {
  const { submissionData } = useSubmission();
  const endDate = formatDate(submissionData["financial-period"].endFiscalYear);
  const nextYearEndDate = formatDate(addYears(submissionData["financial-period"].endFiscalYear, 1));
  const data = useMemo(() => submissionData[Pages.LIABILITIES_DETAILS] as LiabilitiesDetailsSchemaType, [submissionData]);
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const [open, setOpen] = useState(false);
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [incomeIndex, setIncomeIndex] = useState<number | undefined>();
  const [fieldName, setFieldName] = useState< FieldNameType | undefined>()
  const form = useForm<LiabilitiesDetailsSchemaType>({
    resolver: zodResolver(liabilitiesDetailsSchema),
    shouldFocusError: false,
    defaultValues: { loans: [], accountPayableAccrual: [], otherLiabilities: [] },
  });
  const liabilityForm = useForm<LiabilitySchemaType>({
    resolver: zodResolver(liabilitySchema),
    defaultValues: { description: "", current: "", nonCurrent: "" },
  });
  const loansArray = useFieldArray({
    control: form.control,
    name: "loans",
    keyName: "formArrayId",
  });
  const accountsPayableAccrualArray = useFieldArray({
    control: form.control,
    name: "accountPayableAccrual",
    keyName: "formArrayId",
  });
  const otherLiabilitiesArray = useFieldArray({
    control: form.control,
    name: "otherLiabilities",
    keyName: "formArrayId",
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  function addLiability(fieldName: FieldNameType): void {
    liabilityForm.reset();
    setFieldName(fieldName)
    setIncomeIndex(undefined);
    setOpen(true);
  }

  function onSubmitLiability(data: LiabilitySchemaType): void {
    if (fieldName === "loans") {
      if (incomeIndex !== undefined) {
        loansArray.update(incomeIndex, data);
      } else {
        loansArray.append(data);
      }
    }

    if (fieldName === "accountPayableAccrual") {
      if (incomeIndex !== undefined) {
        accountsPayableAccrualArray.update(incomeIndex, data);
      } else {
        accountsPayableAccrualArray.append(data);
      }
    }

    if (fieldName === "otherLiabilities") {
      if (incomeIndex !== undefined) {
        otherLiabilitiesArray.update(incomeIndex, data);
      } else {
        otherLiabilitiesArray.append(data);
      }
    }

    setOpen(false);
  }

  function onSelect(fieldName: FieldNameType, income: LiabilitySchemaType, index: number): void {
    setFieldName(fieldName)
    liabilityForm.reset(income, { keepDefaultValues: true });
    setIncomeIndex(index);
    setOpen(true);
  }

  function onDelete(): void {
    if (fieldName === "loans") {
      loansArray.remove(incomeIndex);
    }

    if (fieldName === "accountPayableAccrual") {
      accountsPayableAccrualArray.remove(incomeIndex);
    }

    if (fieldName === "otherLiabilities") {
      otherLiabilitiesArray.remove(incomeIndex);
    }

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(fieldName: FieldNameType, index: number): void {
    setFieldName(fieldName)
    setIncomeIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setIncomeIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  const fetcher = useFetcher();

  function onSubmit(data: LiabilitiesDetailsSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const handleCompanyLiabilitiesChange = (value: string) => {
    if (value === "false") {
      form.setValue("loans", undefined)
      form.setValue("accountPayableAccrual", undefined)
      form.setValue("otherLiabilities", undefined)
    }
  }
  const watchCompanyLiabilities = form.watch("companyLiabilities")

  return (
    <>
      <LiabilityDialog
        open={open}
        setOpen={setOpen}
        form={liabilityForm}
        onSubmit={onSubmitLiability}
      />
      <Dialog open={openDeletedConfirmation} onOpenChange={setOpenDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete the property?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation} disabled={isSubmitting}>Cancel</Button>
            <Button type="button" variant="destructive" onClick={onDelete} disabled={isSubmitting}>Yes, delete this property</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={BASIC_FINANCIAL_REPORT_FORM_ID} className="space-y-5">
          <div className="flex-col space-y-7">
            <div>
              <Tooltip delayDuration={0}>
                <Label>
                  <p className="flex gap-1">
                    Liabilities
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                </Label>
                <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                  <p>
                    A liability is something a person or company owes, usually a sum of money. Liabilities are settled over time through the transfer of economic benefits including money, goods, or services
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <FormField
              control={form.control}
              name="companyLiabilities"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Are there any liabilities that the company expects to pay?*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      disabled={isSubmitting}
                      onValueChange={(value) => {
                        field.onChange(value)
                        handleCompanyLiabilitiesChange(value)
                      }}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            { watchCompanyLiabilities === "true" && (
              <>
                <div className="flex flex-col gap-2">
                  <p className="text-xs text-gray-500 my-auto">
                    {`Current Liabilities are expected to be paid within 12 months from the end of ${endDate}`}
                  </p>
                  <p className="text-xs text-gray-500 my-auto">{`Non-Current Liabilities are expected to be paid after ${nextYearEndDate}`}</p>
                </div>
                <div>
                  <Label>Administrative and Operational Expenses</Label>
                  {form.getFieldState("loans").invalid && <ValidationAlert fieldState={form.getFieldState("loans")} />}
                </div>

                <FormField
                  name="loans"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <Tooltip delayDuration={0}>
                        <FormLabel>
                          <p className="flex gap-1">
                            Loans
                            <TooltipTrigger asChild>
                              <Info className="flex shrink-0 size-4" />
                            </TooltipTrigger>
                          </p>
                        </FormLabel>
                        <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                          <p>
                            A loan is a debt incurred from a lender—usually a corporation, financial institution, or government—which advances a sum of money to the borrower. In return, the borrower agrees to a certain set of terms including any finance charges, interest, repayment date, and other conditions.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                      <FormControl>
                        <LiabilityTable
                          disabled={isSubmitting}
                          liabilities={loansArray.fields}
                          onSelect={(liability, index) => onSelect("loans", liability, index)}
                          onDelete={index => onOpenDeleteConfirmation("loans", index)}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Button size="sm" onClick={() => addLiability("loans")} type="button" disabled={isSubmitting}>
                    <Plus className="mr-2 size-4 text-white" />
                    Add New Liability
                  </Button>
                </div>

                <FormField
                  name="accountPayableAccrual"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <Tooltip delayDuration={0}>
                        <FormLabel>
                          <p className="flex gap-1">
                            Accounts Payable and Accrual
                            <TooltipTrigger asChild>
                              <Info className="flex shrink-0 size-4" />
                            </TooltipTrigger>
                          </p>
                        </FormLabel>
                        <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                          <p>
                            Amounts due to vendors or suppliers of goods or services received that have not been paid for.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                      <FormControl>
                        <LiabilityTable
                          disabled={isSubmitting}
                          liabilities={accountsPayableAccrualArray.fields}
                          onSelect={(liability, index) => onSelect("accountPayableAccrual", liability, index)}
                          onDelete={index => onOpenDeleteConfirmation("accountPayableAccrual", index)}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Button size="sm" onClick={() => addLiability("accountPayableAccrual")} type="button" disabled={isSubmitting}>
                    <Plus className="mr-2 size-4 text-white" />
                    Add New Liability
                  </Button>
                </div>

                <FormField
                  name="otherLiabilities"
                  control={form.control}
                  render={() => (
                    <FormItem>
                      <FormLabel>Other Liabilities</FormLabel>
                      <FormControl>
                        <LiabilityTable
                          disabled={isSubmitting}
                          liabilities={otherLiabilitiesArray.fields}
                          onSelect={(liability, index) => onSelect("otherLiabilities", liability, index)}
                          onDelete={index => onOpenDeleteConfirmation("otherLiabilities", index)}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Button size="sm" onClick={() => addLiability("otherLiabilities")} type="button" disabled={isSubmitting}>
                    <Plus className="mr-2 size-4 text-white" />
                    Add New Liability
                  </Button>
                </div>
              </>
            ) }
          </div>
        </RemixForm>
      </Form>
    </>
  )
}
