import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig, loadEnv } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { remixDevTools } from "remix-development-tools";

export default defineConfig({
  server: {
    port: 3000,
  },
  plugins: [
    remixDevTools({
      suppressDeprecationWarning: true,
    }),
    !process.env.VITEST && remix({
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
      },
    }),
    tsconfigPaths(),
  ],
  test: {
    coverage: {
      reporter: process.env.CI ? "json" : "html-spa",
    },
    environment: "happy-dom",
    // Load .env.test while running tests
    env: loadEnv("test", process.cwd(), ""),
    browser: {
      provider: "playwright",
      enabled: true,
      name: "chromium",
      providerOptions: {
        launch: {
          devtools: true,
        },
      },
    },
  },
});
