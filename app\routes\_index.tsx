import type { LinksFunction, MetaArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import type { ReactNode } from "react";
import { defaultLinks, defaultMeta } from "~/lib/config";
import landingStyles from "~/landing.css?url";
import { FileDropdown } from "~/components/landing/FileDropdown";
import { getAppVersion } from "~/lib/utilities/app-version";

export async function loader() {
  // Get the version from environment variable
  const appVersion = getAppVersion();

  return json({
    appVersion,
  });
}

export const links: LinksFunction = () => [
  ...defaultLinks,
  { rel: "stylesheet", href: landingStyles },
];

export const meta: MetaFunction = ({ error }: MetaArgs) => [
  ...defaultMeta(error ? "Error" : undefined),
];

export default function Index(): ReactNode {
  const { appVersion } = useLoaderData<typeof loader>();

  return (
    <div className="min-h-screen flex flex-col">
      <div className="h-full flex flex-col justify-between grow bg-[#010230]">
        {/** Color sent is #00012E but image background is #010230 so using the sent color creates a cut */}
        <main className="relative grid bg-[#010230] grid-cols-3 pb-5">
          <img
            src="/images/landing-header-background.jpeg"
            className="absolute inset-0 max-h-full w-full object-cover opacity-40 pt-28 md:hidden"
          />
          <div className="col-span-3 md:col-span-2 px-12 md:pl-16 z-20">
            <div className="md:py-12 py-10 md:pb-32">
              <img
                src="/images/tt-logo-white.svg"
                alt="TridentTrust"
                className="w-auto md:h-10 h-[25px]"
              />
            </div>
            <div className="max-w-screen md:max-w-3xl lg:max-w-6xl space-y-16 text-white my-16">
              <h1 className="text-xl md:text-3xl lg:text-5xl tracking-[.20em] font-plaak-bold !leading-[1.4] uppercase">
                Welcome to our caribbean private client portal
              </h1>

              <p className="text-lg md:text-xl lg:text-2xl font-akkurat-light md:leading-9 leading-7 text-white/80">
                Please log in to view and update key information for the entities we administer on your behalf. You can also use this platform to prepare and submit statutory filings, and review memos and circulars about recent regulatory developments and industry news. If you experience any difficulties logging in, you can check our FAQs below or contact our support team via the ‘Need Help’ option.
              </p>
              <div>
                <Link
                  to="/login"
                  className="inline-block text-lg font-semibold link-underline-animation pb-2 font-plaak-bold tracking-[.20em] after:bg-[#DBFE87] uppercase"
                >
                  Login
                </Link>
              </div>
            </div>
          </div>
          <div className="md:flex justify-end hidden">
            <img
              src="/images/landing-header-background.jpeg"
              className="object-cover h-[700px] object-right"
            />
          </div>
        </main>
        <div className="grid grid-cols-1 md:grid-cols-2">
          <div className="flex flex-col md:flex-row order-2 md:order-1 gap-8 md:pl-16 uppercase px-12 bg-[#DBFE87] py-4 border-1 border-white/20 border-t">
            <FileDropdown
              title="Download FAQs"
              items={[
                { name: "English", href: "/documents/PCP FAQ December 2024-ENG.pdf" },
                { name: "Spanish", href: "/documents/PCP FAQ December 2024-ESP.pdf" },
                { name: "Portuguese", href: "/documents/PCP FAQ December 2024-POR.pdf" },
                { name: "Chinese", href: "/documents/PCP FAQ December 2024-CHI.pdf" },
              ]}
            />
            <FileDropdown
              title="Quick Registration Guide"
              items={[
                { name: "English", href: "/documents/PCP Quick Start Guide - en.pdf" },
                { name: "Spanish", href: "/documents/PCP Quick Start Guide - es.pdf" },
                { name: "Portuguese", href: "/documents/PCP Quick Start Guide - pt.pdf" },
                { name: "Chinese", href: "/documents/PCP Quick Start Guide - zh.pdf" },
              ]}
            />
          </div>

          <div className="flex order-1 md:order-2 bg-[#010230] w-full justify-end md:border-1 md:border-white/20 md:border-t">
            <div className="grid md:grid-cols-2 w-full md:w-fit">
              <div className="flex md:justify-end pl-12 md:pl-0 md:pr-16 border-1 border-white/20 border-t py-4 md:border-0">
                <a href="mailto:<EMAIL>" className="font-plaak-regular tracking-widest text-sm uppercase text-white hover:underline">
                  Need help?
                </a>
              </div>
              <div className="flex md:justify-end pl-12 md:pl-0 md:pr-16 border-1 border-white/20 border-t py-4 md:border-0">
                <Link to="https://www.tridenttrust.com" className="font-plaak-regular tracking-widest text-sm uppercase text-white hover:underline">
                  Explore our services
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <footer className="md:flex bg-white py-8 justify-center">
        <div className="w-full">
          <div className="md:flex md:justify-between items-center md:px-16 pl-12">
            <a href="https://www.tridenttrust.com">
              <svg
                id="Group_1060"
                data-name="Group 1060"
                xmlns="http://www.w3.org/2000/svg"
                width="90"
                height="45"
                className="mb-6 md:mb-0"
                viewBox="0 0 90 45"
              >
                <path id="Path_295" data-name="Path 295" d="M52.2,44.854,70.652,26.117l-18.453,0Z" transform="translate(-14.751 -7.38)" fill="#000000"></path>
                <path id="Path_296" data-name="Path 296" d="M26.1,44.854,44.552,26.117l-18.453,0Z" transform="translate(-7.375 -7.38)" fill="#000000"></path>
                <path id="Path_297" data-name="Path 297" d="M0,44.854,18.453,26.117,0,26.115Z" transform="translate(0 -7.38)" fill="#000000"></path>
                <path id="Path_298" data-name="Path 298" d="M52.2,0V18.737L70.652,0Z" transform="translate(-14.751)" fill="#000000"></path>
                <path id="Path_299" data-name="Path 299" d="M26.1,0V18.737L44.552,0Z" transform="translate(-7.375)" fill="#000000"></path>
                <path id="Path_300" data-name="Path 300" d="M0,0V18.737L18.453,0Z" fill="#000000"></path>
              </svg>
            </a>
            <div className="flex flex-col md:flex-row gap-3 md:gap-6 text-sm">
              <div>
                <Link to="/terms" target="_blank" className="hover:opacity-75 transition-opacity hover:underline text-[#010230]">
                  Terms & Conditions
                </Link>
              </div>
              <div>
                <Link to="/privacy-policy" target="_blank" className="hover:opacity-75 transition-opacity hover:underline text-[#010230]">
                  Privacy Policy
                </Link>
              </div>
              <div>
                <span className="text-gray-500">
                  Version:
                  {" "}
                  {appVersion}
                </span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
