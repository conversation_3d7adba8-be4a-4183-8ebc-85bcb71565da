import { z } from "zod";
import { fileSchema } from "./file-schema";
import { nonNullDate, stringBoolean } from "~/lib/utilities/zod-validators";

export enum CompanyActivity {
  HOLDING_BUSINESS = "Holding Business - Entities with Securities Investment",
  OTHER = "Other, please specify",
}

export const financialPeriodSchema = z.object({
  firstFinancialReport: stringBoolean(),
  startFiscalYear: nonNullDate("Fiscal year start date"),
  endFiscalYear: nonNullDate("Fiscal year end date"),
  companyActivity: z.enum(Object.values(CompanyActivity) as [string, ...string[]], {
    message: "Please select a company activity",
  }),
  otherCompanyActivity: z.string({
    required_error: "Please specify the other company activity",
  }).optional(),
  tridentAccountingRecordsTool: stringBoolean(),
  summaryReport: fileSchema.optional(),
  accountingRecords: fileSchema.optional(),
// pending to add schema to upload files
}).refine(data => !(data.companyActivity === CompanyActivity.OTHER && !data.otherCompanyActivity), {
  message: "Please specify the other activity",
  path: ["otherCompanyActivity"],
}).refine(data => ((data.startFiscalYear && data.endFiscalYear) && data.startFiscalYear < data.endFiscalYear), {
  message: "The 'To' date must be after the 'From' date",
  path: ["startFiscalYear"],
})
  .refine(data => !((data.tridentAccountingRecordsTool === "false") && !data.summaryReport), {
    message: "Please upload  a file for summary/reports",
    path: ["summaryReport"],
  })
  .refine(data => !((data.tridentAccountingRecordsTool === "false") && !data.accountingRecords), {
    message: "Please upload  a file for accounting records",
    path: ["accountingRecords"],
  });

export type FinancialPeriodSchemaType = z.infer<typeof financialPeriodSchema>;
