import { z } from "zod";
import { nonEmptyString, preprocessArray, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";

export enum AccountType {
  CASH = "Cash",
  CHECKINGS = "Checkings",
  SAVINGS = "Savings",
  TIME_DEPOSITS = "Time deposits",
}

export const cashBankAccountSchema = z.object({
  accountType: z.enum(Object.values(AccountType) as [string, ...string[]], {
    message: "Please select a company activity",
  }),
  bankName: nonEmptyString("Bank Name"),
  amount: stringNumber({ invalidTypeMessage: "Amount is required", greaterThan: 0 }),
})

export const currentAssetsDetailsSchema = z.object({
  companyCash: stringBoolean(),
  cashBankAccounts: preprocessArray(z.array(cashBankAccountSchema)).optional(),
  cashSetupCapital: z.string().optional(),
  cashReceivedIncome: z.string().optional(),
  cashPaidExpenses: z.string().optional(),
  cashReceivedPaidLoans: z.string().optional(),
  cashReceivedInvestmentsSold: z.string().optional(),
  cashPaidInvestmentsPurchased: z.string().optional(),
  cashReceivedSalePaidNonCurrentAssets: z.string().optional(),
  otherCashTransactions: z.string().optional(),
  totalCashBalance: z.string().optional(),
  reviewPreviousInfo: stringBoolean(),
})
  .refine(data =>
    !(data.companyCash === "true" && !data.cashBankAccounts?.length), {
    message: "You must add at least one item.",
    path: ["cashBankAccounts", 0],
  })

export type CurrentAssetsDetailsSchemaType = z.infer<typeof currentAssetsDetailsSchema>;
export type CashBankAccountSchemaType = z.infer<typeof cashBankAccountSchema>;
