import type { JSX } from "react";
import { Badge } from "@netpro/design-system";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import type { SubmissionStatus } from "~/services/api-generated";

export function StatusBadge({ status }: { status: SubmissionStatus }): JSX.Element | null {
  switch (status) {
    case SubmissionStatusNames.Draft:
      return <Badge variant="info">Draft</Badge>;
    case SubmissionStatusNames.Revision:
      return <Badge variant="info">Revision</Badge>;
    case SubmissionStatusNames.Submitted:
      return <Badge variant="success">Submitted</Badge>;
    case SubmissionStatusNames.Paid:
      return <Badge variant="success">Submitted</Badge>;
    default:
      return null;
  }
}
