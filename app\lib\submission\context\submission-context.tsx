import type { ReactNode } from "react";
import { createContext, useMemo, useState } from "react";
import type { FormYear } from "~/lib/simplified-tax-return/hooks/use-form-steps";

export type CreateSubmissionContextType = {
  submissionData: Record<string, any>
  financialYear?: FormYear
};

export type SubmissionContextType = {
  canContinue: boolean
  setCanContinue: (value: boolean) => void
  isSubmitting: boolean
  setIsSubmitting: (value: boolean) => void
} & CreateSubmissionContextType;

export const SubmissionContext = createContext<SubmissionContextType | undefined>(undefined);

export function SubmissionProvider({ submissionData, financialYear, children }: CreateSubmissionContextType & { children: ReactNode }): JSX.Element {
  const [canContinue, setCanContinue] = useState<boolean>(true);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const value = useMemo(() => ({
    submissionData,
    financialYear,
    canContinue,
    setCanContinue,
    isSubmitting,
    setIsSubmitting,
  }), [submissionData, financialYear, canContinue, isSubmitting]);

  return (
    <SubmissionContext.Provider value={value}>
      {children}
    </SubmissionContext.Provider>
  );
}
