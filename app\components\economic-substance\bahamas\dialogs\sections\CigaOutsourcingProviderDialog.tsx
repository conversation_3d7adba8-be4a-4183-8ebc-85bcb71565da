import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem,
  ScrollArea,
  ScrollBar,
  Textarea,
} from "@netpro/design-system";
import type { FormEvent, ReactNode } from "react";
import type { UseFormReturn } from "react-hook-form";
import { Form as RemixForm } from "@remix-run/react"
import { FORM_ID } from "~/lib/economic-substance/types/bahamas/employee-schema";
import type { CigaOutsourcingProvidersSchemaType } from "~/lib/economic-substance/types/bahamas/ciga-schema";

  type Props = {
    open: boolean
    setOpen: (open: boolean) => void
    onSubmit: (data: CigaOutsourcingProvidersSchemaType) => void
    form: UseFormReturn<CigaOutsourcingProvidersSchemaType>
  }

export function CigaOutsourcingProviderDialog({
  open,
  setO<PERSON>,
  onSubmit,
  form,
}: Props): ReactNode {
  function handleFormSubmit(e: FormEvent) {
    // avoid to trigger parent form
    e.preventDefault();
    e.stopPropagation()
    form.handleSubmit(onSubmit)();
  }

  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <RemixForm onSubmit={handleFormSubmit} className="p-2" noValidate id={FORM_ID}>
              <DialogHeader>
                <DialogTitle>Outsourcing Provider Details</DialogTitle>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="entityName"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-2/3 sm:w-full">
                      <FormLabel>Name of entity to whom outsourced.*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="detailsOfResources"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-2/3 sm:w-full">
                      <FormLabel>Details of resources deployed by the entity in carrying out the activity on their behalf.*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="numberOfStaff"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-2/3 sm:w-full">
                      <FormLabel>Number of full-time equivalent staff employed in carrying out CIGA for the entity.*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          type="number"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="monitoringAndControl"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>
                        <p className="flex gap-1">
                          Is the entity able to monitor and control carrying out of the outsourced activity?*
                        </p>
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          invalid={!!fieldState.error}
                          className="flex flex-row space-x-2"
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Yes
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              No
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="physicalAddress"
                  render={({ field, fieldState }) => (
                    <FormItem className="w-full">
                      <FormLabel>Physical Address of Outsourcing Service Provider.*</FormLabel>
                      <FormControl>
                        <Textarea
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="monitoringControlExplanation"
                  render={({ field, fieldState }) => (
                    <FormItem className="w-full">
                      <FormLabel>How is the entity able to monitor and control carrying out of the outsourced activity?*</FormLabel>
                      <FormControl>
                        <Textarea
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                  </div>
                </div>
              </DialogFooter>
            </RemixForm>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
