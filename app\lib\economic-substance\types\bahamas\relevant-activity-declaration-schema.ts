import { z } from "zod";
import type { PageSlug } from "../../utilities/form-pages-bahamas";
import { Pages } from "../../utilities/form-pages-bahamas";
import { nonNullDate, preprocessArray, stringBoolean } from "~/lib/utilities/zod-validators";

function createRelevantActivitySchema(financialPeriodStart?: Date, financialPeriodEnd?: Date) {
  return z.object({
    id: z.string(),
    label: z.string(),
    page: z.string().nullish(),
    selected: stringBoolean(),
    carriedOnForOnlyPartOfFinancialPeriod: z.string().optional().nullable(),
    startDate: nonNullDate("Start date").nullish(),
    endDate: nonNullDate("End date").nullish(),
  }).refine((data) => {
    if (data.selected === "true") {
      return !!data.carriedOnForOnlyPartOfFinancialPeriod
    }

    return true;
  }, {
    message: "Carried on for only part of Financial Period is required",
    path: ["carriedOnForOnlyPartOfFinancialPeriod"],
  })
    .refine((data) => {
      if (data.selected === "true" && data.carriedOnForOnlyPartOfFinancialPeriod === "true") {
        return !!data.startDate
      }

      return true;
    }, {
      message: "Start date is required",
      path: ["startDate"],
    })
    .refine((data) => {
      if (data.selected === "true" && data.carriedOnForOnlyPartOfFinancialPeriod === "true") {
        return !!data.endDate
      }

      return true;
    }, {
      message: "End date is required",
      path: ["endDate"],
    })
    .refine((data) => {
      if (data.selected === "true" && data.carriedOnForOnlyPartOfFinancialPeriod === "true" && data.startDate && financialPeriodStart) {
        return new Date(data.startDate) >= financialPeriodStart;
      }

      return true;
    }, {
      message: "Start date must be within the financial period",
      path: ["startDate"],
    })
    .refine((data) => {
      if (data.selected === "true" && data.carriedOnForOnlyPartOfFinancialPeriod === "true" && data.startDate && financialPeriodEnd) {
        return new Date(data.startDate) <= financialPeriodEnd;
      }

      return true;
    }, {
      message: "Start date must be within the financial period",
      path: ["startDate"],
    })
    .refine((data) => {
      if (data.selected === "true" && data.carriedOnForOnlyPartOfFinancialPeriod === "true" && data.endDate && financialPeriodStart) {
        return new Date(data.endDate) >= financialPeriodStart;
      }

      return true;
    }, {
      message: "End date must be within the financial period",
      path: ["endDate"],
    })
    .refine((data) => {
      if (data.selected === "true" && data.carriedOnForOnlyPartOfFinancialPeriod === "true" && data.endDate && financialPeriodEnd) {
        return new Date(data.endDate) <= financialPeriodEnd;
      }

      return true;
    }, {
      message: "End date must be within the financial period",
      path: ["endDate"],
    })
}

export function createRelevantActivityDeclarationSchema(financialPeriodStart?: Date, financialPeriodEnd?: Date) {
  return z.object({
    relevantActivities: preprocessArray(z.array(createRelevantActivitySchema(financialPeriodStart, financialPeriodEnd))).optional(),
  })
    .refine((data) => {
    // Validate that at least one activity is selected
      return data.relevantActivities?.some(item => item.selected === "true");
    }, {
      path: ["relevantActivities", 0, "selected"],
      message: "At least one option must be selected.",
    })
}

// Keep the original schema for backward compatibility
export const relevantActivityDeclarationSchema = createRelevantActivityDeclarationSchema();

export type RelevantActivityDeclarationSchemaType = z.infer<typeof relevantActivityDeclarationSchema>;

const relevantActivities: { id: string, label: string, page: PageSlug | null }[] = [
  { id: "none", label: "None", page: null },
  { id: "holdingBusiness", label: "Holding Business", page: Pages.HOLDING_BUSINESS },
  { id: "financeAndLeasingBusiness", label: "Finance and Leasing Business", page: Pages.FINANCE_LEASING_BUSINESS },
  { id: "bankingBusiness", label: "Banking Business", page: Pages.BANKING_BUSINESS },
  { id: "insuranceBusiness", label: "Insurance Business", page: Pages.INSURANCE_BUSINESS },
  { id: "fundManagementBusiness", label: "Fund Management Business", page: Pages.FUND_MANAGEMENT_BUSINESS },
  { id: "headquartersBusiness", label: "Headquarters Business", page: Pages.HEADQUARTERS_BUSINESS },
  { id: "shippingBusiness", label: "Shipping Business", page: Pages.SHIPPING_BUSINESS },
  {
    id: "intellectualPropertyBusiness",
    label: "Intellectual Property Business",
    page: Pages.INTELLECTUAL_PROPERTY_BUSINESS,
  },
  {
    id: "distributionAndServiceCentreBusiness",
    label: "Distribution and Service Centre Business",
    page: Pages.DISTRIBUTION_SERVICE_CENTRE_BUSINESS,
  },
]

export const defaultValues: RelevantActivityDeclarationSchemaType = {
  relevantActivities: relevantActivities?.map(activity => ({
    ...activity,
    selected: "false",
    carriedOnForOnlyPartOfFinancialPeriod: null,
    startDate: null,
    endDate: null,
  })),
};
