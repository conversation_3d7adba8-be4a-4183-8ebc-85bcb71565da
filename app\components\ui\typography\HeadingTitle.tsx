import type { JSX, ReactNode } from "react";

type HeadingTitleProps = {
  suffix?: string
  children: ReactNode
};

export function HeadingTitle({
  suffix = "",
  children,
}: HeadingTitleProps): JSX.Element {
  return (
    <div className="w-full gap-3">
      <h2 className="text-4xl font-semibold font-inter">
        {children}
        {" "}
        <span className="text-blue-700">{suffix}</span>
      </h2>
    </div>
  );
}
