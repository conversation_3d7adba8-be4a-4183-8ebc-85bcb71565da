import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Label, RadioGroup, RadioGroupItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { AssetDialog } from "../../dialogs/non-current-assets-details/AssetDialog";
import { AssetTable } from "../../tables/non-current-assets-details/AssetTable";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { BASIC_FINANCIAL_REPORT_FORM_ID } from "~/lib/basic-financial-report/utilities/constants";
import type { AssetSchemaType, NonCurrentAssetsDetailsSchemaType } from "~/lib/basic-financial-report/types/non-current-assets-details-schema";
import { assetSchema, nonCurrentAssetsDetailsSchema } from "~/lib/basic-financial-report/types/non-current-assets-details-schema";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { Currency } from "~/lib/basic-financial-report/utilities/currencies";
import { formatDate } from "~/lib/utilities/format";

export function NonCurrentAssetsDetails(): ReactNode {
  const { submissionData } = useSubmission();
  const startDate = formatDate(submissionData["financial-period"].startFiscalYear)
  const endDate = formatDate(submissionData["financial-period"].endFiscalYear)
  const data = useMemo(() => submissionData[Pages.NON_CURRENT_ASSETS_DETAILS] as NonCurrentAssetsDetailsSchemaType, [submissionData]);
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const [open, setOpen] = useState(false);
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [incomeIndex, setIncomeIndex] = useState<number | undefined>();
  const form = useForm<NonCurrentAssetsDetailsSchemaType>({
    resolver: zodResolver(nonCurrentAssetsDetailsSchema),
    shouldFocusError: false,
    defaultValues: {
      fixedAssets: [],
      amountPaid: "",
    },
  });
  const assetForm = useForm<AssetSchemaType>({
    resolver: zodResolver(assetSchema),
    defaultValues: { description: "", purchaseYear: "", purchaseCost: "", assessedValue: "" },
  });
  const assetsArray = useFieldArray({
    control: form.control,
    name: "fixedAssets",
    keyName: "formArrayId",
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  function addAsset(): void {
    assetForm.reset();

    setIncomeIndex(undefined);
    setOpen(true);
  }

  function onSubmitAsset(data: AssetSchemaType): void {
    if (incomeIndex !== undefined) {
      assetsArray.update(incomeIndex, data);
    } else {
      assetsArray.append(data);
    }

    setOpen(false);
  }

  function onSelect(income: AssetSchemaType, index: number): void {
    assetForm.reset(income, { keepDefaultValues: true });
    setIncomeIndex(index);
    setOpen(true);
  }

  function onDelete(): void {
    assetsArray.remove(incomeIndex);

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(index: number): void {
    setIncomeIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setIncomeIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  const fetcher = useFetcher();

  function onSubmit(data: NonCurrentAssetsDetailsSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const handleCompanyAssetsChange = (value: string) => {
    if (value === "false") {
      form.setValue("fixedAssets", [])
      form.setValue("assetsPaidInCash", undefined)
      form.setValue("amountPaid", undefined)
    }
  }
  const handleAssetsPaidInCashChange = (value: string) => {
    if (value === "false") {
      form.setValue("amountPaid", undefined)
    }
  }
  const watchCompanyAssets = form.watch("companyAssets")
  const watchAssetsPaidInCash = form.watch("assetsPaidInCash")

  return (
    <>
      <AssetDialog
        open={open}
        setOpen={setOpen}
        form={assetForm}
        onSubmit={onSubmitAsset}
      />
      <Dialog open={openDeletedConfirmation} onOpenChange={setOpenDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete the property?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation} disabled={isSubmitting}>Cancel</Button>
            <Button type="button" variant="destructive" onClick={onDelete} disabled={isSubmitting}>Yes, delete this property</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={BASIC_FINANCIAL_REPORT_FORM_ID} className="space-y-5">
          <div className="flex-col space-y-7">
            <div>
              <Tooltip delayDuration={0}>
                <Label>
                  <p className="flex gap-1">
                    Non-Current Assets
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                </Label>
                <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                  <p>
                    A company's long term assets for which the full value will not be realized within the accounting year.
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <FormField
              control={form.control}
              name="companyAssets"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Does the company own assets, which are purchased for long-term use, other than those set-up for capital, or purchased after the incorporation of the company?*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      disabled={isSubmitting}
                      onValueChange={(value) => {
                        field.onChange(value)
                        handleCompanyAssetsChange(value)
                      }}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            { watchCompanyAssets === "true" && (
              <>
                <FormField
                  name="fixedAssets"
                  control={form.control}
                  render={({ fieldState }) => (
                    <FormItem>
                      <Tooltip delayDuration={0}>
                        <FormLabel>
                          <p className="flex gap-1">
                            Fixed Assets
                            <TooltipTrigger asChild>
                              <Info className="flex shrink-0 size-4" />
                            </TooltipTrigger>
                          </p>
                        </FormLabel>
                        <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                          <p>
                            Also known as long-lived assets, tangible asset or property, plant and equipment, is a term used in accounting for assets and property that cannot easily be converted into cash. Examples are building, computer equipment, software, furniture, land, machinery and vehicles.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                      <div className="flex flex-col gap-2">
                        <p className="text-xs text-gray-500 my-auto">
                          These assets are not likely to be converted quickly into cash, such as land, building and equipment.
                        </p>
                        <p className="text-xs text-gray-500 my-auto">{`These assets are purchased after incorporation of the company or during this period ${startDate} to ${endDate}`}</p>
                      </div>
                      {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                      <FormControl>
                        <AssetTable
                          assets={assetsArray.fields}
                          onSelect={onSelect}
                          onDelete={onOpenDeleteConfirmation}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Button size="sm" onClick={addAsset} type="button" disabled={isSubmitting}>
                    <Plus className="mr-2 size-4 text-white" />
                    Add New Asset
                  </Button>
                </div>
                <FormField
                  control={form.control}
                  name="assetsPaidInCash"
                  render={({ field, fieldState }) => (
                    <FormItem className="space-y-3 py-2">
                      <FormLabel>
                        <p className="flex gap-1">
                          Were these assets paid in Cash from the bank?
                        </p>
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={(value) => {
                            field.onChange(value)
                            handleAssetsPaidInCashChange(value)
                          }}
                          value={field.value}
                          invalid={!!fieldState.error}
                          disabled={isSubmitting}
                          className="flex flex-row space-x-2"
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="true" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              Yes
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="false" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              No
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {watchAssetsPaidInCash === "true" && (
                  <>
                    <FormField
                      control={form.control}
                      name="amountPaid"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>
                            <p>Please indicate the cash amount paid.</p>
                          </FormLabel>
                          <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                            <CurrencyInput
                              currencyName={Currency.USD}
                              invalid={!!fieldState.error}
                              {...field}
                              disabled={isSubmitting}
                              type="number"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <p className="text-xs text-gray-500 my-auto">
                      Amount paid should not be more than the Purchase Cost.
                    </p>
                  </>
                )}

              </>
            ) }
          </div>
        </RemixForm>
      </Form>
    </>
  )
}
