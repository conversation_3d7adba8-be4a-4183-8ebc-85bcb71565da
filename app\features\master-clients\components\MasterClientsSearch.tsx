import { useFetcher } from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";
import type { JSX } from "react";
import SearchableList from "~/components/searchable-list/SearchableList";
import type { SearchResultsItemDTO } from "~/lib/types/search-results-type";
import { useDelayedState } from "~/lib/utilities/use-delayed-state";
import type { MasterClientsSearchResultDTO, MasterClientsSearchResultsDTO } from "~/services/api-generated";

function mapMasterClientsToResultItems(clients: MasterClientsSearchResultDTO[]): SearchResultsItemDTO[] {
  return clients.map((mcc: MasterClientsSearchResultDTO) => {
    const juridictions = mcc.companies?.reduce((acc, company) => {
      if (company.jurisdictionName) {
        acc.add(company.jurisdictionName);
      }

      return acc;
    }, new Set<string>()) || new Set<string>();
    const sortedJurisdictions = Array.from(juridictions).sort();
    const jurisdictionText = juridictions.size > 3 ? `${sortedJurisdictions.slice(0, 3).join(", ")}...` : sortedJurisdictions.join(", ");

    return {
      id: mcc.masterClientId as string,
      title: mcc.masterClientCode ?? "Unknown code",
      subtitle: jurisdictionText ? `Jurisdiction${juridictions.size > 1 ? "s" : ""}: ${jurisdictionText}` : "",
    }
  });
}

type MasterClientSearchProps = {
  initialValues: MasterClientsSearchResultDTO[] | null
  onSelect: (masterClient: MasterClientsSearchResultDTO) => void
}

export default function MasterClientsSearch({
  initialValues,
  onSelect,
}: MasterClientSearchProps): JSX.Element {
  const fetcher = useFetcher<MasterClientsSearchResultsDTO>();
  const [masterClients, setMasterClients] = useState<MasterClientsSearchResultDTO[] | null>(initialValues);
  const handleSelect = (id: string): void => {
    const selectedValue = masterClients?.find((mcc: MasterClientsSearchResultDTO) => mcc.masterClientId === id);
    if (selectedValue) {
      onSelect(selectedValue);
    }
  }
  const handleSearch = (searchTerm: string): void => {
    fetcher.load(`/api/master-clients?search=${encodeURIComponent(searchTerm)}`);
  }
  const memoizedItems = useMemo(() => mapMasterClientsToResultItems(masterClients ?? []), [masterClients]);
  const delayedState = useDelayedState(fetcher.state === "loading", 800);

  useEffect(() => {
    if (fetcher.data) {
      setMasterClients(fetcher.data?.masterClients || []);
    }
  }, [fetcher.data]);

  useEffect(() => {
    if (masterClients === null && fetcher.state === "idle") {
      fetcher.load("/api/master-clients");
    }
  }, [fetcher, masterClients]);

  return (
    <div className="w-full max-w-3xl min-h-80 pb-2">
      <SearchableList
        title="Select a Master Client Code"
        placeholder="Search by Master Client Code"
        onSelect={handleSelect}
        onSearch={handleSearch}
        isSearching={delayedState}
        items={memoizedItems}
      />
    </div>
  );
}
