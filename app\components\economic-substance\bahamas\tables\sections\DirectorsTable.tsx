import { <PERSON><PERSON>, <PERSON>rollA<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Pencil, X } from "lucide-react";
import type { JSX } from "react"
import type { DirectorSchemaType } from "~/lib/economic-substance/types/bahamas/direction-management-schema";

type Props = {
  directors: (DirectorSchemaType & { formArrayId: string })[]
  onSelect: (income: DirectorSchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function DirectorsTable({
  directors,
  onSelect,
  onDelete,
  disabled,
}: Props): JSX.Element {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Resident in Bahamas?</TableHead>
              <TableHead>Relation to the Entity</TableHead>
              <TableHead>Meetings Attended</TableHead>
              <TableHead>Physically Present in Bahamas?</TableHead>
              <TableHead>Qualification</TableHead>
              <TableHead>Years of Experience</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!directors.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No directors available
                </TableCell>
              </TableRow>
            )}
            {directors.length > 0 && directors.map((item, index) => (
              <TableRow key={item.formArrayId}>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.isResidentInBahamas === "true" ? "Yes" : "No"}</TableCell>
                <TableCell>{item.relationToEntity}</TableCell>
                <TableCell>{item.meetingsAttended === "true" ? "Yes" : "No"}</TableCell>
                <TableCell>{item.physicallyPresentInBahamas === "true" ? "Yes" : "No"}</TableCell>
                <TableCell>{item.qualification}</TableCell>
                <TableCell>{item.yearsOfExperience}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(item, index)} disabled={disabled}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
