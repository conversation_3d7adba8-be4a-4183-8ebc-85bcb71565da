import { <PERSON><PERSON>, <PERSON><PERSON>, Separator } from "@netpro/design-system";
import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { ChevronRight, Download } from "lucide-react";
import type { ReactNode } from "react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { clientGetSubmission } from "~/services/api-generated";

const title = "Submission for" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Economic Substance",
  },
];

export const handle = {
  breadcrumb: (): ReactNode => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  submissionId: string
  isPaid: boolean | null | undefined
}> {
  await middleware(["auth", "terms", "requireEsModule"], request);
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found");
  }

  // Validate finalize step and status
  if (submission.status !== SubmissionStatusNames.Submitted) {
    throw new Error("Submission is not in submitted status");
  }

  return { submissionId: id, isPaid: submission.isPaid };
}

export default function BasicFinancialReportConfirmation(): ReactNode {
  const { submissionId } = useLoaderData<typeof loader>();

  return (
    <div className="w-full flex flex-col gap-5 px-4 pt-1 pb-5">
      <div className="flex flex-col gap-1">
        <p>
          Thank you for submitting your declaration.
        </p>
        <p>
          Click download for the summary of the declaration.
        </p>
      </div>
      <Alert variant="info" title="Important note">
        The declaration will be submitted to the tax office upon payment of the annual invoice.
        If the invoice has already been paid, the declaration will be processed automatically, and no further action is needed.
      </Alert>
      <div className="flex gap-x-2">
        <Button type="button" size="sm" asChild>
          <Link to={`/economic-substance/${submissionId}/summary`} target="_blank">
            <Download className="size-4 mr-2 text-white" />
            Download
          </Link>
        </Button>
      </div>
      <div>
        <Separator />
        <div className="flex justify-end space-x-2 py-4">
          <Button type="button" asChild>
            <Link to="/economic-substance/submissions">
              Back to submissions
              <ChevronRight className="size-4 ml-2 text-white" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
