import { useLoaderData } from "@remix-run/react";
import { Fragment } from "react/jsx-runtime";
import { SummaryTable } from "../table/SummaryTable";
import { SummaryTableRow } from "../table/SummaryTableRow";
import { SummaryTableData } from "../table/SummaryTableData";
import { SummarySubItem } from "../table/SummarySubItem";
import type { PageSlug } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import type { CigaSchema } from "~/lib/economic-substance/types/bahamas/ciga-schema";
import { ActivityEnum } from "~/lib/economic-substance/types/bahamas/ciga-schema";
import { transformBooleanStringToLabel } from "~/lib/economic-substance/utilities/summary";
import { activityOptions } from "~/lib/economic-substance/utilities/ciga";
import { getCurrencyString } from "~/lib/economic-substance/utilities/currencies";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.$id.summary";

export function Ciga({ page }: { page: PageSlug }) {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const {
    hasCiga,
    activities,
    isCigaOutsourced,
    cigaOutsourcingProportion,
    bahamasOutsourcingExpenditure,
    outsourcingProviders,
  } = submissionData[page] as CigaSchema
  const getActivityDescription = (activity: ActivityEnum) => activityOptions.find(a => a.value === activity)

  function getActivityLabel(activity: ActivityEnum, otherActivity?: string) {
    if (activity === ActivityEnum.OTHER_PLEASE_SPECIFY) {
      return `${getActivityDescription(activity)?.label}:  ${otherActivity}`
    }

    return `${getActivityDescription(activity)?.label}`
  }

  return (
    <div className="space-y-5">
      <h2 className="text-blue-500 font-thin mb-4 text-lg">5. Ciga (Core Income Generating Activities)</h2>
      <SummaryTable>
        <tbody>
          {/* Activities */}
          <SummaryTableRow className="border-b-0">
            <SummaryTableData>
              Do you have any CIGA? (Core Income Generating Activities)
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(hasCiga)}</SummaryTableData>
          </SummaryTableRow>
          {hasCiga && (
            <>
              <SummaryTableRow className="border-b-0">
                <SummaryTableData>
                  <p className="pl-10 pt-2">CIGA DESCRIPTION</p>
                </SummaryTableData>
              </SummaryTableRow>
              {activities.map((activity, index) => (
                <Fragment key={`${activity.description}-${activity.otherActivity}`}>
                  <SummaryTableRow className={index === activities.length - 1 ? "" : "border-b-0"}>
                    <SummaryTableData>
                      <SummarySubItem>
                        {getActivityLabel(activity.description as ActivityEnum, activity.otherActivity)}
                      </SummarySubItem>
                    </SummaryTableData>
                  </SummaryTableRow>
                </Fragment>
              ))}
            </>
          )}
          {/* Providers */}
          <SummaryTableRow className="border-b-0">
            <SummaryTableData>
              Has any core income generating activity (CIGA) been outsourced to
              another entity?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(isCigaOutsourced)}</SummaryTableData>
          </SummaryTableRow>
          {isCigaOutsourced === "true" && (
            <>
              <SummaryTableRow className="border-b-0">
                <SummaryTableData>
                  <SummarySubItem>
                    What proportion of the entity’s total core income-generating activity, is
                    carried out by the outsourcing services provider(%)?
                  </SummarySubItem>
                </SummaryTableData>
                <SummaryTableData>
                  {cigaOutsourcingProportion}
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow>
                <SummaryTableData>
                  <SummarySubItem>
                    Total expenditure incurred on outsourcing in the Bahamas during the
                    financial period.
                  </SummarySubItem>
                </SummaryTableData>
                <SummaryTableData>
                  {getCurrencyString(bahamasOutsourcingExpenditure)}
                </SummaryTableData>
              </SummaryTableRow>
            </>
          )}
        </tbody>
      </SummaryTable>

      {isCigaOutsourced === "true" && (
        <>
          <h2 className="text-blue-700 font-bold mb-4">
            Outsourcing Providers
          </h2>
          <SummaryTable>
            <tbody>
              {outsourcingProviders && outsourcingProviders.map(op => (
                <Fragment key={op.entityName}>
                  <SummaryTableRow className="border-b-0">
                    <SummaryTableData>
                      <div className="flex gap-2">
                        <span>Entity Name:</span>
                        <p className="font-semibold">
                          {op.entityName}
                        </p>
                      </div>
                    </SummaryTableData>
                    <SummaryTableData>
                      <div className="flex gap-2">
                        <span>Number of staff:</span>
                        <p className="font-semibold">
                          {op.numberOfStaff}
                        </p>
                      </div>
                    </SummaryTableData>
                  </SummaryTableRow>
                  <SummaryTableRow className="border-b-0">
                    <SummaryTableData>
                      <div className="flex gap-2">
                        <span>Details of Resources:</span>
                        <p className="font-semibold">
                          {op.detailsOfResources}
                        </p>
                      </div>
                    </SummaryTableData>
                    <SummaryTableData>
                      <div className="flex gap-2">
                        <span>Monitoring and Control:</span>
                        <p className="font-semibold">
                          {transformBooleanStringToLabel(op.monitoringAndControl)}
                        </p>
                      </div>
                    </SummaryTableData>
                  </SummaryTableRow>
                  <SummaryTableRow className="border-b-0">
                    <SummaryTableData>
                      <div className="flex gap-2">
                        <span>Physical Address:</span>
                        <p className="font-semibold">
                          {op.physicalAddress}
                        </p>
                      </div>
                    </SummaryTableData>
                    <SummaryTableData>
                      <div className="flex gap-2">
                        <span>Monitoring Control Explanation:</span>
                        <p className="font-semibold">
                          {op.monitoringControlExplanation}
                        </p>
                      </div>
                    </SummaryTableData>
                  </SummaryTableRow>
                </Fragment>
              ))}
            </tbody>
          </SummaryTable>
        </>
      )}
    </div>
  )
}
