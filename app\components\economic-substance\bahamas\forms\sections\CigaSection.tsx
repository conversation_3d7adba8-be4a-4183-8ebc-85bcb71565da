import { Button, FormControl, FormField, FormItem, FormLabel, FormMessage, RadioGroup, RadioGroupItem } from "@netpro/design-system";
import { useNavigation } from "@remix-run/react";
import { useFormContext } from "react-hook-form";
import { Plus } from "lucide-react";
import { useState } from "react";
import { DeletePropertyDialog } from "../../dialogs/DeletePropertyDialog";
import { CigaActivitiesTable } from "../../tables/sections/CigaActivitiesTable";
import { CigaActivityDialog } from "../../dialogs/sections/CigaActivityDialog";
import { CigaDeleteDialog } from "../../dialogs/sections/CigaDeleteDialog";
import { CigaOutsourcingProvidersTable } from "../../tables/sections/CigaOutsourcingProvidersTable";
import { CigaOutsourcingProviderDialog } from "../../dialogs/sections/CigaOutsourcingProviderDialog";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import type { CigaSchema } from "~/lib/economic-substance/types/bahamas/ciga-schema";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { Currency } from "~/lib/economic-substance/utilities/currencies";
import { useCigaActivity } from "~/lib/economic-substance/hooks/bahamas/use-ciga-activity";
import { useCigaProvider } from "~/lib/economic-substance/hooks/bahamas/use-ciga-provider";

export type ArrayFieldName = keyof Pick<CigaSchema, "activities" | "outsourcingProviders">;
export function CigaSection() {
  const form = useFormContext<CigaSchema>()
  const { setValue, watch } = form
  const navigation = useNavigation()
  const [arrayFieldName, setArrayFieldName] = useState<ArrayFieldName | undefined>()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const { hasCiga, isCigaOutsourced } = watch()
  const {
    activityForm,
    activityArray,
    addActivity,
    onSelect: onSelectActivity,
    onDelete: onDeleteActivity,
    setOpenDeleteAllConfirmation: setOpenDeleteAllConfirmationActivity,
    onDeleteAll: onDeleteAllActivity,
    setOpenDeleteConfirmation: setOpenDeleteConfirmationActivity,
    onOpenDeleteConfirmation: onOpenDeleteConfirmationActivity,
    onCloseDeleteConfirmation: onCloseDeleteConfirmationActivity,
    onOpenDeleteAllConfirmation: onOpenDeleteAllConfirmationActivity,
    onCloseDeleteAllConfirmation: onCloseDeleteAllConfirmationActivity,
    onSubmitActivity,
    openDialog: openDialogActivity,
    setOpenDialog: setOpenDialogActivity,
    openDeleteConfirmation: openDeleteConfirmationActivity,
    openDeleteAllConfirmation: openDeleteAllConfirmationActivity,
  } = useCigaActivity(arrayFieldName, setArrayFieldName);
  const {
    providerForm,
    providerArray,
    addProvider,
    onSelect: onSelectProvider,
    onDelete: onDeleteProvider,
    onOpenDeleteConfirmation: onOpenDeleteConfirmationProvider,
    onCloseDeleteConfirmation: onCloseDeleteConfirmationProvider,
    openDeleteConfirmation: openDeleteConfirmationProvider,
    onSubmit: onSubmitProvider,
    openDialog: openDialogProvider,
    setOpenDeleteConfirmation: setOpenDeleteConfirmationProvider,
    setOpenDialog: setOpenDialogProvider,
  } = useCigaProvider(arrayFieldName, setArrayFieldName)
  const deletePropertyDialogProps = {
    open: arrayFieldName === "activities" ? openDeleteConfirmationActivity : openDeleteConfirmationProvider,
    onOpenChange: arrayFieldName === "activities" ? setOpenDeleteConfirmationActivity : setOpenDeleteConfirmationProvider,
    onCloseDeleteConfirmation: arrayFieldName === "activities" ? onCloseDeleteConfirmationActivity : onCloseDeleteConfirmationProvider,
    onDelete: arrayFieldName === "activities" ? onDeleteActivity : onDeleteProvider,
  }
  const handleChangeHasSiga = (value: CigaSchema["hasCiga"]) => {
    setValue("hasCiga", value)
    if (value === "false") {
      onOpenDeleteAllConfirmationActivity("activities")
    } else {
      activityArray.remove()
    }
  }
  const handleChangeIsCigaOutsourced = (value: CigaSchema["isCigaOutsourced"]) => {
    setValue("isCigaOutsourced", value)
    if (value === "false") {
      setValue("cigaOutsourcingProportion", "")
      setValue("bahamasOutsourcingExpenditure", "")
      providerArray.remove()
    }
  }

  return (
    <>
      {arrayFieldName && (
        <>
          <CigaActivityDialog
            open={openDialogActivity}
            setOpen={setOpenDialogActivity}
            form={activityForm}
            onSubmit={onSubmitActivity}
          />
          <CigaOutsourcingProviderDialog
            open={openDialogProvider}
            setOpen={setOpenDialogProvider}
            form={providerForm}
            onSubmit={onSubmitProvider}
          />
        </>
      )}
      <DeletePropertyDialog
        {...deletePropertyDialogProps}
        isSubmitting={isSubmitting}
      />
      <CigaDeleteDialog
        open={openDeleteAllConfirmationActivity}
        onOpenChange={setOpenDeleteAllConfirmationActivity}
        onCloseDeleteConfirmation={onCloseDeleteAllConfirmationActivity}
        onDelete={onDeleteAllActivity}
        isSubmitting={isSubmitting}
      />
      <p className="text-md font-bold">CIGA</p>
      <FormField
        control={form.control}
        name="hasCiga"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Do you have any CIGA? (Core Income Generating Activities)*
              </p>
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={handleChangeHasSiga}
                value={field.value}
                invalid={!!fieldState.error}
                className="flex flex-row space-x-2"
                disabled={isSubmitting}
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="true" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Yes
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="false" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    No
                  </FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="activities"
        control={form.control}
        render={({ fieldState }) => (
          <FormItem>
            {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
            <FormControl>
              <CigaActivitiesTable
                disabled={isSubmitting || hasCiga === "false"}
                activities={activityArray.fields}
                onSelect={(income, index) => onSelectActivity("activities", income, index)}
                onDelete={index => onOpenDeleteConfirmationActivity("activities", index)}
              />
            </FormControl>
          </FormItem>
        )}
      />
      <div className="flex justify-end">
        <Button
          size="sm"
          onClick={() => addActivity("activities")}
          type="button"
          disabled={isSubmitting || hasCiga === "false"}
        >
          <Plus className="mr-2 size-4 text-white" />
          Add CIGA
        </Button>
      </div>
      <FormField
        control={form.control}
        name="isCigaOutsourced"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Has any core income generating activity (CIGA) been outsourced to another entity?*
              </p>
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={handleChangeIsCigaOutsourced}
                value={field.value}
                invalid={!!fieldState.error}
                className="flex flex-row space-x-2"
                disabled={isSubmitting}
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="true" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Yes
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="false" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    No
                  </FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <>
        {isCigaOutsourced === "true" && (
          <>
            <FormField
              control={form.control}
              name="cigaOutsourcingProportion"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p className="flex gap-1">
                      What propotion of the entities total core income-generating activity, is carried out by the outsourcing services provider?*
                    </p>
                  </FormLabel>
                  <FormControl className="md:w-1/3 sm:w-full">
                    <CurrencyInput
                      currencyName="%"
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                      placeholder="0.0"
                      min={0.1}
                      step={0.1}
                      max={100}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="bahamasOutsourcingExpenditure"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p className="flex gap-1">
                      Total expenditure incurred on outsourcing in the Bahamas during the financia period.*
                    </p>
                  </FormLabel>
                  <FormControl className="md:w-1/3 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                      placeholder="0.0"
                      min={0.1}
                      step={0.1}
                      max={100}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="outsourcingProviders"
              control={form.control}
              render={({ fieldState }) => (
                <FormItem>
                  {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                  <FormLabel>Outsourcing providers</FormLabel>
                  <FormControl>
                    <CigaOutsourcingProvidersTable
                      disabled={isSubmitting}
                      providers={providerArray.fields}
                      onSelect={(income, index) => onSelectProvider("outsourcingProviders", income, index)}
                      onDelete={index => onOpenDeleteConfirmationProvider("outsourcingProviders", index)}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button
                size="sm"
                onClick={() => addProvider("outsourcingProviders")}
                type="button"
                disabled={isSubmitting}
              >
                <Plus className="mr-2 size-4 text-white" />
                Add Outsourcing Providers
              </Button>
            </div>
          </>
        )}
      </>
    </>
  )
}
