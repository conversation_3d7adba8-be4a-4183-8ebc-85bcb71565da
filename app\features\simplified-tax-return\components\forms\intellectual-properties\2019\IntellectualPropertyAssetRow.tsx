import type { JSX } from "react";
import { formatDate } from "~/lib/utilities/format";
import type { IntellectualPropertyAssetType } from "~/lib/simplified-tax-return/types/intellectual-property/2019/intellectual-property-schema";

type IntellectualPropertyAssetRowProps = {
  asset: IntellectualPropertyAssetType & { formArrayId: string }
  onClick: (businessActivity: IntellectualPropertyAssetType, index: number) => void
  index: number
}

export function IntellectualPropertyAssetRow({ asset, onClick, index }: IntellectualPropertyAssetRowProps): JSX.Element {
  return (
    <div
      key={asset.formArrayId}
      className="flex px-3 space-x-8 items-center transition-all duration-150 ring-2 ring-transparent
      hover:ring-primary ring-inset group cursor-pointer
      first-of-type:rounded-t-md last-of-type:rounded-b-md"
      onClick={() => onClick(asset, index)}
    >
      <div className="p-3 flex items-center justify-between">
        <div className="flex flex-col gap-1">
          <div className="inline-flex items-center gap-3">
            <span className="font-semibold text-nowrap group-hover:text-primary transition-colors duration-150">
              {asset.description}
            </span>
          </div>
          <span className="text-sm text-gray-700 text-nowrap">
            {`Acquisition date: ${formatDate(asset.acquisitionDate!)} | Income: $${asset.income}`}
          </span>
        </div>
      </div>
    </div>
  );
}
