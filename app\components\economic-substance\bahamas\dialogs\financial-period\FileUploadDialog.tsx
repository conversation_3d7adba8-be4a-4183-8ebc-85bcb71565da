import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  DialogTitle,
  Dropzone,
  FileList,
  FileUploader,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  notify,
} from "@netpro/design-system";
import type { UseFormReturn } from "react-hook-form";
import type { FileRejection } from "react-dropzone";
import type { FormEvent } from "react";
import type { FileSchemaType } from "~/lib/basic-financial-report/types/file-schema";

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (data: FileSchemaType) => void
  form: UseFormReturn<FileSchemaType>
};

// Rejection messages for upload file component
function getRejectionMessage(rejectionCode: string) {
  switch (rejectionCode) {
    case "file-invalid-type":
      return "Invalid file type. Only images are allowed";
    case "file-too-large":
      return "File is too large. Max size is 5MB";
    case "too-many-files":
      return "Too many files. Max 3 files allowed";
    default:
      return "Unknown error";
  }
}

function handleRejections(rejections: FileRejection[]) {
  rejections.forEach((rejection) => {
    notify({
      title: "File Rejected",
      message: `${
        rejection.file.name
      } was rejected. Reason: ${getRejectionMessage(rejection.errors[0].code)}`,
      variant: "error",
      duration: 5000,
    });
  });
}

const FORM_ID = "upload-files-dialog-form"

export function FileUploadDialog({
  open,
  setOpen,
  onSubmit,
  form,
}: Props) {
  function handleFormSubmit(e: FormEvent) {
    // avoid to trigger parent form
    e.preventDefault();
    e.stopPropagation()
    form.handleSubmit(onSubmit)();
  }

  const handleFileChange = (newFiles: File[], currentFiles: File[] | undefined, setFiles: (files: File[]) => void) => {
    // Ensure currentFiles is defined, otherwise default to an empty array
    const filesList = currentFiles ?? [];
    // Filter out files with duplicate names and ensure no files with the same name are added
    const uniqueFiles = newFiles.filter(
      newFile => !filesList.some(currentFile => currentFile.name === newFile.name),
    );

    // Update the file list with only unique files if there are any
    if (uniqueFiles.length > 0) {
      setFiles([...filesList, ...uniqueFiles]);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <Form {...form}>
          <form onSubmit={handleFormSubmit} className="p-2" noValidate id={FORM_ID}>
            <DialogHeader>
              <DialogTitle>Upload file</DialogTitle>
            </DialogHeader>
            <FormField
              control={form.control}
              name="files"
              render={({ field }) => (
                <FormItem className="pt-5">
                  <FormControl>
                    <>
                      <FileUploader
                        maxFiles={4}
                        files={field.value}
                        setFiles={newFiles => handleFileChange(newFiles, field.value, field.onChange)}
                        onReject={handleRejections}
                        allowedTypes={["application/pdf"]}
                        maxSize={5 * 1024 * 1024}
                        multiple
                      >
                        <Dropzone>
                          <p>
                            Drop a file here or click to select a file.
                          </p>
                          <p className="mt-2 italic text-gray-400">
                            Max. of 4 files. PDF only. File must not be password protected.
                          </p>
                        </Dropzone>
                      </FileUploader>
                      <FileList files={field.value} setFiles={field.onChange} />
                    </>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter className="pt-4">
              <div className="flex w-full justify-end">
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                  <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                </div>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
