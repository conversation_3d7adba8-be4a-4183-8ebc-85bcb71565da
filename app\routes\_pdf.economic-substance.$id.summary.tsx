import type { LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import type { ReactNode } from "react";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import { middleware } from "~/lib/middlewares.server";
import type { SubmissionStatus } from "~/services/api-generated";
import { clientGetSubmission } from "~/services/api-generated";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { SummaryPage } from "~/components/economic-substance/bahamas/summary/components/SummaryPage";
import { EntityDetails } from "~/components/economic-substance/bahamas/summary/components/EntityDetails";
import { FinancialPeriod } from "~/components/economic-substance/bahamas/summary/components/FinancialPeriod";
import {
  TaxIdentificationAddress,
} from "~/components/economic-substance/bahamas/summary/components/TaxIdentificationAddress";
import { RelevantActivities } from "~/components/economic-substance/bahamas/summary/components/RelevantActivities";
import {
  TaxResidencyEntityIncome,
} from "~/components/economic-substance/bahamas/summary/components/TaxResidenctyEntityIncome";
import { ParentEntity } from "~/components/economic-substance/bahamas/summary/components/ParentEntity";
import type {
  RelevantActivityDeclarationSchemaType,
} from "~/lib/economic-substance/types/bahamas/relevant-activity-declaration-schema";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { getActivitySummary } from "~/components/economic-substance/bahamas/summary/components/get-activity-summary";
import { SupportingDetails } from "~/components/economic-substance/bahamas/summary/components/SupportingDetailst";
import { Declaration } from "~/components/economic-substance/bahamas/summary/components/Declaration";
import type { TaxPayerIdentificationSchemaType } from "~/lib/economic-substance/types/bahamas/tax-payer-identification-schema";

export type EconomicSubstanceSummaryLoader = Promise<{
  submissionData: Record<string, any>
  entityDetails: {
    legalEntityName: string
    status: SubmissionStatus | undefined
    companyIdentityCode: string | undefined | null
    masterClientCode: string | undefined | null
    submittedAt: string | undefined | null
  }
}>

export async function loader({ request, params }: LoaderFunctionArgs): EconomicSubstanceSummaryLoader {
  await middleware(["auth", "terms", "requireEsModule"], request);
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found");
  }

  const submissionData = getUnflattenedDataSet(submission);
  const entityDetails = {
    legalEntityName: submission.legalEntityName || "No company name",
    status: submission.status,
    companyIdentityCode: submission.legalEntityCode,
    masterClientCode: submission.masterClientCode,
    submittedAt: submission.submittedAt,
  }

  return {
    submissionData,
    entityDetails,
  };
}

export default function EconomicSubstanceReportSummary(): ReactNode {
  const { submissionData } = useLoaderData<typeof loader>()
  const { relevantActivities } = submissionData[Pages.RELEVANT_ACTIVITY_DECLARATION] as RelevantActivityDeclarationSchemaType
  const selectedRelevantActivities = relevantActivities?.filter(activities => activities.selected === "true")
  const taxPayerIdentification = submissionData[Pages.TAX_PAYER_IDENTIFICATION] as TaxPayerIdentificationSchemaType
  const showRelevantActivities = !(taxPayerIdentification?.isBahamianResident === "true" && !!taxPayerIdentification.files_BusinessLicense)
    && !(taxPayerIdentification?.isBahamianResident === "false" && taxPayerIdentification.isInvestmentFund === "true")

  return (
    <>
      <SummaryPage>
        <div className="flex flex-col">
          <h1 className="font-inter text-xl tracking-widest text-blue-500 uppercase font-semibold">
            Economic substance reporting
          </h1>
        </div>
        <EntityDetails />
        <FinancialPeriod />
      </SummaryPage>
      <SummaryPage>
        <TaxIdentificationAddress />
      </SummaryPage>
      <SummaryPage>
        <RelevantActivities />
        {relevantActivities && relevantActivities[0].selected !== "true" && <TaxResidencyEntityIncome />}
      </SummaryPage>
      {relevantActivities && relevantActivities[0].selected !== "true" && (
        <SummaryPage>
          <ParentEntity />
        </SummaryPage>
      )}
      {showRelevantActivities && selectedRelevantActivities?.map((activity) => {
        if (activity.id !== "none") {
          return getActivitySummary(activity)
        }

        return null;
      })}
      <SummaryPage>
        <SupportingDetails />
        <Declaration />
      </SummaryPage>
    </>
  )
}
