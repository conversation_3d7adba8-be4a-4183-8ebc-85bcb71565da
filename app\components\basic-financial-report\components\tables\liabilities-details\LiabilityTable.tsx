import { <PERSON><PERSON>, <PERSON>rollA<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Pencil, X } from "lucide-react";
import type { JSX } from "react";
import type { LiabilitySchemaType } from "~/lib/basic-financial-report/types/liabilities-details-schema";

type Props = {
  liabilities: (LiabilitySchemaType & { formArrayId: string })[]
  onSelect: (income: LiabilitySchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function LiabilityTable({
  liabilities,
  onSelect,
  onDelete,
  disabled,
}: Props): JSX.Element {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Description</TableHead>
              <TableHead>Current</TableHead>
              <TableHead>Non-Current</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!liabilities.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No liabilities available
                </TableCell>
              </TableRow>
            )}
            {liabilities.length > 0 && liabilities.map((liability, index) => (
              <TableRow key={liability.formArrayId}>
                <TableCell>{liability.description}</TableCell>
                <TableCell>{`$ ${liability.current}`}</TableCell>
                <TableCell>{`$ ${liability.nonCurrent}`}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(liability, index)} disabled={disabled}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell className="font-semibold">TOTAL</TableCell>
              <TableCell className="font-semibold">{`$ ${liabilities.reduce((acc, cur) => acc + Number(cur.current), 0)}`}</TableCell>
              <TableCell className="font-semibold">{`$ ${liabilities.reduce((acc, cur) => acc + Number(cur.nonCurrent), 0)}`}</TableCell>
              <TableCell />
            </TableRow>
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
