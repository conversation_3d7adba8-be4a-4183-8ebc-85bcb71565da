import type { Dispatch, SetStateAction } from "react";
import { useState } from "react";
import { useFieldArray, useForm, useFormContext } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ActivityEnum, type CigaActivitySchemaType, type CigaSchema, cigaActivitySchema } from "~/lib/economic-substance/types/bahamas/ciga-schema";
import type { ArrayFieldName } from "~/components/economic-substance/bahamas/forms/sections/CigaSection";

export function useCigaActivity(arrayFieldName: ArrayFieldName | undefined, setArrayFieldName: Dispatch<SetStateAction<ArrayFieldName | undefined>>) {
  const form = useFormContext<CigaSchema>()
  // Ciga activities table logic
  const [index, setIndex] = useState<number | undefined>();
  const [openDialog, setOpenDialog] = useState(false)
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [openDeleteAllConfirmation, setOpenDeleteAllConfirmation] = useState(false);
  const activityForm = useForm<CigaActivitySchemaType>({
    resolver: zodResolver(cigaActivitySchema),
    defaultValues: {
      description: "",
      otherActivity: "",
    },
  });
  const activityArray = useFieldArray({
    control: form.control,
    name: "activities",
    keyName: "formArrayId",
  });
  function addActivity(fieldName: ArrayFieldName): void {
    activityForm.reset();
    setArrayFieldName(fieldName)
    setIndex(undefined);
    setOpenDialog(true);
  }

  // Handle logic for individual CIGA activity properties
  function onSelect(fieldName: ArrayFieldName, director: CigaActivitySchemaType, index: number): void {
    setArrayFieldName(fieldName)
    activityForm.reset(director, { keepDefaultValues: true });
    setIndex(index);
    setOpenDialog(true);
  }

  function onDelete(): void {
    if (arrayFieldName === "activities") {
      activityArray.remove(index);
    }

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(fieldName: ArrayFieldName, index: number): void {
    setArrayFieldName(fieldName)
    setIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setOpenDeleteConfirmation(false);
  }

  // Handle logic for bulk delete of all CIGA activities
  function onDeleteAll(): void {
    if (arrayFieldName === "activities") {
      activityArray.remove();
      activityArray.append({ description: ActivityEnum.NO_CIGA, otherActivity: "" })
    }

    setOpenDeleteAllConfirmation(false);
  }

  function onOpenDeleteAllConfirmation(fieldName: ArrayFieldName): void {
    setArrayFieldName(fieldName)
    setOpenDeleteAllConfirmation(true);
  }

  function onCloseDeleteAllConfirmation(): void {
    setOpenDeleteAllConfirmation(false);
  }

  function onSubmitActivity(data: CigaActivitySchemaType): void {
    if (arrayFieldName === "activities") {
      if (index !== undefined) {
        activityArray.update(index, data);
      } else {
        activityArray.append(data);
      }
    }

    setOpenDialog(false);
  }

  return {
    activityArray,
    arrayFieldName,
    activityForm,
    addActivity,
    onSelect,
    onDelete,
    onOpenDeleteConfirmation,
    setOpenDeleteConfirmation,
    onCloseDeleteConfirmation,
    onDeleteAll,
    setOpenDeleteAllConfirmation,
    onOpenDeleteAllConfirmation,
    onCloseDeleteAllConfirmation,
    onSubmitActivity,
    openDialog,
    setOpenDialog,
    openDeleteConfirmation,
    openDeleteAllConfirmation,
  };
}
