import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { getPreviousStep } from "../hooks/use-form-steps";
import type { RelevantActivityDeclarationSchemaType } from "../types/bahamas/relevant-activity-declaration-schema";
import { type PageSlug, Pages } from "./form-pages-bahamas";
import { mapDocumentIdsToKeys, mapDocumentsToFormKeys } from "./documents";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { middleware } from "~/lib/middlewares.server";
import type { DocumentDTO } from "~/services/api-generated";
import { clientGetSubmission, getApiV1CommonDocuments } from "~/services/api-generated";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import type { EconomicSubstanceContainerLoader } from "~/routes/_main._card.economic-substance.$id.$pageName";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function getFormLoader({
  request,
  params,
}: LoaderFunctionArgs, page: PageSlug): Promise<EconomicSubstanceContainerLoader> {
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireEsModule"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found");
  }

  if (submission.status !== SubmissionStatusNames.Draft && submission.status !== SubmissionStatusNames.Revision && submission.status !== SubmissionStatusNames.Temporal) {
    session.flash("notification", { title: "Error!", message: "Submission was already submitted", variant: "error" });

    return redirect("/economic-substance/new", { headers: { "Set-Cookie": await commitSession(session) } });
  }

  const submissionData = getUnflattenedDataSet(submission);
  const relevantActivityDeclaration = submissionData[Pages.RELEVANT_ACTIVITY_DECLARATION] as RelevantActivityDeclarationSchemaType | undefined;

  if (relevantActivityDeclaration?.relevantActivities) {
    const { relevantActivities } = relevantActivityDeclaration
    const pagesOrder = [
      { page: Pages.RELEVANT_ACTIVITY_DECLARATION, selected: true },
      { page: Pages.TAX_PAYER_IDENTIFICATION, selected: relevantActivities?.[0].selected === "false" },
      { page: Pages.HOLDING_BUSINESS, selected: relevantActivities?.[1]?.selected === "true" },
      { page: Pages.FINANCE_LEASING_BUSINESS, selected: relevantActivities?.[2]?.selected === "true" },
      { page: Pages.BANKING_BUSINESS, selected: relevantActivities?.[3]?.selected === "true" },
      { page: Pages.INSURANCE_BUSINESS, selected: relevantActivities?.[4]?.selected === "true" },
      { page: Pages.FUND_MANAGEMENT_BUSINESS, selected: relevantActivities?.[5]?.selected === "true" },
      { page: Pages.HEADQUARTERS_BUSINESS, selected: relevantActivities?.[6]?.selected === "true" },
      { page: Pages.SHIPPING_BUSINESS, selected: relevantActivities?.[7]?.selected === "true" },
      { page: Pages.INTELLECTUAL_PROPERTY_BUSINESS, selected: relevantActivities?.[8]?.selected === "true" },
      { page: Pages.DISTRIBUTION_SERVICE_CENTRE_BUSINESS, selected: relevantActivities?.[9]?.selected === "true" },
      { page: Pages.SUPPORTING_DETAILS, selected: true },
    ];
    const isPageRestricted = pagesOrder.find(i => ((i.page === page) && !i.selected))

    if (isPageRestricted) {
      session.flash("notification", {
        title: "Access restricted",
        message: "Access is restricted. To gain access to this section, please activate it from the Relevant Activity Declaration page.",
        variant: "info",
      });

      return redirect(`/economic-substance/${id}/${Pages.RELEVANT_ACTIVITY_DECLARATION}`, { headers: { "Set-Cookie": await commitSession(session) } });
    }
  }

  // Validate previous step was filled in
  const previousPage = getPreviousStep(submissionData, page, company.jurisdictionName);

  if (previousPage && !submissionData[previousPage]) {
    // Redirect to the previous page
    return redirect(`/economic-substance/${id}/${previousPage}`);
  }

  let mappedDocuments = {}

  if (submission.documentIds?.length && submissionData[page]) {
    const match = mapDocumentIdsToKeys(submissionData[page], submission.documentIds)

    if (Object.values(match).length > 0) {
      const { data: document } = await getApiV1CommonDocuments({
        headers: await authHeaders(request),
        query: {
          documentIds: Object.keys(match),
          includeData: true,
        },
      });

      mappedDocuments = mapDocumentsToFormKeys(document as DocumentDTO[], match)
    }
  }

  return { submission, page, mappedDocuments, jurisdictionName: company.jurisdictionName };
}
