import type { Submission } from "./get-submission";
import { client } from "~/lib/api-client";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export function finalizeSubmission({ id, accessToken, userId }: { id: string } & ClientRequestHeaders): Promise<Submission> {
  return client.post<Submission>(
    `/client/submissions/${id}/finalize-request`,
    accessToken,
    userId,
    {
      submissionId: id,
    },
  );
}
