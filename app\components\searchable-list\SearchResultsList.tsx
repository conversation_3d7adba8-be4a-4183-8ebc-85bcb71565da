import type { JS<PERSON> } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from "@netpro/design-system";
import { SearchResultsItem } from "./SearchResultsItem"
import type { SearchResultsItemDTO } from "~/lib/types/search-results-type"

type SearchableResultsProps = {
  items: SearchResultsItemDTO[]
  onSelect: (itemId: string) => void
}

export function SearchResultsList({ items, onSelect }: SearchableResultsProps): JSX.Element {
  return (
    <ScrollArea className="[&>*]:max-h-96" type="scroll">
      <ul className="flex flex-col w-full p-1 gap-1">
        {items.map((item: SearchResultsItemDTO) => (
          <SearchResultsItem key={item.id} item={item} onSelect={onSelect} />
        ))}
        <ScrollBar />
      </ul>
    </ScrollArea>
  )
}
