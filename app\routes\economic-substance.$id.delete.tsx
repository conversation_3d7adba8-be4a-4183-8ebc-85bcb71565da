import { type ActionFunctionArgs, type TypedResponse, json } from "@remix-run/node";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import { clientDeleteSubmission } from "~/services/api-generated";

export async function action({ request, params }: ActionFunctionArgs): Promise<TypedResponse<{ success: boolean, data?: any, error?: string }>> {
  await middleware(["auth", "terms", "requireEsModule"], request);
  const { id } = params

  if (!id) {
    throw new Response("The Id is required to delete the submission", { status: 400 })
  }

  const result = await clientDeleteSubmission({
    headers: await authHeaders(request),
    path: {
      submissionId: id,
    },
  })

  if ("error" in result && result.error) {
    return json({
      success: false,
      errors: {
        SubmissionDelete: result.error.exceptionMessage as string,
      },
    })
  }

  return json({
    success: true,
  })
}
