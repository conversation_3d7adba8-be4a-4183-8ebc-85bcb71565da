import { z } from "zod";
import { fileSchema } from "./file-schema";
import { nonEmptyString, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";

export const intellectualPropertySchema = z.object({
  isHighRiskIpEntity: stringBoolean(),
  relevantIpAsset: nonEmptyString("This field"),
  incomeGenerationExplanation: nonEmptyString("This field"),
  employeeResponsibility: nonEmptyString("This field"),
  strategicDecisionsBahamas: nonEmptyString("This field"),
  tradingActivitiesBahamas: nonEmptyString("This field"),
  // conditional questions if isHighRiskIpEntity === "true"
  grossIncomeRoyalties: stringNumber({ allowDecimal: true, optional: true }),
  grossIncomeSaleIpAsset: stringNumber({ allowDecimal: true, optional: true }),
  grossIncomeOtherSources: stringNumber({ allowDecimal: true, optional: true }),
  businessPlanExplanation: nonEmptyString("This field", true),
  files_BusinessPlanDocument: fileSchema.optional(),
  decisionMakingEvidenceExplanation: nonEmptyString("This field", true),
  files_EvidenceDocument: fileSchema.optional(),
  additionalComplianceExplanation: nonEmptyString("This field", true),
  files_AdditionalEvidence: fileSchema.optional(),
}).superRefine((data, ctx) => {
  if (data.isHighRiskIpEntity === "true") {
    if (!data.businessPlanExplanation) {
      ctx.addIssue({
        path: ["businessPlanExplanation"],
        message: "Required",
        code: "custom",
      });
    }

    if (!data.files_BusinessPlanDocument) {
      ctx.addIssue({
        path: ["files_BusinessPlanDocument"],
        message: "Required",
        code: "custom",
      });
    }

    if (!data.decisionMakingEvidenceExplanation) {
      ctx.addIssue({
        path: ["decisionMakingEvidenceExplanation"],
        message: "Required",
        code: "custom",
      });
    }

    if (!data.files_EvidenceDocument) {
      ctx.addIssue({
        path: ["files_EvidenceDocument"],
        message: "Required",
        code: "custom",
      });
    }

    if (!data.additionalComplianceExplanation) {
      ctx.addIssue({
        path: ["additionalComplianceExplanation"],
        message: "Required",
        code: "custom",
      });
    }
  }
});

export type IntellectualPropertySchemaType = z.infer<typeof intellectualPropertySchema>

export function getIntellectualPropertyDefaultValues(data: IntellectualPropertySchemaType | undefined): IntellectualPropertySchemaType {
  return {
    isHighRiskIpEntity: (data?.isHighRiskIpEntity ?? undefined) as "true" | "false", // bypass to set as undefined the first status
    relevantIpAsset: data?.relevantIpAsset ?? "",
    incomeGenerationExplanation: data?.incomeGenerationExplanation ?? "",
    employeeResponsibility: data?.employeeResponsibility ?? "",
    strategicDecisionsBahamas: data?.strategicDecisionsBahamas ?? "",
    tradingActivitiesBahamas: data?.tradingActivitiesBahamas ?? "",
    grossIncomeRoyalties: data?.grossIncomeRoyalties ?? "",
    grossIncomeSaleIpAsset: data?.grossIncomeSaleIpAsset ?? "",
    grossIncomeOtherSources: data?.grossIncomeOtherSources ?? "",
    businessPlanExplanation: data?.businessPlanExplanation ?? "",
    decisionMakingEvidenceExplanation: data?.decisionMakingEvidenceExplanation ?? "",
    additionalComplianceExplanation: data?.additionalComplianceExplanation ?? "",
  };
}
