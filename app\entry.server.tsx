/**
 * By default, <PERSON> will handle generating the HTTP Response for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/file-conventions/entry.server
 */

import { PassThrough } from "node:stream";

import type { ActionFunctionArgs, AppLoadContext, EntryContext, LoaderFunctionArgs } from "@remix-run/node";
import { createReadableStreamFromReadable } from "@remix-run/node";
import { RemixServer } from "@remix-run/react";
import { isbot } from "isbot";
import { renderToPipeableStream } from "react-dom/server";
import { NonceProvider } from "./lib/utilities/nonce-context";
import { cspHeaders, generateNonce } from "./lib/utilities/csp-headers.server";

const ABORT_DELAY = 5_000;

export default function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
  /*
   * This is ignored so we can keep it in the template for visibility.  Feel
   * free to delete this parameter in your app if you're not using it!
   */
  // eslint-disable-next-line unused-imports/no-unused-vars
  loadContext: AppLoadContext,
): Promise<Response> {
  return isbot(request.headers.get("user-agent") || "")
    ? handleBotRequest(
      request,
      responseStatusCode,
      responseHeaders,
      remixContext,
    )
    : handleBrowserRequest(
      request,
      responseStatusCode,
      responseHeaders,
      remixContext,
    );
}

function handleBotRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
): Promise<Response> {
  return new Promise((resolve, reject) => {
    let shellRendered = false;
    const nonce = generateNonce();
    const { pipe, abort } = renderToPipeableStream(
      <NonceProvider nonce={nonce}>
        <RemixServer
          context={remixContext}
          url={request.url}
          abortDelay={ABORT_DELAY}
        />
      </NonceProvider>,
      {
        onAllReady() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          const csp = cspHeaders(nonce);
          responseHeaders.set("Content-Type", "text/html");
          responseHeaders.set("Content-Security-Policy", csp);

          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode,
            }),
          );

          pipe(body);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          responseStatusCode = 500;
          /*
           * Log streaming rendering errors from inside the shell.  Don't log
           * errors encountered during initial shell rendering since they'll
           * reject and get logged in handleDocumentRequest.
           */
          if (shellRendered) {
            console.error(error);
          }
        },
        nonce,
      },
    );

    setTimeout(abort, ABORT_DELAY);
  });
}

function handleBrowserRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
): Promise<Response> {
  return new Promise((resolve, reject) => {
    let shellRendered = false;
    const nonce = generateNonce();
    const { pipe, abort } = renderToPipeableStream(
      <NonceProvider nonce={nonce}>
        <RemixServer
          context={remixContext}
          url={request.url}
          abortDelay={ABORT_DELAY}
        />
      </NonceProvider>,
      {
        onShellReady() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          const csp = cspHeaders(nonce);

          responseHeaders.set("Content-Type", "text/html");
          responseHeaders.set("Content-Security-Policy", csp);

          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode,
            }),
          );

          pipe(body);
        },
        onShellError(error: unknown) {
          reject(error);
        },
        onError(error: unknown) {
          responseStatusCode = 500;
          /*
           * Log streaming rendering errors from inside the shell.  Don't log
           * errors encountered during initial shell rendering since they'll
           * reject and get logged in handleDocumentRequest.
           */
          if (shellRendered) {
            console.error(error);
          }
        },
        nonce,
      },
    );

    setTimeout(abort, ABORT_DELAY);
  });
}

export function handleError(error: unknown, { request }: LoaderFunctionArgs | ActionFunctionArgs): void {
  if (error) {
    console.error("NetPro Error Log:", error);
    if (!request.signal.aborted) {
      /*
       * TODO: Implement Azure Application Insights error logging
       * See: https://remix.run/docs/en/main/file-conventions/entry.server#handleerror
       */
    }
  }
}
