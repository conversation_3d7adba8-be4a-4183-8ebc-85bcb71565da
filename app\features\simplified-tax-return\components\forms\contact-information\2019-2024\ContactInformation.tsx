import { zodResolver } from "@hookform/resolvers/zod";
import { Combobox, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Input, PhonePrefix } from "@netpro/design-system";
import { Form as RemixForm, useFetcher } from "@remix-run/react";
import { type JSX, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { type ContactInformationType, contactInformationSchema } from "~/lib/simplified-tax-return/types/contact-information/2019-2024/contact-information-schema";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { getCountryOptions } from "~/lib/utilities/countries";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { decodeFields, encodeFields } from "~/lib/utilities/hashPrefix";

export function ContactInformation(): JSX.Element {
  const { submissionData } = useSubmission();
  const data = useMemo(() => submissionData[Pages.CONTACT_INFORMATION] as ContactInformationType, [submissionData]);
  const form = useForm<ContactInformationType>({
    resolver: zodResolver(contactInformationSchema),
    shouldFocusError: false,
    defaultValues: {
      name: "",
      position: "",
      address1: "",
      address2: "",
      zipCode: "",
      country: "",
      telephone: {
        countryCode: "",
        prefix: "",
        number: "",
      },
      fax: {
        countryCode: "",
        prefix: "",
        number: "",
      },
      email: "",
      companyRepresentativeTelephone: {
        countryCode: "KN",
        prefix: "******",
        number: "4691817",
      },
      companyRepresentativeFax: {
        countryCode: "KN",
        prefix: "******",
        number: "4691794",
      },
      companyRepresentativeName: "Trident Trust Company (Nevis) Limited",
      companyRepresentativeEmail: "<EMAIL>",
    },
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    if (!data) {
      return;
    }

    const decoded = decodeFields(
      {
        ...data,
        address1: data?.address1 ?? "",
        address2: data?.address2 ?? "",
      },
      ["address1", "address2"],
    );

    reset(decoded, { keepDefaultValues: true });
  }, [data, reset]);

  const fetcher = useFetcher();

  function onSubmit(data: ContactInformationType): void {
    const encoded = encodeFields(data, ["address1", "address2"]);
    fetcher.submit({ data: JSON.stringify(encoded) }, {
      method: "post",
    });
  }

  const countryOptions = useMemo(() => getCountryOptions(), []);

  return (
    <Form {...form}>
      <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id="str-form">
        <div className="pb-4">
          <p>Contact information for above mentioned entity:</p>
        </div>
        <div className="flex-col space-y-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Name *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="position"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Position *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="address1"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Address #1 *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="address2"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Address #2</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="zipCode"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Zip code *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="country"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Country*</FormLabel>
                <FormControl>
                  <Combobox
                    placeholder="Select a country"
                    searchText="Search..."
                    noResultsText="No countries found."
                    items={countryOptions}
                    onChange={field.onChange}
                    value={field.value}
                    invalid={!!fieldState.error}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="telephone"
            render={({ fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Telephone *</FormLabel>
                <div className="flex">
                  <FormField
                    control={form.control}
                    name="telephone.countryCode"
                    render={({ field }) => (
                      <FormControl>
                        <PhonePrefix
                          invalid={!!fieldState.error}
                          onChange={field.onChange}
                          onPrefixChange={value => form.setValue("telephone.prefix", value)}
                          className="min-w-20 max-w-fit rounded-r-none"
                          value={field.value}
                        />
                      </FormControl>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="telephone.number"
                    render={({ field }) => (
                      <FormControl>
                        <Input
                          className="rounded-l-none"
                          type="number"
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                    )}
                  />
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="fax"
            render={({ fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Fax</FormLabel>
                <div className="flex">
                  <FormField
                    control={form.control}
                    name="fax.countryCode"
                    render={({ field }) => (
                      <FormControl>
                        <PhonePrefix
                          invalid={!!fieldState.error}
                          onChange={field.onChange}
                          onPrefixChange={value => form.setValue("fax.prefix", value)}
                          className="min-w-20 max-w-fit rounded-r-none"
                          value={field.value}
                        />
                      </FormControl>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="fax.number"
                    render={({ field }) => (
                      <FormControl>
                        <Input
                          className="rounded-l-none"
                          type="number"
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                    )}
                  />
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Email address *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="pt-6 pb-4">
          <p>Registered Agent Information:</p>
        </div>
        <div className="flex-col space-y-2">
          <FormField
            control={form.control}
            name="companyRepresentativeName"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Name *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="companyRepresentativeTelephone"
            render={({ fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Telephone *</FormLabel>
                <div className="flex">
                  <FormField
                    control={form.control}
                    name="companyRepresentativeTelephone.countryCode"
                    render={({ field }) => (
                      <FormControl>
                        <PhonePrefix
                          invalid={!!fieldState.error}
                          onChange={field.onChange}
                          onPrefixChange={value => form.setValue("companyRepresentativeTelephone.prefix", value)}
                          className="min-w-20 max-w-fit rounded-r-none"
                          value={field.value}
                        />
                      </FormControl>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="companyRepresentativeTelephone.number"
                    render={({ field }) => (
                      <FormControl>
                        <Input
                          className="rounded-l-none"
                          type="number"
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                    )}
                  />
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="companyRepresentativeFax"
            render={({ fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Fax</FormLabel>
                <div className="flex">
                  <FormField
                    control={form.control}
                    name="companyRepresentativeFax.countryCode"
                    render={({ field }) => (
                      <FormControl>
                        <PhonePrefix
                          invalid={!!fieldState.error}
                          onChange={field.onChange}
                          onPrefixChange={value => form.setValue("companyRepresentativeFax.prefix", value)}
                          className="min-w-20 max-w-fit rounded-r-none"
                          value={field.value}
                        />
                      </FormControl>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="companyRepresentativeFax.number"
                    render={({ field }) => (
                      <FormControl>
                        <Input
                          className="rounded-l-none"
                          type="number"
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                    )}
                  />
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="companyRepresentativeEmail"
            render={({ field, fieldState }) => (
              <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                <FormLabel>Email address *</FormLabel>
                <FormControl>
                  <Input
                    invalid={!!fieldState.error}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </RemixForm>
    </Form>
  )
}
