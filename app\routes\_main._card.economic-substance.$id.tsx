import { Outlet, useLoaderData } from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import type { ReactNode } from "react";
import { SubmissionProvider } from "~/lib/submission/context/submission-context";
import { middleware } from "~/lib/middlewares.server";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { clientGetSubmission } from "~/services/api-generated";
import { getFirstStep, useFormSteps } from "~/lib/economic-substance/hooks/use-form-steps";
import { FormLayout } from "~/components/economic-substance/FormLayout";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function loader({ request, params }: LoaderFunctionArgs): Promise<never | {
  submissionData: Record<string, any>
  jurisdictionName: string
}> {
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireEsModule"], request);
  const { id } = params;
  if (!id) {
    throw new Error("Submission ID is required");
  }

  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission is required")
  }

  if (!params.pageName) {
    if (submission.status === SubmissionStatusNames.Draft || submission.status === SubmissionStatusNames.Revision
      || submission.status === SubmissionStatusNames.Temporal) {
      const firstStep = getFirstStep(company.jurisdictionName);
      // Redirect to the first page of the form
      throw redirect(`/economic-substance/${id}/${firstStep.page}`);
    }

    if (submission.status === SubmissionStatusNames.Submitted) {
      // Redirect to the submissions page
      throw redirect("/economic-substance/submissions");
    }
  }

  const submissionData = getUnflattenedDataSet(submission);

  return {
    submissionData,
    jurisdictionName: company.jurisdictionName,
  }
}

export default function EconomicSubstanceFormLayout(): ReactNode {
  const { submissionData, jurisdictionName } = useLoaderData<typeof loader>();
  const formSteps = useFormSteps(jurisdictionName);

  return (
    <SubmissionProvider submissionData={submissionData}>
      <FormLayout formSteps={formSteps} jurisdictionName={jurisdictionName}>
        <Outlet />
      </FormLayout>
    </SubmissionProvider>
  )
}
