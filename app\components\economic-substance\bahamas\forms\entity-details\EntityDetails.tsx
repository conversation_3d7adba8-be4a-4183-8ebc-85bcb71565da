import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import {
  Combobox,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem,
} from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import type { ReactNode } from "react";
import { useContext, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { SessionContext } from "~/components/session-context";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { type EntityDetailsSchemaType, entityDetailsSchema } from "~/lib/economic-substance/types/bahamas/entity-details-schema";
import {
  ECONOMIC_SUBSTANCE_FORM_ID,
  registeredAptUnit,
  registeredCountry,
  registeredEntityStreetNumberNameCity,
} from "~/lib/economic-substance/utilities/constants";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { getCountryOptions } from "~/lib/utilities/countries";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";

export function EntityDetails(): ReactNode {
  const { submissionData } = useSubmission();
  const navigation = useNavigation()
  const fetcher = useFetcher()
  const countryOptions = useMemo(() => getCountryOptions(), []);
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading" || fetcher.state === "submitting"
  const data = useMemo(() => submissionData[Pages.ENTITY_DETAILS] as EntityDetailsSchemaType, [submissionData]);
  const company = useContext(SessionContext).company;
  const form = useForm<EntityDetailsSchemaType>({
    resolver: zodResolver(entityDetailsSchema),
    shouldFocusError: false,
    defaultValues: data || {
      entityId: company?.incorporationNumber,
      entityTin: "",
      streetNumberNameCity: "",
      aptUnit: "",
      country: "",
    },
  });
  const { formState, setValue } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  function onSubmit(data: EntityDetailsSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const handleSameAddressChange = (value: EntityDetailsSchemaType["sameAddress"]) => {
    setValue("sameAddress", value)
    if (value === "true") {
      setValue("streetNumberNameCity", registeredEntityStreetNumberNameCity)
      setValue("aptUnit", registeredAptUnit)
      setValue("country", registeredCountry)
    } else {
      setValue("streetNumberNameCity", "")
      setValue("aptUnit", "")
      setValue("country", "")
    }
  }

  return (
    <div className="relative">
      <Form {...form}>
        <RemixForm
          onSubmit={form.handleSubmit(onSubmit, onError)}
          noValidate
          id={ECONOMIC_SUBSTANCE_FORM_ID}
          className="space-y-5"
        >
          <div className="flex-col space-y-7">
            <FormField
              control={form.control}
              name="entityId"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <FormLabel>
                    <p className="flex gap-1">
                      Entity Unique ID*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="entityTin"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <FormLabel>
                    <p className="flex gap-1">
                      Entity Taxpayer identification Number (TIN), if applicable
                    </p>
                  </FormLabel>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="sameAddress"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Physical Business Address is same as registered address*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={handleSameAddressChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                      disabled={isSubmitting}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="streetNumberNameCity"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <FormLabel>
                    <p className="flex gap-1">
                      Street Number/Name & City*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      {...field}
                      type="text"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="aptUnit"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <FormLabel>
                    <p className="flex gap-1">
                      Apt/Unit
                    </p>
                  </FormLabel>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      {...field}
                      type="text"
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="country"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <FormLabel>Country*</FormLabel>
                  <FormControl>
                    <Combobox
                      placeholder="Select a country"
                      searchText="Search..."
                      noResultsText="No countries found."
                      items={countryOptions}
                      onChange={field.onChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </RemixForm>
      </Form>
      <LoadingState
        isLoading={fetcher.state === "submitting" || fetcher.state === "loading"}
        message="Saving..."
      />
    </div>
  )
}
