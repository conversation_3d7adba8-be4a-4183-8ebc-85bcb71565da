import { useLoaderData } from "@remix-run/react";
import { SummaryTable } from "../table/SummaryTable";
import { SummaryTableRow } from "../table/SummaryTableRow";
import { SummaryTableData } from "../table/SummaryTableData";
import type { PageSlug } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import type { PremisesSchemaType } from "~/lib/economic-substance/types/bahamas/premises-schema";
import { transformBooleanStringToLabel } from "~/lib/economic-substance/utilities/summary";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.$id.summary";

export function Premises({ page }: { page: PageSlug }) {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { bahamasPremisesOwnership, premises } = submissionData[page] as PremisesSchemaType

  return (
    <div className="space-y-5">
      <h2 className="text-blue-500 font-thin mb-4 text-lg">3. Premises</h2>
      <SummaryTable>
        <tbody>
          <SummaryTableRow>
            <SummaryTableData>
              Does the entity own any premises in Bahamas?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(bahamasPremisesOwnership)}</SummaryTableData>
          </SummaryTableRow>
        </tbody>
      </SummaryTable>
      {bahamasPremisesOwnership === "true" && (
        <SummaryTable>
          <thead>
            <SummaryTableRow>
              <SummaryTableData>
                <p className="font-bold">Address Line 1</p>
              </SummaryTableData>
              <SummaryTableData><p className="font-bold">Address Line 2 </p></SummaryTableData>
              <SummaryTableData><p className="font-bold">Country</p></SummaryTableData>
            </SummaryTableRow>
          </thead>
          <tbody>
            {bahamasPremisesOwnership === "true" && premises && premises.map(p => (
              <SummaryTableRow key={`${p.addressLine1}-${p.country}`}>
                <SummaryTableData>
                  {p.addressLine1}
                </SummaryTableData>
                <SummaryTableData>{p.addressLine2}</SummaryTableData>
                <SummaryTableData>{p.country}</SummaryTableData>
              </SummaryTableRow>
            ))}
          </tbody>
        </SummaryTable>
      )}
    </div>
  )
}
