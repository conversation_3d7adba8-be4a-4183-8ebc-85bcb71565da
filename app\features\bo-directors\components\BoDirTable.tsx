import type { ReactNode } from "react";
import { useMemo, useState } from "react";
import {
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@netpro/design-system";
import {
  <PERSON>ertTriangle,
  ArrowDownAZ,
  ArrowUpAZ,
  CheckIcon,
  Maximize2,
  RepeatIcon,
  ScrollTextIcon,
} from "lucide-react";
import ComparisonModal from "./ComparisonModal";
import DetailedModal from "./DetailedModal";
import HistoryModal from "./HistoryModal";
import RowModal from "./RowModal";
import UpdateModal from "./UpdateModal";
import { BoDirCell } from "./BoDirCell";
import { sortItems } from "~/lib/bo-directors/utilities/bo-dir-sort";
import { hasEmptyFields } from "~/lib/bo-directors/utilities/bo-dir-empty-fields";
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";
import type { BeneficialOwnerType, DirectorType } from "~/lib/bo-directors/utilities/bo-directors-columns";

type ModalType = "detailed" | "individual" | "history" | "requestUpdate" | "compare";

type BoDirTableProps<T extends DirectorDTO | BeneficialOwnerDTO, K extends DirectorType | BeneficialOwnerType> = {
  title: string
  items: T[]
  type: K
  requiredFields: string[]
  visibleColumns: string[]
  columnsMap: Record<K, { columns: Record<string, string> }>
  sortFieldKeys: string[]
  keyPrefix: string
}

export function BoDirTable<T extends DirectorDTO | BeneficialOwnerDTO, K extends DirectorType | BeneficialOwnerType>({
  title,
  items,
  type,
  requiredFields,
  visibleColumns,
  columnsMap,
  sortFieldKeys,
  keyPrefix,
}: BoDirTableProps<T, K>): ReactNode {
  const [openModal, setOpenModal] = useState<ModalType | null>(null);
  const [selectedItem, setSelectedItem] = useState<T | null>(null);
  const [sortConfig, setSortConfig] = useState<{ key: string, direction: "ascending" | "descending" } | null>(null);
  const memoizedColumns = useMemo(() => {
    return columnsMap[type];
  }, [type, columnsMap]);
  const filteredColumns = useMemo(() => {
    if (!memoizedColumns) {
      return null;
    }

    return Object.fromEntries(
      Object.entries(memoizedColumns.columns).filter(([field]) => visibleColumns.includes(field)),
    );
  }, [memoizedColumns, visibleColumns]);
  const sortedItems = useMemo(() => sortItems(items, sortConfig), [items, sortConfig]);
  const handleOpenModalChange = (modalType: ModalType | null): void => {
    setOpenModal(modalType);
  };
  const handleSort = (field: string): void => {
    setSortConfig((prev) => {
      if (prev?.key === field) {
        return { key: field, direction: prev.direction === "ascending" ? "descending" : "ascending" };
      }

      return { key: field, direction: "ascending" };
    });
  };
  const handleRequestUpdateClick = (e: React.MouseEvent, item: T): void => {
    e.stopPropagation();
    setSelectedItem(item);
    handleOpenModalChange("requestUpdate");
  };
  const handleHistoryClick = (e: React.MouseEvent, item: T): void => {
    e.stopPropagation();
    setSelectedItem(item);
    handleOpenModalChange("history");
  };
  const handleCompareClick = (e: React.MouseEvent, item: T): void => {
    e.stopPropagation();
    setSelectedItem(item);
    handleOpenModalChange("compare");
  };

  return (
    <div>
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold mb-4">{title}</h2>
        <Button
          variant="link"
          className="flex gap-1.5 items-center text-sm"
          onClick={() => handleOpenModalChange("detailed")}
        >
          <Maximize2 size={16} className="text-blue-500" />
          <span className="text-xs font-semibold">Detailed view</span>
        </Button>
      </div>

      <div className="overflow-x-auto">
        <Table className="w-full">
          <TableHeader className="bg-teal-100">
            <TableRow>
              {filteredColumns
              && Object.entries(filteredColumns).map(([field, header]) => (
                <TableHead
                  key={`${keyPrefix}-header-${field}`}
                  className={`font-semibold text-teal-900 truncate ${
                    sortFieldKeys.includes(field) ? "cursor-pointer min-w-72" : "min-w-52"
                  }`}
                  onClick={() => sortFieldKeys.includes(field) && handleSort(field)}
                >
                  <div className="flex items-center justify-between">
                    {header}
                    {sortFieldKeys.includes(field)
                    && (sortConfig?.key === field
                      ? (
                          sortConfig.direction === "ascending"
                            ? (
                                <ArrowDownAZ size={16} className="text-blue-500" />
                              )
                            : (
                                <ArrowUpAZ size={16} className="text-blue-500" />
                              )
                        )
                      : (
                          <ArrowUpAZ size={16} className="text-black" />
                        ))}
                  </div>
                </TableHead>
              ))}
              <TableHead className="w-full"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="text-teal-900">
            {sortedItems.map((item, index) => (
              <TableRow
                key={(item as any).id}
                className={`${index % 2 === 0 ? "bg-white cursor-pointer" : "bg-teal-50 cursor-pointer"}`}
                onClick={() => {
                  setSelectedItem(item);
                  handleOpenModalChange("individual");
                }}
              >
                {filteredColumns
                && Object.keys(filteredColumns).map((field, colIndex) => (
                  <TableCell key={`${keyPrefix}-row-${field}-${(item as any).id}`}>
                    <span className="flex items-center">
                      {colIndex === 0 && hasEmptyFields(item, requiredFields) && (
                        <AlertTriangle size={16} className="text-orange-700 mr-2 flex-shrink-0" strokeWidth={3} />
                      )}
                      <span className="truncate">
                        <BoDirCell field={field} item={item} />
                      </span>
                    </span>
                  </TableCell>
                ))}
                <TableCell className="text-right py-0">
                  <div className="flex gap-2 justify-end">
                    {(item as any).metaData?.statusText !== "PendingUpdateRequest" && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex gap-1.5 items-center text-sm"
                        onClick={e => handleRequestUpdateClick(e, item)}
                      >
                        <RepeatIcon size={16} className="text-blue-500" />
                        <span className="hidden sm:inline text-xs font-semibold">Request update</span>
                      </Button>
                    )}
                    {(item as any).metaData?.statusText === "PendingUpdateRequest" && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex gap-2 items-center justify-center text-sm"
                        onClick={e => handleHistoryClick(e, item)}
                      >
                        <ScrollTextIcon size={16} className="text-blue-500" />
                        <span className="hidden sm:inline text-xs font-semibold">History</span>
                      </Button>
                    )}
                    {["UpdateReceived", "Initial", "Refreshed"].includes((item as any).metaData?.status || "")
                    && !hasEmptyFields(item, requiredFields) && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex gap-2 items-center justify-center text-sm"
                        onClick={e => handleCompareClick(e, item)}
                      >
                        <CheckIcon size={16} className="text-blue-500" />
                        <span className="hidden sm:inline text-xs font-semibold">Confirm</span>
                      </Button>
                    )}
                    {(item as any).metaData?.statusText === "Confirmed" && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex gap-2 items-center justify-center text-sm"
                        disabled
                      >
                        <CheckIcon size={16} className="text-blue-500" />
                        <span className="hidden sm:inline text-xs font-semibold">Confirmed</span>
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <DetailedModal
        open={openModal === "detailed"}
        handleOpenChange={() => handleOpenModalChange(null)}
        items={items}
        requiredFields={requiredFields}
        type={type}
      />
      <RowModal
        open={openModal === "individual"}
        handleOpenChange={() => handleOpenModalChange(null)}
        item={selectedItem as T}
        requiredFields={requiredFields}
        type={type}
      />
      <HistoryModal
        open={openModal === "history"}
        handleOpenChange={() => handleOpenModalChange(null)}
        selectedItem={selectedItem as T}
      />
      <UpdateModal
        open={openModal === "requestUpdate"}
        handleOpenChange={() => handleOpenModalChange(null)}
        selectedItem={selectedItem}
      />
      <ComparisonModal
        open={openModal === "compare"}
        handleOpenChange={() => handleOpenModalChange(null)}
        item={selectedItem as T}
        type={type}
      />
    </div>
  );
}
