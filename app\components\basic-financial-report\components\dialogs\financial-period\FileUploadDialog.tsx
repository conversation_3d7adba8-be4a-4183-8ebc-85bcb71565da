import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogHeader,
  DialogTitle,
  Dropzone,
  FileList,
  FileUploader,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  notify,
} from "@netpro/design-system";
import type { UseFormReturn } from "react-hook-form";
import type { FileRejection } from "react-dropzone";
import type { FileSchemaType } from "~/lib/basic-financial-report/types/file-schema";

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (data: FileSchemaType) => void
  form: UseFormReturn<FileSchemaType>
};

// Rejection messages for upload file component
function getRejectionMessage(rejectionCode: string) {
  switch (rejectionCode) {
    case "file-invalid-type":
      return "Invalid file type. Only images are allowed";
    case "file-too-large":
      return "File is too large. Max size is 5MB";
    case "too-many-files":
      return "Too many files. Max 1 file allowed";
    default:
      return "Unknown error";
  }
}

function handleRejections(rejections: <PERSON><PERSON><PERSON><PERSON><PERSON>[]) {
  rejections.forEach((rejection) => {
    notify({
      title: "File Rejected",
      message: `${
        rejection.file.name
      } was rejected. Reason: ${getRejectionMessage(rejection.errors[0].code)}`,
      variant: "error",
      duration: 5000,
    });
  });
}

const FORM_ID = "upload-files-dialog-form"

export function FileUploadDialog({
  open,
  setOpen,
  onSubmit,
  form,
}: Props) {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="p-2" noValidate id={FORM_ID}>
            <DialogHeader>
              <DialogTitle>Upload file</DialogTitle>
            </DialogHeader>
            <FormField
              control={form.control}
              name="files"
              render={({ field }) => (
                <FormItem className="pt-5">
                  <FormControl>
                    <>
                      <FileUploader
                        maxFiles={1}
                        files={field.value}
                        setFiles={field.onChange}
                        onReject={handleRejections}
                        allowedTypes={["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]}
                        maxSize={5 * 1024 * 1024}
                      >
                        <Dropzone>
                          <p>
                            Drop a file here or click to select a file.
                          </p>
                          <p className="mt-2 italic text-gray-400">
                            Max. of 1 file .XLSX only. File must not be password protected
                          </p>
                        </Dropzone>
                      </FileUploader>
                      <FileList files={field.value} setFiles={field.onChange} />
                    </>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter className="pt-4">
              <div className="flex w-full justify-end">
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                  <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                </div>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>

  );
}
