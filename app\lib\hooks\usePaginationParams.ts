import type { URLSearchParamsInit } from "react-router-dom";
import { useSearchParams } from "@remix-run/react";
import { useUserPreferences } from "./useUserPreferences";

export const PAGINATION = {
  PAGE_SIZE: 10, // Matches the API default value
  PAGE_NUMBER: 1, // Matches the API default value
  PAGE_RANGE: [10, 25, 50, 100],
} as const;

export type PageRange = typeof PAGINATION.PAGE_RANGE[number];

export type PaginationParams = {
  pageNumber: number
  pageSize: PageRange
  order?: string
  orderDirection?: string
}

/**
 * Get pagination parameters from the URL search params.
 * @returns Pagination parameters
 */
export function usePaginationParams(defaultValues?: URLSearchParamsInit): PaginationParams {
  const { tablePageSize } = useUserPreferences();
  const [searchParams] = useSearchParams(defaultValues);

  return {
    order: searchParams.get("order") ?? undefined,
    orderDirection: searchParams.get("orderDirection") ?? undefined,
    pageNumber: Number(searchParams.get("page") ?? PAGINATION.PAGE_NUMBER),
    pageSize: Number(tablePageSize ?? searchParams.get("pageSize") ?? PAGINATION.PAGE_SIZE) as PageRange,
  }
}
