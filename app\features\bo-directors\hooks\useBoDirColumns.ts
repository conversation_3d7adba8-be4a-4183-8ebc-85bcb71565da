import { useMemo } from "react";
import type { BeneficialOwnerType, DirectorType } from "~/lib/bo-directors/utilities/bo-directors-columns";
import { BeneficialOwnerColumns, DirectorColumns } from "~/lib/bo-directors/utilities/bo-directors-columns";
import { getVisibleBOColumns, getVisibleDirColumns } from "~/lib/bo-directors/utilities/bo-directors-get-visible-columns";
import type { BoDirOfficerType } from "~/lib/types/bo-dir-officer-type";
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";

type UseBoDirColumnsProps = {
  item: DirectorDTO | BeneficialOwnerDTO
  type: BeneficialOwnerType | DirectorType
  isBeneficialOwner: boolean
};

export type Column = {
  name: string
  field: keyof BeneficialOwnerDTO | keyof DirectorDTO
};

export function useBoDirColumns({ item, type, isBeneficialOwner }: UseBoDirColumnsProps): Column[] {
  const memoizedColumns = useMemo(() => {
    if (!item) {
      return [];
    }

    const boDirector = isBeneficialOwner ? item as BeneficialOwnerDTO : item as DirectorDTO;
    let visibleColumns: string[];

    /*
     * TBVI BO's have a officerTypeCode of "VGTP01" | "VGTP02" | "VGTP03" | "VGTP04" | "VGTP05" | "VGTP06";
     * TNEV BO's have a officerTypeCode of "KNTP01" | "KNTP02" | "KNTP03" | "KNTP04" | "KNTP05" | "KNTP06";
     */
    if (isBeneficialOwner) {
      visibleColumns = getVisibleBOColumns((boDirector as BeneficialOwnerDTO).officerTypeCode as BoDirOfficerType);
    } else {
      visibleColumns = getVisibleDirColumns(boDirector.isIndividual ? "INDIVIDUAL" : "CORPORATE");
    }

    const columnNames = isBeneficialOwner ? BeneficialOwnerColumns[type as BeneficialOwnerType].detailedColumns : DirectorColumns[type as DirectorType].detailedColumns;
    const columns = visibleColumns.map((column) => {
      return { name: columnNames[column as keyof typeof columnNames] || "", field: column as keyof typeof columnNames };
    });

    return columns;
  }, [item, type, isBeneficialOwner]);

  return memoizedColumns;
}
