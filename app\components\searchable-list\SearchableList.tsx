import type { JSX } from "react"
import { CenteredMessage } from "../errors/CenteredMessage";
import { SearchInputForm } from "./SearchInputForm"
import { SearchResultsList } from "./SearchResultsList"
import type { SearchResultsItemDTO } from "~/lib/types/search-results-type"

type SearchableListProps = {
  title: string
  placeholder?: string
  items?: SearchResultsItemDTO[]
  isSearching?: boolean
  onSelect: (itemId: string) => void
  onSearch: (searchTerm: string) => void
}

const defaultItems: SearchResultsItemDTO[] = [];

export default function SearchableList({
  title,
  placeholder = "Search...",
  items = defaultItems,
  isSearching,
  onSelect,
  onSearch,
}: SearchableListProps): JSX.Element {
  return (
    <div className="flex flex-col w-full h-full mx-auto gap-2.5">
      <div>
        <h2 className="text-2xl font-inter">{title}</h2>
      </div>

      <SearchInputForm placeholder={placeholder} onSearch={onSearch} isSearching={isSearching} />
      <div className="flex flex-col border border-gray-300 rounded bg-gray-100">
        {items.length === 0
          ? (
              <CenteredMessage title="No results found" />
            )
          : (
              <SearchResultsList items={items} onSelect={onSelect} />
            )}
      </div>
    </div>
  )
}
