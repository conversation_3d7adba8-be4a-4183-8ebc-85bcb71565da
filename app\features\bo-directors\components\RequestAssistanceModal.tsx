import {
  But<PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogD<PERSON><PERSON>,
  Di<PERSON><PERSON>ooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@netpro/design-system";
import { useFetcher } from "@remix-run/react";
import { type JSX, useState } from "react";
import type { LegalEntityRelationAssistanceRequestType } from "~/services/api-generated";

type RequestAssistanceButtonProps = {
  companyId: string
  assistanceRequestType: LegalEntityRelationAssistanceRequestType
  assistanceRequestComments: string
}

export function RequestAssistanceButton({
  companyId,
  assistanceRequestType,
  assistanceRequestComments,
}: RequestAssistanceButtonProps): JSX.Element {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const fetcher = useFetcher();
  const handleConfirmAssistance = (): void => {
    setIsModalOpen(false);
    fetcher.submit(
      {
        legalEntityId: companyId,
        assistanceRequestType,
        assistanceRequestComments,
      },
      { method: "post", action: `/api/bo-directors/${companyId}/assistance-request` },
    );
  };

  return (
    <>
      <Button type="button" variant="default" onClick={() => setIsModalOpen(true)} className="mt-4">
        Request Assistance
      </Button>
      <Dialog modal open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-md [&>button]:hidden">
          <DialogHeader>
            <DialogTitle className="text-2xl font-semibold">Request Assistance</DialogTitle>
            <DialogDescription>
              Do you want to request assistance?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex items-center justify-end gap-4">
            <Button variant="outline" size="sm" onClick={() => setIsModalOpen(false)}>
              <span className="text-xs font-bold">Cancel</span>
            </Button>
            <Button variant="default" size="sm" onClick={handleConfirmAssistance}>
              <span className="text-xs font-bold">Confirm</span>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
