import { addMonths, isBefore } from "date-fns";
import { z } from "zod";
import { nonNullDate, stringBoolean } from "~/lib/utilities/zod-validators";

export const financialPeriodSchema = z.object({
  hasFinancialPeriodChanged: stringBoolean(),
  startDate: nonNullDate("Start date"),
  endDate: nonNullDate("End date"),
  isReclassifiedToPEH: stringBoolean(),
  previousSubmissionEndDate: z.coerce.date().optional().nullable(),
})
  .refine(
    (data) => {
      if (!data.startDate || !data.endDate) {
        return true; // Skip if dates are not set
      }

      const maxEndDate = addMonths(data.startDate, 12);

      return isBefore(data.endDate, maxEndDate);
    },
    {
      message: "The end date must be within 12 months after the start date",
      path: ["endDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.startDate || !data.endDate) {
        return true; // Skip if dates are not set
      }

      return isBefore(data.startDate, data.endDate);
    },
    {
      message: "The start date cannot be later than the end date.",
      path: ["endDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.startDate || !data.endDate) {
        return true; // Skip if dates are not set
      }

      return isBefore(data.endDate, new Date());
    },
    {
      message: "The end date cannot be in the future.",
      path: ["endDate"],
    },
  );

export type FinancialPeriodSchemaType = z.infer<typeof financialPeriodSchema>;
