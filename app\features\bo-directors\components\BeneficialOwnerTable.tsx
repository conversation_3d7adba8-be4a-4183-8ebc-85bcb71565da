import { BoDirTable } from "./BoDirTable";
import { BeneficialOwnerColumns } from "~/lib/bo-directors/utilities/bo-directors-columns";
import type { BeneficialOwnerType } from "~/lib/bo-directors/utilities/bo-directors-columns";
import type { BeneficialOwnerDTO } from "~/services/api-generated";

type BeneficialOwnerTableProps = {
  title: string
  items: BeneficialOwnerDTO[]
  type: BeneficialOwnerType
  requiredFields: string[]
  visibleColumns: string[]
  jurisdictionName?: string
};

export default function BeneficialOwnerTable({
  title,
  items,
  type,
  requiredFields,
  visibleColumns,
  jurisdictionName,
}: BeneficialOwnerTableProps) {
  return (
    <BoDirTable<BeneficialOwnerDTO, BeneficialOwnerType>
      title={title}
      items={items}
      type={type}
      requiredFields={requiredFields}
      visibleColumns={visibleColumns}
      columnsMap={BeneficialOwnerColumns}
      sortFieldKeys={["name", "incorporationNumber"]}
      keyPrefix="bo"
      jurisdictionName={jurisdictionName}
    />
  );
}
