import type { Dispatch, SetStateAction } from "react";
import { useState } from "react";
import { useFieldArray, useForm, useFormContext } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type { CigaOutsourcingProvidersSchemaType, CigaSchema } from "~/lib/economic-substance/types/bahamas/ciga-schema";
import { cigaOutsourcingProvidersSchema } from "~/lib/economic-substance/types/bahamas/ciga-schema";
import type { ArrayFieldName } from "~/components/economic-substance/bahamas/forms/sections/CigaSection";

export function useCigaProvider(arrayFieldName: ArrayFieldName | undefined, setArrayFieldName: Dispatch<SetStateAction<ArrayFieldName | undefined>>) {
  const form = useFormContext<CigaSchema>()
  // Ciga providers table logic
  const [index, setIndex] = useState<number | undefined>();
  const [openDialog, setOpenDialog] = useState(false)
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const providerForm = useForm<CigaOutsourcingProvidersSchemaType>({
    resolver: zodResolver(cigaOutsourcingProvidersSchema),
    defaultValues: {
      entityName: "",
      detailsOfResources: "",
      numberOfStaff: "",
      monitoringAndControl: undefined,
      physicalAddress: "",
      monitoringControlExplanation: "",
    },
  });
  const providerArray = useFieldArray({
    control: form.control,
    name: "outsourcingProviders",
    keyName: "formArrayId",
  });
  function addProvider(fieldName: ArrayFieldName): void {
    providerForm.reset();
    setArrayFieldName(fieldName)
    setIndex(undefined);
    setOpenDialog(true);
  }

  function onSelect(fieldName: ArrayFieldName, provider: CigaOutsourcingProvidersSchemaType, index: number): void {
    setArrayFieldName(fieldName)
    providerForm.reset(provider, { keepDefaultValues: true });
    setIndex(index);
    setOpenDialog(true);
  }

  function onDelete(): void {
    if (arrayFieldName === "outsourcingProviders") {
      providerArray.remove(index);
    }

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(fieldName: ArrayFieldName, index: number): void {
    setArrayFieldName(fieldName)
    setIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setOpenDeleteConfirmation(false);
  }

  function onSubmit(data: CigaOutsourcingProvidersSchemaType): void {
    if (arrayFieldName === "outsourcingProviders") {
      if (index !== undefined) {
        providerArray.update(index, data);
      } else {
        providerArray.append(data);
      }
    }

    setOpenDialog(false);
  }

  return {
    providerForm,
    providerArray,
    addProvider,
    onSelect,
    onDelete,
    onOpenDeleteConfirmation,
    setOpenDeleteConfirmation,
    onCloseDeleteConfirmation,
    onSubmit,
    openDialog,
    setOpenDialog,
    openDeleteConfirmation,
  };
}
