import type { RelevantActivityDeclarationSchemaType } from "../types/bahamas/relevant-activity-declaration-schema";
import type { TaxPayerIdentificationSchemaType } from "../types/bahamas/tax-payer-identification-schema";
import type { PageSlug } from "./form-pages-bahamas";
import { Pages } from "./form-pages-bahamas";

function getSectionFromActivity(
  submission: Record<string, unknown>,
  currentPage: PageSlug,
  direction: "next" | "previous",
): string {
  const data = submission[Pages.TAX_PAYER_IDENTIFICATION] as TaxPayerIdentificationSchemaType;

  if (direction === "next" && currentPage === Pages.TAX_PAYER_IDENTIFICATION) {
    if (data.isBahamianResident === "true" && data.files_BusinessLicense) {
      return Pages.SUPPORTING_DETAILS
    }

    if (data.isBahamianResident === "false" && data.isInvestmentFund === "true") {
      return Pages.SUPPORTING_DETAILS
    }
  }

  if (direction === "previous" && currentPage === Pages.SUPPORTING_DETAILS) {
    if (data?.isBahamianResident === "true" && data.files_BusinessLicense) {
      return Pages.TAX_PAYER_IDENTIFICATION
    }

    if (data?.isBahamianResident === "false" && data.isInvestmentFund === "true") {
      return Pages.TAX_PAYER_IDENTIFICATION
    }
  }

  const relevantActivityDeclaration = submission[Pages.RELEVANT_ACTIVITY_DECLARATION] as RelevantActivityDeclarationSchemaType;
  const { relevantActivities } = relevantActivityDeclaration;
  const pagesOrder = [
    { page: Pages.RELEVANT_ACTIVITY_DECLARATION, selected: true },
    { page: Pages.TAX_PAYER_IDENTIFICATION, selected: relevantActivities?.[0].selected === "false" },
    { page: Pages.HOLDING_BUSINESS, selected: relevantActivities?.[1]?.selected === "true" },
    { page: Pages.FINANCE_LEASING_BUSINESS, selected: relevantActivities?.[2]?.selected === "true" },
    { page: Pages.BANKING_BUSINESS, selected: relevantActivities?.[3]?.selected === "true" },
    { page: Pages.INSURANCE_BUSINESS, selected: relevantActivities?.[4]?.selected === "true" },
    { page: Pages.FUND_MANAGEMENT_BUSINESS, selected: relevantActivities?.[5]?.selected === "true" },
    { page: Pages.HEADQUARTERS_BUSINESS, selected: relevantActivities?.[6]?.selected === "true" },
    { page: Pages.SHIPPING_BUSINESS, selected: relevantActivities?.[7]?.selected === "true" },
    { page: Pages.INTELLECTUAL_PROPERTY_BUSINESS, selected: relevantActivities?.[8]?.selected === "true" },
    { page: Pages.DISTRIBUTION_SERVICE_CENTRE_BUSINESS, selected: relevantActivities?.[9]?.selected === "true" },
    { page: Pages.SUPPORTING_DETAILS, selected: true },
  ];
  const currentIndex = pagesOrder.findIndex(page => page.page === currentPage);

  if (currentIndex === -1) {
    throw new Error(`The current page "${currentPage}" is not found in the pages order.`);
  }

  // Determine the slice direction based on "next" or "previous"
  const pageSlice = direction === "next" ? pagesOrder.slice(currentIndex + 1) : pagesOrder.slice(0, currentIndex).reverse();
  // Find the next or previous selected page
  const selectedPage = pageSlice.find(page => page.selected === true);

  if (selectedPage) {
    return selectedPage.page;
  }

  // If there are no more selected pages, return the default section
  return Pages.SUPPORTING_DETAILS;
}

export function getNextSectionFromActivity(
  submission: Record<string, unknown>,
  currentPage: PageSlug,
): string {
  return getSectionFromActivity(submission, currentPage, "next");
}

export function getPreviousSectionFromActivity(
  submission: Record<string, unknown>,
  currentPage: PageSlug,
): string {
  return getSectionFromActivity(submission, currentPage, "previous");
}
