import { <PERSON><PERSON>, <PERSON>rollA<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Pencil, X } from "lucide-react";
import type { JSX } from "react"
import type { EmployeeSchemaType } from "~/lib/economic-substance/types/bahamas/employee-schema";

type Props = {
  employees: (EmployeeSchemaType & { formArrayId: string })[]
  onSelect: (income: EmployeeSchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function EmployeesTable({
  employees,
  onSelect,
  onDelete,
  disabled,
}: Props): JSX.Element {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Full Name</TableHead>
              <TableHead>Qualification</TableHead>
              <TableHead>Years of Relevant Experience</TableHead>
              <TableHead>Contract Type</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!employees.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No employees available
                </TableCell>
              </TableRow>
            )}
            {employees.length > 0 && employees.map((item, index) => (
              <TableRow key={item.formArrayId}>
                <TableCell>{item.fullName}</TableCell>
                <TableCell>{item.qualification}</TableCell>
                <TableCell>{item.yearsOfExperience}</TableCell>
                <TableCell>{item.contractType}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(item, index)} disabled={disabled}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
