import type { BoDirOfficerType } from "~/lib/types/bo-dir-officer-type";
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";

type DirectorType = "INDIVIDUAL" | "CORPORATE";

export function getRequiredBOFields(officerType: BoDirOfficerType): Array<keyof BeneficialOwnerDTO> {
  switch (officerType) {
    // Nevis BO
    case "KNTP01": // Individual BO
      return ["name", "dateOfBirth", "countryOfBirth", "nationality", "residentialAddress"];
    case "KNTP02":
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation"];
    case "KNTP03":
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation"];
    case "KNTP04":
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation"];
    case "KNTP05":
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation"];
    case "KNTP06":
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation"];
    // BVI BO
    case "VGTP01":
      return ["name", "dateOfBirth", "countryOfBirth", "nationality", "residentialAddress"];
    case "VGTP02":
    case "VGTP06":
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation"];
    case "VGTP03":
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation", "nameOfRegulator", "jurisdictionOfRegulator"];
    case "VGTP04":
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation", "sovereignState"];
    case "VGTP05":
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation", "stockExchange", "stockCode"];
    default:
      return ["name", "incorporationNumber", "dateOfIncorporation", "address", "countryOfFormation"];
  }
}

export function getRequiredDirFields(directorType: DirectorType): Array<keyof DirectorDTO> {
  switch (directorType) {
    case "INDIVIDUAL":
      return [
        "name",
        "officerTypeName",
        "dateOfBirth",
        "countryOfBirth",
        "nationality",
        "appointmentDate",
        "residentialAddress",
      ];
    case "CORPORATE":
      return [
        "name",
        "officerTypeName",
        "incorporationNumber",
        "dateOfIncorporation",
        "incorporationCountry",
        "appointmentDate",
        "address",
      ];
    default:
      return [];
  }
}
