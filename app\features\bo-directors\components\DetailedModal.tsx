import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@netpro/design-system"
import { AlertTriangle, ArrowDownAZ, ArrowUpAZ } from "lucide-react"
import type { JSX, ReactNode } from "react"
import { useState } from "react"
import { BoDirCell } from "./BoDirCell"
import { type SortConfig, sortItems } from "~/lib/bo-directors/utilities/bo-dir-sort"
import type {
  BeneficialOwnerType,
  DirectorType,
} from "~/lib/bo-directors/utilities/bo-directors-columns"
import {
  BeneficialOwnerColumns,
  DirectorColumns,
} from "~/lib/bo-directors/utilities/bo-directors-columns"
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated"

type DetailedViewProps = {
  open: boolean
  handleOpenChange: (isOpen: boolean) => void
  items: DirectorDTO[] | BeneficialOwnerDTO[]
  requiredFields: string[]
  type: BeneficialOwnerType | DirectorType
}

export default function DetailedModal({
  open,
  handleOpenChange,
  items,
  requiredFields,
  type,
}: DetailedViewProps): JSX.Element {
  const memoizedColumns
    = type in BeneficialOwnerColumns
      ? BeneficialOwnerColumns[type as keyof typeof BeneficialOwnerColumns]
      : DirectorColumns[type as keyof typeof DirectorColumns]
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
  const sortedItems = sortItems(items as DirectorDTO[], sortConfig);
  const handleSort = (key: string): void => {
    let direction: "ascending" | "descending" = "ascending";
    if (sortConfig && sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }

    setSortConfig({ key, direction });
  };

  return (
    <Dialog modal open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-full h-[calc(100vh-4rem)] w-[calc(100vw-4rem)] [&>button]:hidden flex flex-col">
        <DialogHeader>
          <DialogDescription>Detailed View</DialogDescription>
          <DialogTitle>
            {`${type.startsWith("CORPORATE") ? "Corporate" : "Individual"} ${type.endsWith("BO") ? "Beneficial Owners" : "Officers"} for `}
            <span className="text-blue-500">{`${items[0]?.legalEntityName}`}</span>
          </DialogTitle>
        </DialogHeader>
        <div className="overflow-x-auto flex-grow min-h-[50vh]">
          <div className="overflow-x-auto h-full">
            <Table className="w-auto text-teal-900 min-w-full">
              <TableHeader className="bg-teal-100 sticky top-0">
                <TableRow>
                  {memoizedColumns
                  && Object.entries(memoizedColumns.detailedColumns).map(
                    ([field, header]) => (
                      <TableHead
                        key={`modal-bo-dir-${field}`}
                        className="whitespace-nowrap px-4 text-teal-900 cursor-pointer"
                        onClick={() => handleSort(field)}
                      >
                        <div className="flex items-center">
                          {header as ReactNode}
                          {sortConfig && sortConfig.key === field
                            ? (
                                sortConfig.direction === "ascending"
                                  ? (
                                      <ArrowDownAZ className="ml-2 h-4 w-4 text-blue-500" />
                                    )
                                  : (
                                      <ArrowUpAZ className="ml-2 h-4 w-4 text-blue-500" />
                                    )
                              )
                            : (
                                <ArrowDownAZ className="ml-2 h-4 w-4 text-black" />
                              )}
                        </div>
                      </TableHead>
                    ),
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedItems.map((item, index) => (
                  <TableRow
                    key={`modal-bo-dir-row-${item.id}`}
                    className={index % 2 === 0 ? "bg-white" : "bg-teal-50"}
                  >
                    {memoizedColumns
                    && Object.keys(memoizedColumns.detailedColumns).map(field => (
                      <TableCell key={`modal-bo-dir-row-${field}-${item.id}`} className="whitespace-nowrap px-4">
                        <span className="flex items-center">
                          {!item[field as keyof DirectorDTO] && requiredFields.includes(field) && (
                            <AlertTriangle
                              size={16}
                              className="text-orange-700 mr-2 flex-shrink-0"
                              strokeWidth={3}
                            />
                          )}
                          <BoDirCell field={field} item={item} />
                        </span>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        <DialogFooter className="mt-auto">
          <Button
            type="button"
            variant="secondary"
            onClick={() => handleOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
