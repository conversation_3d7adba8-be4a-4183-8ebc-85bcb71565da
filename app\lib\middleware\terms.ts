import type { TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { getSessionData } from "~/lib/auth/utils/session.server";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";

type TermsAndConditionsData = {
  termsAndConditionsAccepted: boolean
};

export default async function terms({ request }: MiddlewareProps): MiddlewareResponse<TypedResponse<never> | TermsAndConditionsData> {
  const { termsAndConditionsAccepted } = await getSessionData(request);
  if (!termsAndConditionsAccepted) {
    throw redirect("/auth/terms-and-conditions");
  }

  return { termsAndConditionsAccepted: Boolean(termsAndConditionsAccepted) };
}
