import { Badge, cn } from "@netpro/design-system";
import { Link, useRouteLoaderData } from "@remix-run/react";
import type { LucideIcon } from "lucide-react";

export type MenuItem = {
  label: string
  href: string
  icon: LucideIcon
  children?: Omit<MenuItem, "icon">[]
  show: boolean
  count?: number
  entityTypeGuard: string[]
};

export type MenuItemProps = {
  item: MenuItem
  active: boolean
};

export default function MenuItemComponent({ item, active }: MenuItemProps): JSX.Element {
  const mainLoader = useRouteLoaderData<{ totalMessages: number, totalUnreadMessages: number }>("routes/_main")

  if (!mainLoader) {
    throw new Error("Error accessing total inbox messages")
  }

  const { totalUnreadMessages } = mainLoader;

  return (
    <li>
      {/* TODO: Should be a NavLink component */}
      <Link
        to={item.href}
        className={cn(
          active ? "bg-gray-100" : "hover:bg-gray-100",
          "group flex w-full items-center gap-2.5 rounded-md py-2.5 px-2 text-sm font-semibold font-inter leading-5 text-gray-800",
        )}
      >
        <item.icon
          className={cn(active ? "text-primary" : "text-gray-400 group-hover:text-primary", "h-6 w-6 shrink-0")}
          aria-hidden="true"
        />
        <span className="block overflow-hidden text-ellipsis w-full" title={item.label}>
          {item.label}
          {item.label === "Inbox" && (
            <Badge variant="info" className="float-right">
              {totalUnreadMessages || 0}
            </Badge>
          )}
        </span>
      </Link>
    </li>
  );
}
