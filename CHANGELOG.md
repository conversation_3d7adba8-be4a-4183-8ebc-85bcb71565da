# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [2.20.0-beta.37](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.36&targetVersion=GTv2.20.0-beta.37&_a=files) (2025-07-11)


### Bug Fixes

* revert "feat: RFI - Notification on menu and submenu", reverts fix for [#18161](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18161) ([76db1d9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/76db1d9557e62beb9243f07cf2fde4a669ca0b96))

## [2.20.0-beta.36](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.35&targetVersion=GTv2.20.0-beta.36&_a=files) (2025-07-11)


### Features

* RFI - Notification on menu and submenu ([e17b9f3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e17b9f3ae022a2a8ad979f6cfe3b6e247e1e9cc6))

## [2.20.0-beta.35](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.34&targetVersion=GTv2.20.0-beta.35&_a=files) (2025-07-08)


### Bug Fixes

* update client portal text, refers [#18081](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/18081) ([ff17e2b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ff17e2bbd1e47d7f010a435fa0a1192449067add))

## [2.20.0-beta.34](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.33&targetVersion=GTv2.20.0-beta.34&_a=files) (2025-07-07)

## [2.20.0-beta.33](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.32&targetVersion=GTv2.20.0-beta.33&_a=files) (2025-07-04)

## [2.20.0-beta.32](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.31&targetVersion=GTv2.20.0-beta.32&_a=files) (2025-07-04)


### Bug Fixes

* Multiple bugs in submissions ([b28cc46](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b28cc462d82109999a0eb00cc2a8dd162fedbe91))

## [2.20.0-beta.31](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.30&targetVersion=GTv2.20.0-beta.31&_a=files) (2025-06-27)


### Bug Fixes

* Remaining bugs for sprint 27 ([b2b2c55](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b2b2c552723c1de78376b8c212951105b6cf96ec))

## [2.20.0-beta.30](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.29&targetVersion=GTv2.20.0-beta.30&_a=files) (2025-06-26)


### Bug Fixes

* timezone essubmissions ([ed7e332](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ed7e3329dceb4799c4f965fcfc65400be4103395))

## [2.20.0-beta.29](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.28&targetVersion=GTv2.20.0-beta.29&_a=files) (2025-06-25)

## [2.20.0-beta.28](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.27&targetVersion=GTv2.20.0-beta.28&_a=files) (2025-06-24)


### Bug Fixes

* the bugs with # on address ([6cf7a34](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6cf7a3461d44057493438b3717d755e46f58e47f))

## [2.20.0-beta.27](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.26&targetVersion=GTv2.20.0-beta.27&_a=files) (2025-06-24)

## [2.20.0-beta.26](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.25&targetVersion=GTv2.20.0-beta.26&_a=files) (2025-06-24)


### Bug Fixes

* Multiple es submission bugs ([bf7c4f6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bf7c4f62e1d7671a34d771f25f134c89ee590bee))

## [2.20.0-beta.25](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.24&targetVersion=GTv2.20.0-beta.25&_a=files) (2025-06-20)


### Bug Fixes

* update financial report date ([533fdc7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/533fdc77f3e38d7e88e647e551bf65dbace1ef10))

## [2.20.0-beta.24](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.23&targetVersion=GTv2.20.0-beta.24&_a=files) (2025-06-20)


### Bug Fixes

* error on submissions page, date object ([cb58fa5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/cb58fa5faaeb4a96d7c86f20102c37337955fe6e))

## [2.20.0-beta.23](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.22&targetVersion=GTv2.20.0-beta.23&_a=files) (2025-06-19)


### Bug Fixes

* inbox issue show the modal ([4bd41e0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/4bd41e06282bedc00fd77f7b22b39e21f9069b38))

## [2.20.0-beta.22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.21&targetVersion=GTv2.20.0-beta.22&_a=files) (2025-06-19)

## [2.20.0-beta.21](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.20&targetVersion=GTv2.20.0-beta.21&_a=files) (2025-06-17)


### Bug Fixes

* rfi documents ([da89617](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/da896170898b18ff7085b4e157dd17d074f5820c))

## [2.20.0-beta.20](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.19&targetVersion=GTv2.20.0-beta.20&_a=files) (2025-06-17)

## [2.20.0-beta.19](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.18&targetVersion=GTv2.20.0-beta.19&_a=files) (2025-06-14)

## [2.20.0-beta.18](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.17&targetVersion=GTv2.20.0-beta.18&_a=files) (2025-06-14)


### Bug Fixes

* form2show ([2df80dc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2df80dc1037d38d302e96129cef5213078edcafa))

## [2.20.0-beta.17](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.16&targetVersion=GTv2.20.0-beta.17&_a=files) (2025-06-13)

## [2.20.0-beta.16](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.15&targetVersion=GTv2.20.0-beta.16&_a=files) (2025-06-13)


### Bug Fixes

* remove position ([fc1623a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/fc1623ad2723937c6f48560a567c06e0e412b869))

## [2.20.0-beta.15](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.14&targetVersion=GTv2.20.0-beta.15&_a=files) (2025-06-13)

## [2.20.0-beta.14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.13&targetVersion=GTv2.20.0-beta.14&_a=files) (2025-06-13)


### Bug Fixes

* create the function to check hash ([e4cc69f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e4cc69fc1379722548018476a033092d7f367bba))

## [2.20.0-beta.13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.12&targetVersion=GTv2.20.0-beta.13&_a=files) (2025-06-13)

## [2.20.0-beta.12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.11&targetVersion=GTv2.20.0-beta.12&_a=files) (2025-06-13)


### Bug Fixes

* hash to fix loading str subs ([d8a3ea5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d8a3ea5a9db0b1fd7db1ed8a496dde2b5d7aaebf))

## [2.20.0-beta.11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.10&targetVersion=GTv2.20.0-beta.11&_a=files) (2025-06-13)


### Bug Fixes

* loading str submission ([b9e6c6d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b9e6c6d48c5265b4461184eb9d8d30caa6f42a00))

## [2.20.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.9&targetVersion=GTv2.20.0-beta.10&_a=files) (2025-06-13)


### Bug Fixes

* loading problem with str submission ([e6d3d1e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e6d3d1ea6f4a79ac4728962beab267dacc09d0f6))

## [2.20.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.8&targetVersion=GTv2.20.0-beta.9&_a=files) (2025-06-13)

## [2.20.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.7&targetVersion=GTv2.20.0-beta.8&_a=files) (2025-06-13)


### Bug Fixes

* string encoder decoder ([d44aa46](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d44aa464f6195b827e2fa6e7b9c9ad191554bf7e))

## [2.20.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GTv2.20.0-beta.6&targetVersion=GTv2.20.0-beta.7&_a=files) (2025-06-12)

## [2.20.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.5&targetVersion=GTv2.20.0-beta.6&_a=files) (2025-06-05)


### Bug Fixes

* update menu item ([ac0ced5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ac0ced53b7f99671eb817843ed10d051c95d4c40))

## [2.20.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.4&targetVersion=GTv2.20.0-beta.5&_a=files) (2025-06-04)

## [2.20.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.3&targetVersion=GTv2.20.0-beta.4&_a=files) (2025-05-26)

## [2.20.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.2&targetVersion=GTv2.20.0-beta.3&_a=files) (2025-05-26)

## [2.20.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.1&targetVersion=GTv2.20.0-beta.2&_a=files) (2025-05-26)

## [2.20.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-alpha.2&targetVersion=GTv2.20.0-beta.1&_a=files) (2025-05-26)

## [2.20.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-alpha.2&targetVersion=GTv2.20.0-beta.0&_a=files) (2025-05-26)

## [2.20.0-alpha.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-alpha.1&targetVersion=GTv2.20.0-alpha.2&_a=files) (2025-05-22)

## [2.20.0-alpha.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-alpha.0&targetVersion=GTv2.20.0-alpha.1&_a=files) (2025-05-22)

## [2.20.0-alpha.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.4&targetVersion=GTv2.20.0-alpha.0&_a=files) (2025-05-21)

## [2.20.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.3&targetVersion=GTv2.20.0-beta.4&_a=files) (2025-05-20)


### Features

* Show app version in UI, refers [#17393](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/17393) ([d134724](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d13472442122dc8ddf1f44ca66335c97b0efb68a))

## [2.20.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.2&targetVersion=GTv2.20.0-beta.3&_a=files) (2025-05-20)

## [2.20.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.1&targetVersion=GTv2.20.0-beta.2&_a=files) (2025-05-19)

## [2.20.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.20.0-beta.0&targetVersion=GTv2.20.0-beta.1&_a=files) (2025-05-19)

## [2.20.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.19.0&targetVersion=GTv2.20.0-beta.0&_a=files) (2025-05-07)


### Features

* Add logic to allow BVI ES along with the current TBAH ES, refers [#14441](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14441) ([c237829](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c23782921a7a6d561aa6fee6f34c836ccfd85e09))
* **CI:** Enable deploy of dev-tbah to dev environment ([b1baa4a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b1baa4a10fda1236b7ecab3e2754b2e4ee2560fa))
* split Bahamas and BVI logic for ES and fixes conflicts ([24c36f7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/24c36f77713b0e930a9efbaa50d125728eacc043))
* **TBAH Form:** created form, added draft page, submissions overview, closes [#13490](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13490), [#11713](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11713), [#11714](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11714), [#11715](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11715), [#11717](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11717), [#11718](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11718), [#13491](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13491), [#11719](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11719), [#11720](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11720), [#11721](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11721), [#11722](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11722), [#11723](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11723), [#11726](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11726), [#11724](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11724), [#11727](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11727), #[#11725](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11725), [#11728](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11728), [#13459](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13459), [#12112](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12112), [#12114](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12114) ([4c4a6c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/4c4a6c125c56dc8f0490ca198e94751b63e0e415))


### Bug Fixes

* add missing validations for directors and fix bo/dirs, refers [#15879](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15879), [#15987](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15987) ([7fde882](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7fde8828b8edef9bb4b15d9f84fd4d7d8c02d45d))
* bug fixes for ES, refers [#15846](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15846), [#15847](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15847), [#15848](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15848), [#15849](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15849), [#15860](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15860), [#15933](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15933) ([e233b06](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e233b0632c28a43e7f8b29d26fcb7f64b6d04b64))
* bug fixes for submission actions, refers [#16183](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16183), [#16064](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16064), [#16062](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16062) ([5b30b5c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5b30b5c2ced1c946bc2c4a40fe2a366f941bfc42))
* delete email from PDF, refers [#16213](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16213) ([ea08596](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ea08596214fd2b551cf53ff6a8bdc47da3977f79))
* fix conflicts and deprecated component ([40dd978](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/40dd978f644dbbcd22f1828db144a72b812aefaa))
* fix ES Bah logic for first and second steps, refers [#15733](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15733), 15740, [#15732](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15732) ([fbf0947](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/fbf0947d73d7d4427784c3c5163ac647282ef10e))
* fix lint ([c3bdbe5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c3bdbe5c28ac5d86d460e4375d3d58133875d754))
* re add inbox menu item ([b060e67](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b060e6794b5769cb09fad61bbcee59fac467b137))
* services and update the missing files ([d0c3a5c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d0c3a5cdbf6499913dbd330b27b46aa24c08c6e6))
* services and update the missing files ([a5c3e47](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a5c3e47ba63e03beff5f3d92d7c75584581ee1bd))
* update logic to load financial period, refers [#15851](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15851) ([058f8c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/058f8c141af28c1e03aa1b1a274dde5ff75cba91))
* visual and logic fixes in ES Bahamas, refers [#15950](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15950), [#15953](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15953), [#15957](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15957), [#15982](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15982), [#15983](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15983), [#15984](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15984), [#15985](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15985), [#15986](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15986), [#15988](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15988), [#15993](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15993), [#15995](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15995), [#15996](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15996) ([560a64f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/560a64f2891093afcb61aee4cb8e46de053a5fbb))

## [2.19.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.19.0-beta.0&targetVersion=GTv2.19.0&_a=files) (2025-05-06)

## [2.19.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.18.0&targetVersion=GTv2.19.0-beta.0&_a=files) (2025-05-02)


### Features

* allow "No country" for tax residency question, refers [#16904](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16904) ([2583ac7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2583ac7df40a4ec329fecf6d4ff43e8d2c591a9a))

## [2.18.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.18.0-beta.0&targetVersion=GTv2.18.0&_a=files) (2025-04-11)

## [2.18.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.4&targetVersion=GTv2.18.0-beta.0&_a=files) (2025-04-11)


### Features

* add new tabs for pending payments and paginate tables, refers [#16491](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16491) ([b399649](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b399649d9f7d7b104cb25c85e774099e32c2cfbd))

## [2.17.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.3&targetVersion=GTv2.17.4&_a=files) (2025-04-11)


### Bug Fixes

* Fixed app service name ([3ac4028](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3ac4028ae5309d47e5793c9bf45189c7435af08b))

## [2.17.4-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.3&targetVersion=GTv2.17.4-beta.0&_a=files) (2025-04-11)


### Bug Fixes

* Fixed app service name ([3ac4028](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3ac4028ae5309d47e5793c9bf45189c7435af08b))

## [2.17.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.3-beta.0&targetVersion=GTv2.17.3&_a=files) (2025-04-11)

## [2.17.3-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.2&targetVersion=GTv2.17.3-beta.0&_a=files) (2025-04-11)


### Bug Fixes

* fix removed masterclient filter, refers [#16491](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16491) ([7cb468c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7cb468c2efbf92bdc752c72672a56b5c62ac1041))

## [2.17.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.2-beta.0&targetVersion=GTv2.17.2&_a=files) (2025-04-11)

## [2.17.2-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.1&targetVersion=GTv2.17.2-beta.0&_a=files) (2025-04-11)


### Bug Fixes

* increase limit for invoices and use generated API, refers [#16491](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16491) ([314b2cd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/314b2cd94be733a95b4509915f2e8dd6a9cb8304))

## [2.17.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.1-beta.0&targetVersion=GTv2.17.1&_a=files) (2025-04-10)

## [2.17.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.0&targetVersion=GTv2.17.1-beta.0&_a=files) (2025-04-10)


### Bug Fixes

* add no country logic, refers [#16464](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16464) ([928d4d3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/928d4d322f09009bbdc37f2ff641d20a429ff61e))

## [2.17.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.0-beta.1&targetVersion=GTv2.17.0&_a=files) (2025-04-07)

## [2.17.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.17.0-beta.0&targetVersion=GTv2.17.0-beta.1&_a=files) (2025-04-07)


### Features

* add new T&C document, refers [#16351](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16351) ([dbba273](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/dbba27300fb4c0ff2ac63d5c3edc94fc5e9c237c))


### Bug Fixes

* fix redirect from completed submissions to new, refers [#16352](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16352) ([a0e2ac1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a0e2ac180951e49d834bc41eb49198c2f2252bc1))

## [2.17.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.16.2-beta.0&targetVersion=GTv2.17.0-beta.0&_a=files) (2025-04-04)


### Features

* add logic for STR 2024 payments without invoice, refers [#16305](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16305), [#16306](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16306) ([67920a4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/67920a4fb873a1df005e4bde1dba643f33d184f1))
* add Netherland Antilles to country list, refers [#16272](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16272) ([de24a9f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/de24a9fb03ee63be8e39bad1ca8f1278f17f232f))


### Bug Fixes

* fix officer type column missing, refers [#16317](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16317) ([fda8d51](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/fda8d51eebd3ff7d03fc2376216da893d2786d40))
* fix redirection to client API route when trying to switch master client, refers [#16203](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16203), [#16205](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16205) ([0f2586d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0f2586d3870abd896ded0eb63c0ae734afb61588))

## [2.16.2-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.16.1&targetVersion=GTv2.16.2-beta.0&_a=files) (2025-04-04)

## [2.16.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.16.1-beta.0&targetVersion=GTv2.16.1&_a=files) (2025-03-28)

## [2.16.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.16.0&targetVersion=GTv2.16.1-beta.0&_a=files) (2025-03-28)


### Bug Fixes

* add headers to each API call, refers [#16238](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/16238) ([e918049](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e918049bd7663ee69047777a5efcfc6c96456aea))

## [2.16.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.16.0-beta.0&targetVersion=GTv2.16.0&_a=files) (2025-03-12)

## [2.16.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.15.1-beta.0&targetVersion=GTv2.16.0-beta.0&_a=files) (2025-03-11)


### Features

* add str form for 2024, refers [#15800](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15800) ([198276b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/198276bab61129cc2e2d9714e2d38d4636f804f6))


### Bug Fixes

* add new mapping for officerType name and code, refers [#15805](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15805) ([3fbcfa2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3fbcfa255ad2bd722734ad0603083c3183d1272d))

## [2.15.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.15.0&targetVersion=GTv2.15.1-beta.0&_a=files) (2025-03-10)

## [2.15.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.15.0-beta.0&targetVersion=GTv2.15.0&_a=files) (2025-02-27)

## [2.15.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.14.0&targetVersion=GTv2.15.0-beta.0&_a=files) (2025-02-27)


### Features

* update reset password modal text, refers [#15719](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15719) ([3c93912](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3c93912ee600a210a9f99959c1e7fd9369157ead))

## [2.14.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.14.0-beta.0&targetVersion=GTv2.14.0&_a=files) (2025-02-26)

## [2.14.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.13.0&targetVersion=GTv2.14.0-beta.0&_a=files) (2025-02-26)


### Features

* add modal for reset password information, refers [#15710](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15710) ([2436533](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/24365330b7606ce7d13de0beeb269ac604af7162))


### Bug Fixes

* add blue color to bo/dir name, add missing column and fix order, refers [#15481](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15481) ([a76b2e8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a76b2e8a9233e3029d2b07062d9006ed61d692a4))
* update BO/Dir labels and landing page email address, refers [#15608](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15608), [#15572](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15572) ([77291d2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/77291d24c5cef7ddb1488925ebe118d05f55bcd5))

## [2.13.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.13.0-beta.1&targetVersion=GTv2.13.0&_a=files) (2025-02-20)

## [2.13.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.13.0-beta.0&targetVersion=GTv2.13.0-beta.1&_a=files) (2025-02-20)


### Bug Fixes

* avoid wrap of required symbol in different row, refers [#15563](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15563) ([627ef9b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/627ef9bdb6cb10e8d8ecb8212be9ce4ad3b034e6))
* update label for corporate director, refers [#15562](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15562) ([b975c52](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b975c52307b25ab10b97dbe87433da065819ff99))

## [2.13.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.12.0&targetVersion=GTv2.13.0-beta.0&_a=files) (2025-02-19)


### Bug Fixes

* BO/Director column fixes and refactor, refers [#15497](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15497), [#15539](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15539), [#15520](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15520) ([54a5212](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/54a52129e0ecf8bdeec9999be40a419ae378ff89))

## [2.12.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.11.1-beta.0&targetVersion=GTv2.12.0&_a=files) (2025-02-17)


### Bug Fixes

* update layout for payments, naming for corporate address page and MNE tooltip, refers [#15476](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15476), 15490, [#15491](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15491) ([717d2b7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/717d2b71461300492030aeefdb77eec6f81c99dc))
* update visible columns and order for BO/Dir, refers [#15481](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15481) ([db9ba11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/db9ba117b23faba7c23f37f3ad878792026c2d8c))

## [2.11.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.11.0&targetVersion=GTv2.11.1-beta.0&_a=files) (2025-02-17)


### Bug Fixes

* update layout for payments, naming for corporate address page and MNE tooltip, refers [#15476](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15476), 15490, [#15491](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15491) ([717d2b7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/717d2b71461300492030aeefdb77eec6f81c99dc))
* update visible columns and order for BO/Dir, refers [#15481](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15481) ([db9ba11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/db9ba117b23faba7c23f37f3ad878792026c2d8c))

## [2.11.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.11.0-beta.2&targetVersion=GTv2.11.0&_a=files) (2025-02-13)

## [2.11.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.11.0-beta.1&targetVersion=GTv2.11.0-beta.2&_a=files) (2025-02-13)


### Features

* **CICD:** Use self-hosted agent pool ([0cb54a2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0cb54a29b0dd8e60456c78e6cdd4b4e244be8f1d))

## [2.11.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.11.0-beta.0&targetVersion=GTv2.11.0-beta.1&_a=files) (2025-02-13)


### Bug Fixes

* fix STR PDF, form label and director approval, refers [#15415](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15415), [#15414](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15414), [#15410](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15410), [#15412](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15412) ([8f07e42](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8f07e42a3a8e081c42de1970a7c634db41e30804))
* fix uniform date formatting, refers [#15275](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15275) ([3f74a22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3f74a226a592c7f739c84280404bf2cee68ef460))

## [2.11.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.10.0&targetVersion=GTv2.11.0-beta.0&_a=files) (2025-02-10)


### Features

* add reset filters button and update label, refers [#15258](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15258), [#15255](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15255) ([455af11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/455af115809a7ef15e1216eb2db08d341c1ee4e5))
* added dropdown to quick registration refers [#15251](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15251) ([8708026](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8708026f4ff4ef5abcdfa42dccb151c2984c15d4))

## [2.10.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.10.0-beta.0&targetVersion=GTv2.10.0&_a=files) (2025-02-07)

## [2.10.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0&targetVersion=GTv2.10.0-beta.0&_a=files) (2025-02-07)


### Features

* add pagination hook for PDF generation, refers [#15025](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15025) ([c8e4b8a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c8e4b8a1019174cc8caa42ce002fb3e04ca94a3b))
* update invoice row layout and contact email refers [#15254](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15254) [#15256](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15256) ([c3cd18d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c3cd18d1aaab7dfe1c8bf956bafdaa46a5445970))


### Bug Fixes

* change required field for individual dir refers [#14944](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14944) ([7d9a929](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7d9a929a07ab1c6d8cf742fb66c778cf31badde0))

## [2.9.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.9&targetVersion=GTv2.9.0&_a=files) (2025-02-03)

## [2.9.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.8&targetVersion=GTv2.9.0-beta.9&_a=files) (2025-02-03)


### Bug Fixes

* hide str from sidebar for non LLC IBC companies refers [#14900](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14900) ([ac6a46f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ac6a46f77f4f03a69e5db31695a41568867d07a1))

## [2.9.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.7&targetVersion=GTv2.9.0-beta.8&_a=files) (2025-01-31)


### Bug Fixes

* update BO/Dir request type texts, refers [#15007](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15007) ([49f2ed3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/49f2ed3e5819b87dd719176029e154502760ad29))

## [2.9.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.6&targetVersion=GTv2.9.0-beta.7&_a=files) (2025-01-30)


### Features

* add support for new nevis BO, refers [#14951](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14951) ([3185e5a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3185e5aaad8f468c17830f5b86153f5cf14edba2))

## [2.9.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.5&targetVersion=GTv2.9.0-beta.6&_a=files) (2025-01-30)


### Bug Fixes

* update trigger to display MNE information modal, refers [#15061](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15061) ([ea5f09b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ea5f09b803aa5650cdb3b08837619880e0ce71b7))

## [2.9.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.4&targetVersion=GTv2.9.0-beta.5&_a=files) (2025-01-30)


### Bug Fixes

* update logic to always display request update, refers [#15068](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15068) ([bac8a4d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bac8a4d25e68b8e222153e196c2de9055065a6c8))

## [2.9.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.3&targetVersion=GTv2.9.0-beta.4&_a=files) (2025-01-30)


### Bug Fixes

* update condition for mandatory fields, refers [#15063](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15063) ([2f6256e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2f6256ec8dfc4cdb8370beb5bf521f51f7bc7d3a))

## [2.9.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.2&targetVersion=GTv2.9.0-beta.3&_a=files) (2025-01-30)


### Features

* unify no result messages, refers [#15017](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15017) ([2fe9dcb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2fe9dcb334330aa2f6522dd52bc94a973117cdf7))

## [2.9.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.1&targetVersion=GTv2.9.0-beta.2&_a=files) (2025-01-30)


### Features

* update address of head office question, refers [#15026](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15026) ([81fc637](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/81fc63756d47254a046c082098bea0cadfae3f83))

## [2.9.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.9.0-beta.0&targetVersion=GTv2.9.0-beta.1&_a=files) (2025-01-30)


### Features

* text changes and minor visual updates, refers [#15048](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15048) ([8e7da16](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8e7da169ed33f24c0c7186b7a4e6047ff714bf3e))

## [2.9.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.1-beta.0&targetVersion=GTv2.9.0-beta.0&_a=files) (2025-01-30)


### Features

* update landing page layout, refers [#14987](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14987) ([7b891af](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7b891af120752e5ff669d2da081db54046f29bed))

## [2.8.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0&targetVersion=GTv2.8.1-beta.0&_a=files) (2025-01-30)


### Bug Fixes

* update VGPT for VGTP, refers [#15059](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15059) ([ac06f42](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ac06f422e238a4395d03d419a7ecaebd310bb277))

## [2.8.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.15&targetVersion=GTv2.8.0&_a=files) (2025-01-28)

## [2.8.0-beta.15](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.14&targetVersion=GTv2.8.0-beta.15&_a=files) (2025-01-28)

## [2.8.0-beta.14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.13&targetVersion=GTv2.8.0-beta.14&_a=files) (2025-01-28)


### Features

* text changes, minor updates and fixes, refers [#15027](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/15027) ([78cdec3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/78cdec37ecc4b032a3bd5f70c6e51ea67e912eb7))

## [2.8.0-beta.13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.12&targetVersion=GTv2.8.0-beta.13&_a=files) (2025-01-24)

## [2.8.0-beta.12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.11&targetVersion=GTv2.8.0-beta.12&_a=files) (2025-01-24)


### Features

* add logic to display message for disabled companies on BO/DIR and rename field, refers [#14905](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14905), [#14943](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14943) ([ca7ae2e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ca7ae2e83f27af990ac5c328a666c3d7828608e5))

## [2.8.0-beta.11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.10&targetVersion=GTv2.8.0-beta.11&_a=files) (2025-01-24)


### Features

* add closing badge for companies, refers [#14938](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14938) ([3f22fd4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3f22fd404550257ee450c530f4d8a109e93145b5))

## [2.8.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.9&targetVersion=GTv2.8.0-beta.10&_a=files) (2025-01-24)


### Features

* added payment status to str summary refers [#14702](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14702) ([f994687](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f9946875894f0e3ecacda4b23394d2501642145f))

## [2.8.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.8&targetVersion=GTv2.8.0-beta.9&_a=files) (2025-01-24)


### Features

* add alert on STR for inactive companies, refers [#14940](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14940) ([35b5eae](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/35b5eae6e43237b546324eadcf6b6745d9ef7ef5))

## [2.8.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.7&targetVersion=GTv2.8.0-beta.8&_a=files) (2025-01-24)


### Bug Fixes

* always redirect to login process on new login, refers [#14936](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14936) ([d0cc08d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d0cc08d2327b8731693f388067a93b9c2a331a12))

## [2.8.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.5&targetVersion=GTv2.8.0-beta.7&_a=files) (2025-01-24)


### Features

* add company address to invoices refers [#14625](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14625) ([1871cbc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1871cbc44450445493037f67142daf852b881618))

## [2.8.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.5&targetVersion=GTv2.8.0-beta.6&_a=files) (2025-01-22)

## [2.8.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.4&targetVersion=GTv2.8.0-beta.5&_a=files) (2025-01-22)

## [2.8.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.3&targetVersion=GTv2.8.0-beta.4&_a=files) (2025-01-21)

## [2.8.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.2&targetVersion=GTv2.8.0-beta.3&_a=files) (2025-01-20)


### Bug Fixes

* always show CorporateAccountingRecordsSummary and CorporateMultinationalEnterpriseSummary refers [#14635](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14635) ([035b573](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/035b573d600cd1cbd6295fcb50437b5b2f9914fd))

## [2.8.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.1&targetVersion=GTv2.8.0-beta.2&_a=files) (2025-01-16)


### Bug Fixes

* use legal entity data in STR pdf refers [#14756](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14756) ([8daffaa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8daffaa4167ad786c13ee50ec6f4feeacca5a73a))

## [2.8.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.8.0-beta.0&targetVersion=GTv2.8.0-beta.1&_a=files) (2025-01-14)


### Bug Fixes

* fixed request update button not showing [#14609](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14609) ([8a05f8e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8a05f8ecc63156cf5ca4260f05b4711e4d62caa1))

## [2.8.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.1&targetVersion=GTv2.8.0-beta.0&_a=files) (2025-01-14)


### Features

* updated corporate director column names refers [#14586](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14586) ([ee9d3f3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ee9d3f3c26cd240c774511ec93c4ca171b3ba642))

## [2.7.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.1-beta.2&targetVersion=GTv2.7.1&_a=files) (2025-01-10)

## [2.7.1-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.1-beta.1&targetVersion=GTv2.7.1-beta.2&_a=files) (2025-01-10)

## [2.7.1-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.1-beta.0&targetVersion=GTv2.7.1-beta.1&_a=files) (2025-01-07)


### Bug Fixes

* hide confirm button for director with missing info and update confirmation dialog texts refers [#14361](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14361) [#14511](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14511) ([57265bf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/57265bffe64922d055578dc137e0ac305905622b))

## [2.7.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0&targetVersion=GTv2.7.1-beta.0&_a=files) (2025-01-07)

## [2.7.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.19&targetVersion=GTv2.7.0&_a=files) (2025-01-06)

## [2.7.0-beta.19](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.18&targetVersion=GTv2.7.0-beta.19&_a=files) (2025-01-06)

## [2.7.0-beta.18](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.17&targetVersion=GTv2.7.0-beta.18&_a=files) (2025-01-06)


### Bug Fixes

* confirm button not showing on beneficial owner table refers [#14361](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14361) ([030a963](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/030a963502faf65305adc462e7691ed17bc1381a))

## [2.7.0-beta.17](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.16&targetVersion=GTv2.7.0-beta.17&_a=files) (2025-01-02)


### Bug Fixes

* trim MFA code spaces, refers [#13750](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13750) ([85a2cfa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/85a2cfada91bb13031626905ca3ebc2805c0124e))

## [2.7.0-beta.16](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.15&targetVersion=GTv2.7.0-beta.16&_a=files) (2024-12-30)

## [2.7.0-beta.15](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.14&targetVersion=GTv2.7.0-beta.15&_a=files) (2024-12-30)


### Bug Fixes

* redirect to login when not authenticated and fix infinite loop, refers [#14323](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14323) ([8866a00](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8866a00159fe73c812208ed7e87add41e63da78d))

## [2.7.0-beta.14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.13&targetVersion=GTv2.7.0-beta.14&_a=files) (2024-12-23)


### Features

* Update landing page layout to display proper styles for mobile screen, refers [#14044](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14044) ([dc0d108](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/dc0d1087f6c74b5f4f330a65e6efc6665375d4e9))

## [2.7.0-beta.13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.12&targetVersion=GTv2.7.0-beta.13&_a=files) (2024-12-20)

## [2.7.0-beta.12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.11&targetVersion=GTv2.7.0-beta.12&_a=files) (2024-12-20)


### Bug Fixes

* switch account not logging out user, refers [#14235](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14235) ([898ab9c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/898ab9ccd7bbf852f19f480a1f872a73cf208a5d))

## [2.7.0-beta.11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.10&targetVersion=GTv2.7.0-beta.11&_a=files) (2024-12-20)


### Bug Fixes

* **Security:** add new CSP and headers to allow payments but enhance security, refers [#14266](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14266), [#14261](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14261) ([45b9e9f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/45b9e9f28cd40ea87a2fb318fc7ae8fb2a43e6cb))

## [2.7.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.9&targetVersion=GTv2.7.0-beta.10&_a=files) (2024-12-20)


### Features

* updated the landing page text with the text provided by James Martin, refers [#11824](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11824), [#14044](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14044) ([0ce9003](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0ce9003bf9943e00f24a9a4b4fb1c9e098b0bd8b))

## [2.7.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.8&targetVersion=GTv2.7.0-beta.9&_a=files) (2024-12-20)


### Bug Fixes

* add invoice date, refers [#14281](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14281) ([158df3a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/158df3aad4384cdabcbcc7eecbf4c14b04ba8e90))

## [2.7.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.7&targetVersion=GTv2.7.0-beta.8&_a=files) (2024-12-19)


### Features

* **CI:** first edition of the production deploy pipeline, refers [#14285](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14285) ([0a078c6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0a078c6aff5da5640c0a9d0549863f1f0782d6c6))

## [2.7.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.6&targetVersion=GTv2.7.0-beta.7&_a=files) (2024-12-19)

## [2.7.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.5&targetVersion=GTv2.7.0-beta.6&_a=files) (2024-12-19)


### Bug Fixes

* add BO/Dir confirmation on update request, refers [#14071](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14071) ([0b7f9f2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0b7f9f21b97b99e4ab74738dc0a3d0b20b376a42))

## [2.7.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.4&targetVersion=GTv2.7.0-beta.5&_a=files) (2024-12-19)


### Bug Fixes

* handle registration cancel error, refers [#12317](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12317) ([dba47ef](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/dba47efb4bad405889e50d033c2ea15c36f8d33b))

## [2.7.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.3&targetVersion=GTv2.7.0-beta.4&_a=files) (2024-12-19)


### Features

* add initial view for landing page, refers [#14044](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14044) ([98debb3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/98debb3176b958024bdddd1f4980a79e48c53d8c))

## [2.7.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.2&targetVersion=GTv2.7.0-beta.3&_a=files) (2024-12-19)


### Bug Fixes

* add MCC jurisdiction text, refers [#12728](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12728) ([16061a2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/16061a259c38238ee508029e3860d4af93244c78))

## [2.7.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.1&targetVersion=GTv2.7.0-beta.2&_a=files) (2024-12-19)


### Features

* **Simplified Tax Return:** add logic to only allow one return made, refers [#14067](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14067), [#12757](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12757) ([31ed6c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/31ed6c1d1b68cdf8b32d77231b3892534d20fd0a))

## [2.7.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.7.0-beta.0&targetVersion=GTv2.7.0-beta.1&_a=files) (2024-12-19)


### Bug Fixes

* **CI:** put back the original configuration, refers [#14270](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14270) ([0b3f70c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0b3f70c159be9f3996ab8984bd4fbc40683780b7))

## [2.7.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.6.1-beta.0&targetVersion=GTv2.7.0-beta.0&_a=files) (2024-12-19)


### Features

* **CI:** implement custom runner pool, refers [#14270](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14270) ([8d6662e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8d6662ecadd3331b66f92b3e9e8c195b9fb44b88))


### Bug Fixes

* **CI:** typo in pool name definition, refers [#14270](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14270) ([2fa96fe](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2fa96fe412f32c3f87c01133156f352c6fa5491c))

## [2.6.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.6.0&targetVersion=GTv2.6.1-beta.0&_a=files) (2024-12-19)

## [2.6.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.6.0-beta.4&targetVersion=GTv2.6.0&_a=files) (2024-12-16)

## [2.6.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.6.0-beta.3&targetVersion=GTv2.6.0-beta.4&_a=files) (2024-12-16)

## [2.6.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.6.0-beta.2&targetVersion=GTv2.6.0-beta.3&_a=files) (2024-12-16)


### Bug Fixes

* **Company Modules:** only use client endpoints in client portal, refers [#14204](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14204) ([ba18145](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ba18145e238a1f88d8b9fbbf163b5f0728ccc02c))

## [2.6.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.6.0-beta.1&targetVersion=GTv2.6.0-beta.2&_a=files) (2024-12-16)


### Bug Fixes

* **Master Clients:** only store basic master client data in the cookie, refers [#14050](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14050), [#14065](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14065), [#14081](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14081) ([40b611e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/40b611e54d7a555f837c8b0db487ab161918269a))

## [2.6.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.6.0-beta.0&targetVersion=GTv2.6.0-beta.1&_a=files) (2024-12-14)


### Bug Fixes

* **Basic Financial Report:** Renamed financial report based on api changes, refers [#13984](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13984) ([b803789](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b80378925b4895be8f89ba2e75be5b1364fba249))

## [2.6.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.5.1-beta.0&targetVersion=GTv2.6.0-beta.0&_a=files) (2024-12-12)


### Features

* **Security Policy:** update CSP and Cache-Control headers, refers [#14049](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14049), [#14051](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14051) ([50d0af3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/50d0af3ad4c10e4be5e1242181eb5b557c2ce985))

## [2.5.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.5.0&targetVersion=GTv2.5.1-beta.0&_a=files) (2024-12-12)


### Bug Fixes

* **BFR Submission:** solved error where the useEffect was not updating the document files, refers [#13802](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13802) ([d4af394](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d4af3947c4a830377f4badb5be01616b94559869))

## [2.5.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.5.0-beta.5&targetVersion=GTv2.5.0&_a=files) (2024-12-10)

## [2.5.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.5.0-beta.4&targetVersion=GTv2.5.0-beta.5&_a=files) (2024-12-10)

## [2.5.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.5.0-beta.3&targetVersion=GTv2.5.0-beta.4&_a=files) (2024-12-10)


### Features

* **BFR Submission:** added form-steps, added file handling, added PDF report, added utility functions, refers [#13630](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13630), [#13610](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13610), [#13614](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13614), [#13616](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13616), [#13618](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13618), [#13620](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13620), [#13622](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13622), [#13802](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13802), [#13824](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13824), [#13949](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13949), [#13931](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13931), [#13972](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13972) ([351c593](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/351c5935da28dfa737cc8637b6be46e0410533f3))

## [2.5.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.5.0-beta.2&targetVersion=GTv2.5.0-beta.3&_a=files) (2024-12-10)


### Bug Fixes

* **Simplified Tax Return:** delete unused fields in summary pdf, refers [#14069](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14069) ([666ec53](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/666ec53b819540f56dc486dc117f31ae66bf4ade))

## [2.5.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.5.0-beta.1&targetVersion=GTv2.5.0-beta.2&_a=files) (2024-12-10)


### Bug Fixes

* **MFA:** add max attempts validations for MFA, refers [#14047](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14047) ([0d52d3a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0d52d3a8b08b9bf5214e48828ea16ecbbd6b4c8d))

## [2.5.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.5.0-beta.0&targetVersion=GTv2.5.0-beta.1&_a=files) (2024-12-10)


### Bug Fixes

* **Simplified Tax Return:** rename "Company representative" to "Registered agent", refers [#14066](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14066) ([9a9e4a8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9a9e4a88f262266e0e67b1b91c299d65e22907e9))

## [2.5.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.4.1-beta.0&targetVersion=GTv2.5.0-beta.0&_a=files) (2024-12-09)


### Features

* **Payments:** add logic to cancel payments, refers [#14046](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14046) ([f39da14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f39da14cccaefa5e4a586979c810ac25dd8d3e63))

## [2.4.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.4.0&targetVersion=GTv2.4.1-beta.0&_a=files) (2024-12-09)


### Bug Fixes

* **Payments:** fix pending payments status, refers [#14034](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14034) ([48df252](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/48df252f4b56cb022e2fb03a49482ba521c8a1a1))

## [2.4.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.4.0-beta.1&targetVersion=GTv2.4.0&_a=files) (2024-12-05)

## [2.4.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.4.0-beta.0&targetVersion=GTv2.4.0-beta.1&_a=files) (2024-12-05)

## [2.4.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.2-beta.2&targetVersion=GTv2.4.0-beta.0&_a=files) (2024-12-04)


### Features

* **Basic Financial Report:** update the BFR rotes and delete the draft submission, refers [#13623](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13623), [#13626](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13626) ([a4948c5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a4948c58fa522e42817da41cbc08a2dfb63bca18))

## [2.3.2-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.2-beta.1&targetVersion=GTv2.3.2-beta.2&_a=files) (2024-12-04)


### Bug Fixes

* **BO Directors:** use renamed officer type from API, refers [#13969](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13969) ([0e53a3f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0e53a3fa93a4f712fdf4482a112b0d7aaa9bb24f))

## [2.3.2-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.2-beta.0&targetVersion=GTv2.3.2-beta.1&_a=files) (2024-12-04)


### Bug Fixes

* **Invoice:** add missing info to PDF, refers [#13950](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13950) ([2dae81b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2dae81bcc669366b2ffdf19764d9c5315580bf68))

## [2.3.2-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.1&targetVersion=GTv2.3.2-beta.0&_a=files) (2024-12-04)


### Bug Fixes

* **Log out:** fix log out 401, refers [#13960](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13960) ([1a3c713](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1a3c713ee0710eb5f173fce49df62fc5f57d28ea))

## [2.3.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.1-beta.1&targetVersion=GTv2.3.1&_a=files) (2024-12-03)

## [2.3.1-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.1-beta.0&targetVersion=GTv2.3.1-beta.1&_a=files) (2024-12-03)

## [2.3.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0&targetVersion=GTv2.3.1-beta.0&_a=files) (2024-12-03)


### Bug Fixes

* date formatter to consider date formats properly, refers [#13958](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13958), [#13961](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13961) ([7f5321c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7f5321cd42bd57b8bfbc0dc4ce561b025968b10b))

## [2.3.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.37&targetVersion=GTv2.3.0&_a=files) (2024-12-03)

## [2.3.0-beta.37](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.36&targetVersion=GTv2.3.0-beta.37&_a=files) (2024-12-03)

## [2.3.0-beta.36](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.35&targetVersion=GTv2.3.0-beta.36&_a=files) (2024-12-03)


### Bug Fixes

* **MFA:** expired message on first render, refers [#13925](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13925) ([57aab9d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/57aab9d72030e59d087913351ed7455a80c8514f))

## [2.3.0-beta.35](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.34&targetVersion=GTv2.3.0-beta.35&_a=files) (2024-12-03)


### Bug Fixes

* date formatter to consider timezone, refers [#13930](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13930) ([7ce109c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7ce109cf0c018505b50f31f938fc1ec0df5c9842))

## [2.3.0-beta.34](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.33&targetVersion=GTv2.3.0-beta.34&_a=files) (2024-12-03)


### Bug Fixes

* **Payment:** add CORS policy header for payment callback, refers [#13926](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13926) ([390b088](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/390b08859c3ddbe5fc0167fe7c21902c53f38202))

## [2.3.0-beta.33](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.32&targetVersion=GTv2.3.0-beta.33&_a=files) (2024-12-02)

## [2.3.0-beta.32](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.31&targetVersion=GTv2.3.0-beta.32&_a=files) (2024-12-02)


### Features

* remove landing page, redirect when entering portal, refers [#13809](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13809) ([0f7c870](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0f7c870347d37fccc4fa0f920b4ecc20e395d039))

## [2.3.0-beta.31](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.30&targetVersion=GTv2.3.0-beta.31&_a=files) (2024-12-02)


### Bug Fixes

* **Invoices:** display all invoices in invoice list, refers [#13810](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13810) ([3b34824](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3b348240c953305b1bf782e81952975bdd56375d))

## [2.3.0-beta.30](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.29&targetVersion=GTv2.3.0-beta.30&_a=files) (2024-12-02)


### Bug Fixes

* **Payments:** use env setting for the payment redirect, refers [#13732](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13732) ([a9392c0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a9392c083e48a160b4a49cda5c850d125a277674))

## [2.3.0-beta.29](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.28&targetVersion=GTv2.3.0-beta.29&_a=files) (2024-12-02)


### Bug Fixes

* **MFA:** call MFA email from action to avoid multiple emails, refers [#13652](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13652) ([019e51a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/019e51a1d70faeedafff1794b9a186ef5e3691eb))

## [2.3.0-beta.28](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.27&targetVersion=GTv2.3.0-beta.28&_a=files) (2024-11-29)

## [2.3.0-beta.27](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.26&targetVersion=GTv2.3.0-beta.27&_a=files) (2024-11-29)


### Bug Fixes

* **Payments:** improve error handling for payments, refers [#13731](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13731), [#13733](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13733) ([0ed4c46](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0ed4c463434f8a12f4cab0fd7b7a498c0d71b17c))

## [2.3.0-beta.26](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.25&targetVersion=GTv2.3.0-beta.26&_a=files) (2024-11-29)


### Bug Fixes

* **Submissions:** paid draft submissions not opening/displaying, refers [#13788](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13788) ([76a5801](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/76a58017b46e1f7a76b3a69b86199b25cc94269f))

## [2.3.0-beta.25](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.24&targetVersion=GTv2.3.0-beta.25&_a=files) (2024-11-29)


### Bug Fixes

* **Simplified Tax Return:** remove STR form layout on confirmation, refers [#13756](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13756) ([c92364d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c92364d0d07332d80305f4eba46bb9a93dd9d753))

## [2.3.0-beta.24](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.23&targetVersion=GTv2.3.0-beta.24&_a=files) (2024-11-29)


### Bug Fixes

* **BO Directors:** BO incorporation date formatting, refers [#13534](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13534) ([968d5bb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/968d5bb56a2dcf094b4ee3e4ec0da2f5463f6fb5))

## [2.3.0-beta.23](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.22&targetVersion=GTv2.3.0-beta.23&_a=files) (2024-11-29)


### Bug Fixes

* disable unused menu items before release, refers [#13751](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13751) ([7dae38e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7dae38e8d75fe3d70e6c0488104d183c8fc01aeb))

## [2.3.0-beta.22](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.21&targetVersion=GTv2.3.0-beta.22&_a=files) (2024-11-29)


### Bug Fixes

* **Session age:** expire logged in sessions after 2 hours, refers [#13163](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13163), [#13638](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13638) ([8a1ffee](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8a1ffeeac29cef90bddfd7f34ad041d1d070412c))

## [2.3.0-beta.21](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.20&targetVersion=GTv2.3.0-beta.21&_a=files) (2024-11-29)


### Features

* **Companies:** show “inactive” badge for inactive companies in company list, refers [#13029](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13029) ([8cadd6f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8cadd6f277ad4fa3c402392bc7eb235736427f42))

## [2.3.0-beta.20](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.19&targetVersion=GTv2.3.0-beta.20&_a=files) (2024-11-29)


### Features

* add remix dev tools, refers [#13753](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13753) ([b8a9fb7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b8a9fb788f7f377060be7d88c3a516111a8b7e8f))

## [2.3.0-beta.19](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.18&targetVersion=GTv2.3.0-beta.19&_a=files) (2024-11-28)


### Bug Fixes

* **Simplified Tax Return:** filtering for STR submissions, fixes [#13740](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13740) ([d9cce3c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d9cce3cf39b121fde1dfe74d598c4ccf225969d5))

## [2.3.0-beta.18](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.17&targetVersion=GTv2.3.0-beta.18&_a=files) (2024-11-28)


### Features

* **User management:** block users from loggin in when they're blocked in the management portal, refers [#13678](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13678) ([c425dd3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c425dd339208b41ddca3d847851985fc9b3ca731))

## [2.3.0-beta.17](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.16&targetVersion=GTv2.3.0-beta.17&_a=files) (2024-11-28)

## [2.3.0-beta.16](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.15&targetVersion=GTv2.3.0-beta.16&_a=files) (2024-11-28)


### Bug Fixes

* **BO Directors:** invoice UI and add notification when requesting BO assistance, refers [#12880](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12880), [#13660](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13660) ([eada801](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/eada8013b8b41fe3f3188ba342b62431d599dedc))

## [2.3.0-beta.15](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.14&targetVersion=GTv2.3.0-beta.15&_a=files) (2024-11-27)


### Bug Fixes

* add invoice to STR buttons and fix submission when no fee, refers [#13646](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13646) ([729048f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/729048fd211a14f386424f2785d8dca70db7470a))

## [2.3.0-beta.14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.13&targetVersion=GTv2.3.0-beta.14&_a=files) (2024-11-27)

## [2.3.0-beta.13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.12&targetVersion=GTv2.3.0-beta.13&_a=files) (2024-11-27)


### Features

* **Terms and Conditions:** add terms and conditions API logic, refers [#13653](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13653), [#13654](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13654) ([268649c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/268649c15f660733c012f7ab0007d8f493471697))

## [2.3.0-beta.12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.11&targetVersion=GTv2.3.0-beta.12&_a=files) (2024-11-27)


### Bug Fixes

* **Simplified Tax Return:** fix STR form submission and reopen, refers [#13646](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13646) ([dab7cf9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/dab7cf98e8f775245fae664c0f3b7ee9b3321472))

## [2.3.0-beta.11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.10&targetVersion=GTv2.3.0-beta.11&_a=files) (2024-11-26)

## [2.3.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.9&targetVersion=GTv2.3.0-beta.10&_a=files) (2024-11-26)


### Bug Fixes

* **Terms and Conditions:** updated style and page copy, refers [#13597](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13597) ([64c5351](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/64c53513b39c0f695684b8d01cbbed4a2e426931))

## [2.3.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.8&targetVersion=GTv2.3.0-beta.9&_a=files) (2024-11-26)

## [2.3.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.7&targetVersion=GTv2.3.0-beta.8&_a=files) (2024-11-26)


### Features

* **Payments:** implement payments and invoices module, refers [#10202](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10202), [#10205](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10205), [#10211](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10211), [#10215](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10215), [#10216](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10216), [#10224](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10224), [#10298](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10298), [#11678](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11678) ([8affbb7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8affbb7d64d6ade7af9500bd3106ec617099bdfa))

## [2.3.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.6&targetVersion=GTv2.3.0-beta.7&_a=files) (2024-11-26)

## [2.3.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.5&targetVersion=GTv2.3.0-beta.6&_a=files) (2024-11-26)


### Bug Fixes

* username can be empty, fallback to email for credentials, refers [#13639](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13639) ([df7d6ce](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/df7d6ce755003d28a682f8638cd47513c3b65af1))

## [2.3.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.4&targetVersion=GTv2.3.0-beta.5&_a=files) (2024-11-26)

## [2.3.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.3&targetVersion=GTv2.3.0-beta.4&_a=files) (2024-11-22)

## [2.3.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.2-beta.4&targetVersion=GTv2.3.0-beta.3&_a=files) (2024-11-22)

## [2.3.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.1&targetVersion=GTv2.3.0-beta.2&_a=files) (2024-11-22)


### Features

* **Terms and Conditions:** add UI for terms and condition and placeholders for logic, refers [#13545](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13545), [#13546](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13546) ([585944c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/585944c0606e9051be882366df786b67e9ea0b9f))

## [2.3.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.0&targetVersion=GTv2.3.0-beta.1&_a=files) (2024-11-20)


### Bug Fixes

* update status enum and use API type where possible, fixes [#13492](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13492) ([0c1a0c8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0c1a0c890ae0e183332837334d54df30dac7aab1))

## [2.3.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.2-beta.3&targetVersion=GTv2.3.0-beta.0&_a=files) (2024-11-20)


### Features

* add hey-api generated services, refers [#13480](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13480) ([e156cbb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e156cbb9bb12d6ef04ce34092eb7a874519c6fce))

## [2.3.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.1&targetVersion=GTv2.3.0-beta.2&_a=files) (2024-11-22)


### Features

* **Terms and Conditions:** add UI for terms and condition and placeholders for logic, refers [#13545](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13545), [#13546](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13546) ([585944c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/585944c0606e9051be882366df786b67e9ea0b9f))

## [2.3.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.3.0-beta.0&targetVersion=GTv2.3.0-beta.1&_a=files) (2024-11-20)


### Bug Fixes

* update status enum and use API type where possible, fixes [#13492](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13492) ([0c1a0c8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0c1a0c890ae0e183332837334d54df30dac7aab1))

## [2.3.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.2-beta.3&targetVersion=GTv2.3.0-beta.0&_a=files) (2024-11-20)


### Features

* add hey-api generated services, refers [#13480](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13480) ([e156cbb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e156cbb9bb12d6ef04ce34092eb7a874519c6fce))

## [2.2.2-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.2-beta.3&targetVersion=GTv2.2.2-beta.4&_a=files) (2024-11-22)

## [2.2.2-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.2-beta.2&targetVersion=GTv2.2.2-beta.3&_a=files) (2024-11-11)

## [2.2.2-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.2-beta.1&targetVersion=GTv2.2.2-beta.2&_a=files) (2024-11-11)

## [2.2.2-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.2-beta.0&targetVersion=GTv2.2.2-beta.1&_a=files) (2024-11-11)


### Bug Fixes

* **BO Directors:** fix bugs on BO/Directors tables, fixes [#12870](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12870), [#12877](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12877), [#12893](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12893), [#12894](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12894), [#12895](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12895), [#12898](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12898), [#12919](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12919), [#13123](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13123), [#13135](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13135), [#13136](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13136) ([b115392](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b1153927138491be7e2d8a29064e2c4168f7d3ac))

## [2.2.2-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.1&targetVersion=GTv2.2.2-beta.0&_a=files) (2024-11-11)


### Bug Fixes

* npm audit resolution, fixes [#13204](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/13204) ([3005f79](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3005f79daf2f535db8a2936631e0f992f0ca3d74))

## [2.2.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.1-beta.1&targetVersion=GTv2.2.1&_a=files) (2024-11-05)

## [2.2.1-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.1-beta.0&targetVersion=GTv2.2.1-beta.1&_a=files) (2024-11-05)

## [2.2.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.0&targetVersion=GTv2.2.1-beta.0&_a=files) (2024-11-01)

## [2.2.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.0-beta.1&targetVersion=GTv2.2.0&_a=files) (2024-11-01)

## [2.2.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.2.0-beta.0&targetVersion=GTv2.2.0-beta.1&_a=files) (2024-11-01)


### Bug Fixes

* added the text ([117f22f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/117f22f6db938e960c6594e47b78822539410e63))
* **BO Directors:** added the text, [#12966](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12966) ([e387aa6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e387aa6457fcee5eea97658a96cef59feadcd202))
* **CI:** deploy to ACC slot in UAT ([f2b67ae](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f2b67aef403ee63f2052d3be2655592f7a2555b3))

## [2.2.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.3-beta.1&targetVersion=GTv2.2.0-beta.0&_a=files) (2024-10-29)


### Features

* add concept of middlewares ([ef16f5f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ef16f5f3e0000db9be808eeb1993d93c1927bfea))
* add support for subtitle ([bd6be38](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bd6be38bea732c48db88c1b38f08b145d729d1dc))
* create reusable page error component ([1cbbc59](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1cbbc5903f279f1a90a8d58e393807b2986f3964))
* implement custom error logging logic ([0c7437b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0c7437bdcdb5c2d497180e195afe2bb9f9df706b))
* implement error boundary ([94818d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/94818d8e33ffe7af0de2dc1f06ca465639ec6924))
* **Middleware:** added authenticate middleware ([998855d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/998855d5ef73297090022e12f839a990e9841e40))
* redirect the user to dashboard if they’re already logged in ([ec96bca](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ec96bca47fdfa0b2dcceab09a20def28262df7ce))
* **Self hosted agents:** experiment request by Ronald to use self hosted agent ([8a41d82](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8a41d82d7f96fadf9e4fb8dab6773e0075608113))


### Bug Fixes

* add some security checks on the verify-mcc endpoint ([06e6f47](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/06e6f470aea22f614c3adf160ff1e8d2f28b2f78))
* also check against the paths with trailing / ([dcffa8b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/dcffa8b16b4b1541b788fcab27884858db464749))
* **BO Directors:** correct capitalization on column labels, fixes [#12876](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12876) ([378c776](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/378c776b4048a378f962a9b40438e67420065173))
* **BO Directors:** display Place of Birth instead of TIN number, fixes [#12870](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12870) ([b863f85](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b863f8580b11897dcd0fae074fe460480bfdbe64))
* **BO Directors:** rename modal title, fixes [#12875](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12875) ([9ba6344](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9ba63444dadd0525be605efd895df2ea84a04991))
* **CI:** revert temporary experiment in CI pipeline ([5e30c9b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5e30c9bdf6493f7428d89746e479ad4327c81eef))
* export default function ([22d5860](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/22d58605046b616fc2a69f3a9c25a7d4c6dc0529))
* redirect when no page param is defined ([40261fe](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/40261fe3c9c2ea704c801ab78fcce1080d75c175))
* **Simplified Tax Return:** default country should be read-only, fixes [#12869](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12869) ([3ffa4c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3ffa4c181997742a92a84b960126c315b59cc557))
* submission status check should only be done when submitting the form ([fd1b8ec](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/fd1b8ec80f6fb126838620d0d1f5560a24997b08))

## [2.1.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.3-beta.1&targetVersion=GTv2.1.3&_a=files) (2024-10-23)

## [2.1.3-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.3-beta.0&targetVersion=GTv2.1.3-beta.1&_a=files) (2024-10-23)


### Bug Fixes

* **CI:** pull develop and main before pushing updates ([3d31b2c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3d31b2cbf5a199018ed56eb9a23b1cbdbac25bc1))

## [2.1.3-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.2-beta.1&targetVersion=GTv2.1.3-beta.0&_a=files) (2024-10-23)


### Bug Fixes

* **CI:** resolved typo in slot name for UAT release ([c17078b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c17078b6ef41c30fb8416cc677225b0629a658fb))
* **CI:** resolved typo in slot name for UAT release ([e9b8edf](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e9b8edfe2bf78139f4f5814d1e76032e0238487c))

## [2.1.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.2-beta.1&targetVersion=GTv2.1.2&_a=files) (2024-10-23)

## [2.1.2-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.2-beta.0&targetVersion=GTv2.1.2-beta.1&_a=files) (2024-10-23)

## [2.1.2-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.1&targetVersion=GTv2.1.2-beta.0&_a=files) (2024-10-23)


### Bug Fixes

* **CI:** define slot name for UAT environment ([9fc0f3e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9fc0f3e8c1a15d3a0365926a9175309dc39623bc))

## [2.1.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.1-beta.0&targetVersion=GTv2.1.1&_a=files) (2024-10-23)

## [2.1.1-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.0&targetVersion=GTv2.1.1-beta.0&_a=files) (2024-10-23)


### Bug Fixes

* **CI:** make sure only stable release tags can be deployed to UAT ([f60077a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f60077ad1a41fdca78bcb7b6d6c1ebd94e9acd67))

## [2.1.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.0-beta.4&targetVersion=GTv2.1.0&_a=files) (2024-10-23)

## [2.1.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.0-beta.3&targetVersion=GTv2.1.0-beta.4&_a=files) (2024-10-23)

## [2.1.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.0-beta.2&targetVersion=GTv2.1.0-beta.3&_a=files) (2024-10-23)


### Bug Fixes

* **Simplified Tax Return 2019:** add tooltip icon to MNE question and better button label, fixes [#12812](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12812) ([88f139b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/88f139b213fb856b38d3e08657c3ff31d8a6b113))
* **Simplified Tax Return 2019:** add tooltip icon to MNE question and better button label, fixes [#12812](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12812) ([ae9f98f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ae9f98f08cb6c5ebb0c8ffd621f1c4aeee08d27c))

## [2.1.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.0-beta.1&targetVersion=GTv2.1.0-beta.2&_a=files) (2024-10-23)


### Features

* assistance link in the navigation ([9d01954](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9d01954d2c7cea17970ad2a8031a5ce168183150))
* assistance link in the navigation, closes [#12733](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12733) ([8637292](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8637292a652b0f4f4b26ad3461b33b223002d5bf))

## [2.1.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.1.0-beta.0&targetVersion=GTv2.1.0-beta.1&_a=files) (2024-10-23)


### Features

* **CI:** UAT deploy pipeline script, [#12818](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12818) ([73a1a9b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/73a1a9bda73386321c6545202ddc82ae39354a99))

## [2.1.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0&targetVersion=GTv2.1.0-beta.0&_a=files) (2024-10-22)


### Features

* **CI:** first test version for uat deploy pipeline ([2243eb1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2243eb1343c9dd833958bb564cb490d5ab4800d0))

## [2.0.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.10&targetVersion=GTv2.0.0&_a=files) (2024-10-22)

## [2.0.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.9&targetVersion=GTv2.0.0-beta.10&_a=files) (2024-10-22)

## [2.0.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.6&targetVersion=GTv2.0.0-beta.7&_a=files) (2024-10-21)

## [2.0.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.8&targetVersion=GTv2.0.0-beta.9&_a=files) (2024-10-22)


### Features

* **CI:** create pipeline for UAT release ([39d5c8f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/39d5c8f72a092a7ff99127f474b0ca715ede6dc5))


### Bug Fixes

* **CI:** also push changes back to develop ([8039cae](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8039cae2252dcf8d4a278d21d40d0e3adfc67b06))

## [2.0.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.6&targetVersion=GTv2.0.0-beta.8&_a=files) (2024-10-22)


### Features

* **CI:** add release pipeline ([d61346d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d61346d824015b4b7da1f4f64bcda2fc5a64b55a))
* **Middlewares:** implement middlewares concept, closes [#12752](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12752) ([646ce3d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/646ce3dfb5c0e9f71a85dbf3e83c76d1127bee6a))


### Bug Fixes

* use NodeTool@0 ([d8322c8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d8322c8e5dec17ba2b5bc9a38a670a12281a73c9))

## [2.0.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.6&targetVersion=GTv2.0.0-beta.7&_a=files) (2024-10-21)


### Features

* **Middlewares:** implement middlewares concept, closes [#12752](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12752) ([646ce3d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/646ce3dfb5c0e9f71a85dbf3e83c76d1127bee6a))

## [2.0.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.5&targetVersion=GTv2.0.0-beta.6&_a=files) (2024-10-21)


### Features

* add 2020 str form ([b95b762](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b95b7624343ca076ff0d6926cd9d90ac5d0cb5b6))
* add 2022 and 2023 forms ([516c83b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/516c83be8afe6f21c0c0ea3538b667c4fca2dff0))
* add api logic ([39fd33e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/39fd33e4e98ee98677bd587b924e708cfb90e70d))
* add directors and bo routes and logic ([6e3075a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6e3075a993f4288f667369697e9aac8b79255e1b))
* add request update stuff and dummydata based on swagger ([741c108](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/741c1082a377c57fb163c3fb2610c2b0fd5cbd8b))
* add server side schema validation for MFA ([9bac77a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9bac77a51b5515c8838104d87187ab7978c5908c))
* add version number to meta data ([4386995](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/4386995bdc63fab2a870fe1e351189e62bce0e1e))
* added detailed view to bo table, typo ([d23c683](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d23c6830068beae66399abe995fe1f03c642a3ec))
* all headers should be sortable and base the icon on the direction ([af2a273](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/af2a2738fe59620239209af9a23ea0742349d947))
* api routes based on swagger ([fb8721d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/fb8721d99408475c5dc37b3a238a166718a483c4))
* **BO Directors:** module implementation finished, closes [#11569](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11569), [#11571](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11571), [#11623](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11623), [#11624](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11624), [#11625](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11625), [#11626](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11626), [#11633](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11633), [#11634](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11634), [#11635](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11635), [#11644](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11644), [#11646](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11646), [#11647](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11647), [#11648](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11648), [#11650](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11650), [#11651](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11651), [#11652](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11652), [#11653](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11653), [#11654](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11654), [#11655](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11655) ([1416701](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1416701cbdb7599302dae6b142ace7d88ddf81b8))
* **Changelog:** create beta version changelogs ([515ed39](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/515ed3922580cb6cacc84627d8aa203ca3c7ddd3))
* **CI:** merge changelog creation with deployment ([580162b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/580162bcee4c1ad66353f10381c726476a4f6027))
* **CI:** merge changelog generation with deployment ([3dbcac0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3dbcac0ac7e6a21f39cc42562b9635dfffe2c34e))
* comparison component ([084fe48](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/084fe48f94ebd17d4dffcac2273648eeb6998e38))
* comparison update functionality ([5429108](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/542910808fe035a5e50a1062c51ea9bbb2098aeb))
* director page, dummy columns, different views, table ([f88baa7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f88baa71c5b1396782b541e8e541114ecf17c4f9))
* implement WideContainer utility component ([bdff64d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bdff64ddf6266e60ff7f0c47b4edcd5488cc7cb9))
* **Meta:** add favicon and generic meta data config ([3eda1a6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3eda1a60171d3255ba43956c6dd9399f954af5de))
* request assistance [#11635](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11635) [#11654](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11654) ([b02c2ec](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b02c2ec6f47b721c78d108817f04085aef5ca90f))
* send assistance button when there are no owners [#11635](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11635) [#11654](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11654) ([90c50dc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/90c50dc0c23b2cf96737105780ee98a55c839567))
* **Simplified Tax Return:** add 2021 STR form ([0038d38](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0038d38137cf3e6294846d74d817238347aa351a))
* **Simplified Tax Return:** add simplified tax return summary PDFs, closes [#11202](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11202) ([9b477fe](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9b477fea214ac4893a30c740c1ce7c188c59697c))
* **Simplified Tax Return:** add STR submission logic and 2019 form, related [#10289](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10289), [#11205](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11205), [#11207](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11207), [#11209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11209), [#11211](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11211), [#11212](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11212), [#11223](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11223), [#11224](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11224), [#11225](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11225), [#11237](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11237) ([7a106c7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7a106c7a908f66b6a78d9536499b62f8d6d19c7e))
* **Simplified Tax Return:** add STR submission logic and 2019 form, related [#10289](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10289), [#11205](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11205), [#11207](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11207), [#11209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11209), [#11211](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11211), [#11212](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11212), [#11223](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11223), [#11224](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11224), [#11225](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11225), [#11237](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11237) ([e467abd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e467abda286e22ca028a530854d5a944ac529460))
* **Simplified Tax Return:** add STR submission logic and 2019 form, related [#10289](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10289), [#11205](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11205), [#11207](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11207), [#11209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11209), [#11211](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11211), [#11212](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11212), [#11223](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11223), [#11224](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11224), [#11225](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11225), [#11237](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11237) ([27f6387](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/27f6387677c8e2a39a677205f522e3b913d35d09))
* **Simplified Tax Return:** add STR submission logic and 2019 form, related [#10289](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10289), [#11205](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11205), [#11207](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11207), [#11209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11209), [#11211](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11211), [#11212](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11212), [#11223](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11223), [#11224](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11224), [#11225](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11225), [#11237](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11237) ([8d862b7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8d862b7a15bb57b2033c229a81aba240dad751dd))
* **Simplified Tax Return:** add STR submission logic and 2019 form, related [#10289](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10289), [#11205](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11205), [#11207](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11207), [#11209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11209), [#11211](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11211), [#11212](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11212), [#11223](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11223), [#11224](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11224), [#11225](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11225), [#11237](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11237) ([65fd39e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/65fd39ee03f1df9ad486161c882cef481c8c2fc5))
* **Simplified Tax Return:** add submission list for completed and draft, ref [#11117](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11117) ([948dfc4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/948dfc4c7417c384aa5b15f639eecb18312cf314))
* update request ([64a0506](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/64a0506a60ea6fc602feed114eba5fe2852218fa))


### Bug Fixes

*  styling in table and gave comparison modal ability to switch between the real api and dummy data also added root component session data ([ea0b34a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ea0b34a3b600f91b34984d564bc3d8516f73c494))
* 11646 columns should be according to devops workitem ([8af71ed](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8af71edebd2b145e44cd3a88bedfc7112f999d1e))
* add background image to auth screens ([d733767](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d733767f04fde63f3a4b3bc35e6c1357bee3ae10))
* add validations and information for user ([126a0e0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/126a0e0ca081bf83813bb1b3cf794a7f024edf1f))
* add version number to meta data on index ([ab8e6a6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ab8e6a6467f0be8f2ac1657d42312a73dfe865cb))
* added correct types ([1504322](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1504322d840771f15c930887a8c0da08598acd34))
* allow recreation of tags ([d94d61a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d94d61ad075642ae2cc94130e75f26a613edd38b))
* **Auth:** implement proper background image style using TW CSS ([8ee56b6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8ee56b66ff9618d37aa8890424dbcd7f64b297fc))
* changed all "any" values to the proper types, handled errors ([460f049](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/460f049bce25e6032a472315c1187736a7a872ab))
* changed the columns to swagger values ([b02ae94](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b02ae94b61afd72aaadc91f17189228161d8b7c8))
* **Changelog:** push created changelog to develop branch ([5549848](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/554984886e3814dc76b8101c44ab3360d544790c))
* **CI:** add dependency configuration ([19bf9fe](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/19bf9feb547d53389ee110eadada202f78054518))
* **CI:** add names for deploy job ([3020e21](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3020e21020280165f09cae2cc4ee5cbe94b1fb35))
* **CI:** added stage and job dependencies ([343a93a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/343a93a6243f48eff7b6ebb03b1e88d1488ea7a8))
* **CI:** all prereleases are betas ([bb0ec7e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bb0ec7eb7b84c93e75b9935d736e0518cda9ea98))
* **CI:** do not push new changelog back to develop ([71d849c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/71d849cd5df6be294cc05883328785cfe2999525))
* **CI:** remove dependencies ([9882136](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9882136969a8f1efec1ca1f44392c351dbbdd8fe))
* data fetching should be done server side ([8d1816e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8d1816e352d920516827b8e8b0aa6d7df34bf24b))
* dates should be formatted ([b15f8e7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b15f8e78d0e404d610a2a499fd49bdf547148dc4))
* **Deployment:** correct app service name, trigger on main branch ([87989a2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/87989a2347542d8b48929601833d65329143c257))
* **Deployment:** correct app service name, trigger on main branch ([a98b0d9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a98b0d9382bd3f4698858e0bbf875e9736d12c1b))
* **Deployment:** correct environment variables ([ea1143a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ea1143afaf07c89c1eb31fa920de0d3baf9a5ad2))
* **Deployment:** correct environment variables ([1cd79bb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1cd79bbd09cfae57db9181778f3f6433492e63fc))
* **Deployment:** no auto trigger, refer correct DevOps environment ([5493b5e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5493b5eae8a3145a48d7de9bd5182fd088382293))
* **Deployment:** no auto trigger, refer correct DevOps environment ([68257f6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/68257f66d05203bb07073afe0330094cb7822fcc))
* **Deployments:** update pipeline config ([df6392a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/df6392a04b6bce1c916e947ecb5716c74115434b))
* detailedModal should always be full screen, remove x ([1bf41f4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1bf41f4b84e12b5245ba189623e4a773a6392439))
* double notifications after MFA, ref [#12454](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12454) ([d8b74df](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d8b74df8667d455d3b2609b68a589f1a858c5610))
* dummy data columns should match figma ([d0c2dbb](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d0c2dbb53c18534dffbb9be22d3c3d11ea4edddb))
* error handling ([96fa62d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/96fa62d973f4ced4bb9312d7a6cde27ea4287772))
* error handling, triangle alert should only be visible next to the row where mandatory data is missing ([ba053e5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ba053e5d37b5c1546dc06f51d27208bd15b7b45d))
* eslint errors ([e8dc126](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e8dc12633aa9cf04d25907255f6e7b7f13f19ff9))
* eslint stuff ([f8e2a33](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f8e2a33b2922f19085fb2e89a17cead9300748ec))
* eslint stuff, clear fetcher data ([7e90581](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7e9058123f246e4062bcd78e7ff28ac4de92af29))
* filter fix, button and label visibility [#11634](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11634) [#11623](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11623) [#11625](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11625) [#11625](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11625) [#11650](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11650) [#11653](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11653) [#11655](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11655) ([d8b4374](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d8b4374e9e35d91aa1737795cd6f4fc524916752))
* force prettier disable ([6f5fa8b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6f5fa8b2ca0787ff1478b49fcfba916b1214b35f))
* handle empty body in fetchApi ([2d25b61](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2d25b61d1373dddf4274a47fd60ed199f028860e))
* history should use the comparisonData ([098ba5b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/098ba5ba71d95914b360c0f09394eac22712b3ae))
* infinite loop and handle weird signs in url by encoding url param ([19b37aa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/19b37aaffe0d2e88509145062d36286316a61eb7))
* infinite loop by keeping track whether we've already fetched the data for the current modal session ([b734fb3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b734fb35fa08deb78f266c03537ab3b16697d492))
* loading flickering issue ([fd9f4b6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/fd9f4b67d5ead9bc0b71dd78cd70c248e722d7cd))
* move jurisdiction logic from masterclient to company level ([9ad92e9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9ad92e9d21daa2343cf0cc3b85d3022da6d09825))
* move loader indicator next to button to prevent style flash ([3080249](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/30802498f7d7638871b63b0acce397ba2dee4a53))
* multiple breakpoint steps ([1300652](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/13006521412258526e0b77adb4513baf2ed109b3))
* notes ([6b0365c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6b0365c282a6144ef87b5910ebaa6cbeb8d12f0a))
* only render the "request update" button when there are mandatory fields missing [#11624](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11624) ([a177529](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a1775294e9a912cffd3a61dccabaa69f3caba5db))
* only show the alert in the route when there are required fields missing in the child ([0fea2a1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0fea2a165514673c76b6d91f333475c18fe45720))
* only show the history button when there are pending updates [#11624](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11624) ([1021852](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1021852e6efb27aceddc9f7b7f6fc59bdfc818d1))
* poor naming convention ([c5e3708](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c5e37084829737a91b612148ef2d59bad03d72e0))
* refactor dates should be formatted ([8b61589](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8b6158953fcc411bef18941b3c54f27f83c3c5c9))
* refactor naming convention ([7cfbf7f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7cfbf7f5a923699b3f68819386892d16dc31587e))
* refactor, show ui when there are no owners. code split the tables for owners, table size fix ([d8a4ff4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d8a4ff4be01b9ef2d5e53b99cb5c264cf049bf2b))
* refactored component ([6db28d2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6db28d2e86c0d68ab848c62f6ad0cc9abe388576))
* refactored dummy data and file structure ([2961614](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/29616141e9f9ed55bfc8e7b91c9235c1b8a4a919))
* refactored the sorting functionality ([ef348bd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ef348bd3ce08de3099395435b385ad9c1cebe52c))
* refactored using the api response type ([4d0ae45](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/4d0ae4588420c506f0f6b32846b2ff04dfaa5544))
* refactored, deleted file ([f82f697](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f82f69799828e4d5fee9fd2ece2748f32658e7a1))
* refactoring ([3397273](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/33972730b84678a07f1f49eabf8441a3c60b3ee0))
* refactoring ([399642e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/399642e7bda382bfabb2d3d316d8e03ded3969cf))
* refactoring, corrections, clean up ([fd6861b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/fd6861b99be914c58e6a1e29656a30667fe70728))
* refector ([21d5665](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/21d5665bfac9bf17b34a22c53b68f4ab6c4656d0))
* refector ([169f6e8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/169f6e8c0a4ada2fcf21a6ee8767fbaefe3c2b60))
* refector ([012d4d9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/012d4d9a63c9b6e0d4f50c4d19eb1be49c94633b))
* revert cleanup, infinite loop ([f9801de](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f9801deb7b409a52be8e4c7bb68988b840edf832))
* reverted callback changes and added the request button to the comparison modal ([9b1d0dd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9b1d0ddb6d95ffbd3ce7c6c5aec03f2650bc14bf))
* route based on navigation ([c9006a9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c9006a9a0d8c7296cfea386c94741f7da50cd1c2))
* show confirm or confirmed [#11647](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11647) [#11652](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11652) ([39b46b1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/39b46b1c6cd773d8c554708e906afbbff5ef2aaa))
* show specific columns according to 11569, 11646, 11633 and ([8070b1b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8070b1bd2ca0b18ad545971d0ec1d602376319f7))
* show the confirmation button to trigger the compare modal when there are updatesReceived, there should also be a reject button ([156a010](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/156a01041df9e44e76964b91cfc95e23f1f8bbe2))
* some states and functions should be dynamic, accesstoken ([6c824c9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6c824c9c8980d985be107dad042adcbc1e8e4e28))
* styling ([b33d3a3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b33d3a384afc3bd068ab1d00caf189554182c623))
* styling and added rootloader data handler ([2492e34](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2492e34ece1b151ae9a898342cfc396c4f3d7ea2))
* the columns to show, added a test_data flag for representative data until db is ready ([ad1669e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ad1669e1f80302cad2d83478ecc82783fccf6e4e))
* titles and breadcrumbs ([8b098a8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8b098a8e519551f538c367ab51fd9c9c315f95cf))
* typo in component name ([b14814d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b14814d7951dd43f23c56aff4db216d7f279756e))
* using action functions in route to submit data to the server ([cc23aa3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/cc23aa3bae0478d97104d48ecd6de91be5018102))
* using encoded url param and the id instead of uniqueRelationId ([2a84507](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2a8450774615efcf3ee6c8d632a14d0cc8c0aeca))

## [2.0.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.4&targetVersion=GTv2.0.0-beta.5&_a=files) (2024-10-19)


### Bug Fixes

* actually destroy cookie by using Remix Form instead of browser native form ([22c9a78](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/22c9a7835ad620df8d51734019fa5c40024e7ad7))

## [2.0.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.3&targetVersion=GTv2.0.0-beta.4&_a=files) (2024-10-19)


### Bug Fixes

* **Company switch:** always open the company switch dialog, even if it’s a single company ([8606cec](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8606cec7df4b2d1597ebb3bb669a53a4a3393a3d))

## [2.0.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.2&targetVersion=GTv2.0.0-beta.3&_a=files) (2024-10-18)


### Features

* **Vitest:** implement automated testing framework, closes [#12732](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12732) ([4fe5de6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/4fe5de6a1b454f2f293b0a8d5e64d3a5394d1190))

## [2.0.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.1&targetVersion=GTv2.0.0-beta.2&_a=files) (2024-10-17)


### Bug Fixes

* **Meta:** this default meta call was missed in the previous update ([2ad1f7c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2ad1f7c95256e3424d09cbaf23d22d652407f688))

## [2.0.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv2.0.0-beta.0&targetVersion=GTv2.0.0-beta.1&_a=files) (2024-10-17)


### Bug Fixes

* **Simplified Tax Return:** add legal entity data based on submission data, closes [#12667](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12667) ([ac737a6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ac737a6c5f06caf2825c3cd814f37394b54362c7))

## [2.0.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.16&targetVersion=GTv2.0.0-beta.0&_a=files) (2024-10-17)


### ⚠ BREAKING CHANGES

* rename finalized to submitted, add paid badge and move revisions to drafts, closes #12669, #12717, #12718, #12719

* rename finalized to submitted, add paid badge and move revisions to drafts, closes [#12669](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12669), [#12717](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12717), [#12718](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12718), [#12719](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12719) ([bad82aa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bad82aab9e0284e3d140e7858ada58b611126b8e))

## [1.2.0-beta.16](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.15&targetVersion=GTv1.2.0-beta.16&_a=files) (2024-10-17)


### Bug Fixes

* **Sidebar:** resolve content and mobile issues, closes [#12441](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12441), [#12683](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12683), [#12684](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12684), [#12685](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12685), [#12716](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12716) ([90fc63f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/90fc63ff9e84f58c8c03b4f579914b9fc58fa013))

## [1.2.0-beta.15](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.14&targetVersion=GTv1.2.0-beta.15&_a=files) (2024-10-16)

## [1.2.0-beta.14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.13&targetVersion=GTv1.2.0-beta.14&_a=files) (2024-10-16)


### Bug Fixes

* add custom error handling for 404 after login ([f85ecd0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f85ecd024c7f5d4c71c6ae3bffe4bce2854d21d1))
* add PDF links and custom error handler for 404 error, closes [#12625](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12625), [#12626](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12626), [#12629](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12629), [#12630](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12630), [#12633](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12633) ([c9c23b7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c9c23b7ccd744686e4e8991f9ad2194f03af3e15))
* add pdfs and dollar sign ([f8b9d2e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f8b9d2e515aa587dcd09ca47a24123bff28a9d66))

## [1.2.0-beta.13](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.12&targetVersion=GTv1.2.0-beta.13&_a=files) (2024-10-15)


### Features

* add useBeforeUnload to cancel master client change on refresh ([6645d63](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6645d63cdda4ab412f601e161a12dccd8dba376c))
* improve master client/company selection logic ([09969cc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/09969ccc7c2c7f6ab9e28ac336eae9307102a691))


### Bug Fixes

* add validation on cancel change route ([80f5f95](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/80f5f954dd0d012bdff6a3406c0c61f40fa2c563))
* bug fixes in STR module and company/master client switch, closes [#12546](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12546), [#12547](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12547), [#12549](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12549), [#12550](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12550), [#12627](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12627), [#12631](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12631), [#12632](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12632) ([289b030](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/289b030c748d7a7a7da2fd71bcc2a5dd37ac79ec))
* fix lint ([6ff1e95](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6ff1e95da90f0265c24b32efe9bae3f06e6e2afe))
* fix mne report summary ([7d1f45b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7d1f45b84b9bd90d16ed340b6dbb3dec928b2817))
* fix new activities being created instead of updated ([12f38ae](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/12f38aef4d9f5d073fc3be1c01e1144f5f5fc431))
* fix return made section in PDF ([6b96a80](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6b96a800ad8f0a304b103c88ff21e15eb122da4b))
* update validation for busines activities ([d079c4c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d079c4c711802e707e0ef4f5c7aa1eeb8d1c7b35))

## [1.2.0-beta.12](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.11&targetVersion=GTv1.2.0-beta.12&_a=files) (2024-10-15)


### Bug Fixes

* temporary overwrite of vulnerable cookie dependency ([d62f87b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/d62f87b1b1a2f58cd8294324f021736e00412b6e))

## [1.2.0-beta.11](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.10&targetVersion=GTv1.2.0-beta.11&_a=files) (2024-10-09)


### Bug Fixes

* **Auth:** implement proper background image style using TW CSS ([a16155c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a16155c5af4bf0cf3045d6b3208a9f1d83e10b98))

## [1.2.0-beta.10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.9&targetVersion=GTv1.2.0-beta.10&_a=files) (2024-10-09)


### Bug Fixes

* add background image to auth screens ([67e083c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/67e083cf93bc3a99b8c627cafc45eae60d85224e))

## [1.2.0-beta.9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.8&targetVersion=GTv1.2.0-beta.9&_a=files) (2024-10-08)

## [1.2.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.2&targetVersion=GTv1.2.0-beta.3&_a=files) (2024-10-03)

## [1.2.0-beta.8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.7&targetVersion=GTv1.2.0-beta.8&_a=files) (2024-10-08)


### Features

* **Meta:** add favicon and generic meta data config ([b96d96f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b96d96fae3ce624b76b7c3d8a5d3b241341fd1eb))

## [1.2.0-beta.7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.6&targetVersion=GTv1.2.0-beta.7&_a=files) (2024-10-08)


### Features

* **Simplified Tax Return:** add simplified tax return summary PDFs, closes [#11202](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11202) ([e1ab3dd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e1ab3dd5e8525e61e4500217e1e15a6605546538))

## [1.2.0-beta.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.5&targetVersion=GTv1.2.0-beta.6&_a=files) (2024-10-08)

## [1.2.0-beta.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.4&targetVersion=GTv1.2.0-beta.5&_a=files) (2024-10-08)


### Features

* add 2020 str form ([69f04be](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/69f04bef472dc32703c8a91a3bec9f20d6d8ad3c))
* add 2022 and 2023 forms ([30fbc04](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/30fbc041bad17710db53f60db41ba58f4414321b))
* implement WideContainer utility component ([a30f9d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a30f9d8975d0a5969cff1acb886300837f8c70a0))
* **Simplified Tax Return:** add 2021 STR form ([32d4af4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/32d4af4b7d3e593f43bbbc1c776788c59f154c08))
* **Simplified Tax Return:** add form for years 2020, 2021, 2022 and 2023, refers: [#11228](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11228), [#11232](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11232), [#11233](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11233), [#11234](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11234), [#11235](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11235), [#11236](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11236), [#11240](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11240), [#11287](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11287), [#11288](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11288), [#11289](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11289), [#11291](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11291), [#11293](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11293), [#11295](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11295), [#11296](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11296), [#11297](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11297), [#11298](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11298), [#11300](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11300), [#11301](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11301) ([426bc9a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/426bc9a192a4a9546d61eccf0939dbbf122caa08))


### Bug Fixes

* add validations and information for user ([a4fb76d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a4fb76d2f4e9b5d59c1ea8538bb530bc1ed5ece8))
* multiple breakpoint steps ([e09eb90](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e09eb9081fde064d1d23940d9797c3d3e13ad470))
* titles and breadcrumbs ([18da23b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/18da23b93db0e8899a9f81c09e7592c61a9b56ed))
* typo in component name ([18594ed](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/18594ed25fbefbbd2136079723dca96bc5f64738))

## [1.2.0-beta.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.2&targetVersion=GTv1.2.0-beta.4&_a=files) (2024-10-08)


### Bug Fixes

* move jurisdiction logic from masterclient to company level ([a5dfc61](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a5dfc611fd7cac5c73f4530ab637bded834d93d9))
* move jurisdiction logic from masterclient to company level, closes [#12440](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12440) ([2d94370](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2d94370f7a7b14b933b157fbf5bc05d8a6e0839f))

## [1.2.0-beta.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.2&targetVersion=GTv1.2.0-beta.3&_a=files) (2024-10-03)

## [1.2.0-beta.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.1&targetVersion=GTv1.2.0-beta.2&_a=files) (2024-10-03)


### Bug Fixes

* **CI:** add dependency configuration ([695961c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/695961c58d3a2c5218262e09f4fec4de913718f7))
* **CI:** remove dependencies ([2595a19](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2595a1933a2f23faec53487b2840429a927a5079))

## [1.2.0-beta.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.0&targetVersion=GTv1.2.0-beta.1&_a=files) (2024-10-03)


### Bug Fixes

* add version number to meta data on index ([54e896c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/54e896ceb92613822f17adcabd0abcfe9ec66146))
* **CI:** added stage and job dependencies ([6164858](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/61648580a582b4f598afab5d40439ef417736dd1))
* **CI:** all prereleases are betas ([12cd71b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/12cd71b76264fa610d0f45d32758a403169adbdd))
* **CI:** do not push new changelog back to develop ([e6dc14c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e6dc14c2d45b0fa9597e546125f9e6dbadeb62ac))

## [1.2.0-alpha.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.0&targetVersion=GTv1.2.0-alpha.0&_a=files) (2024-10-03)


### Bug Fixes

* add version number to meta data on index ([54e896c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/54e896ceb92613822f17adcabd0abcfe9ec66146))
* **CI:** added stage and job dependencies ([6164858](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/61648580a582b4f598afab5d40439ef417736dd1))
* **CI:** do not push new changelog back to develop ([e6dc14c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e6dc14c2d45b0fa9597e546125f9e6dbadeb62ac))

## [1.2.0-alpha.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.0&targetVersion=GTv1.2.0-alpha.2&_a=files) (2024-10-03)


### Bug Fixes

* add version number to meta data on index ([54e896c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/54e896ceb92613822f17adcabd0abcfe9ec66146))
* **CI:** do not push new changelog back to develop ([e6dc14c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e6dc14c2d45b0fa9597e546125f9e6dbadeb62ac))

## [1.2.0-alpha.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.0&targetVersion=GTv1.2.0-alpha.1&_a=files) (2024-10-03)


### Bug Fixes

* **CI:** do not push new changelog back to develop ([e6dc14c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e6dc14c2d45b0fa9597e546125f9e6dbadeb62ac))

## [1.2.0-alpha.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-beta.0&targetVersion=GTv1.2.0-alpha.0&_a=files) (2024-10-03)

## [1.2.0-beta.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-alpha.6&targetVersion=GTv1.2.0-beta.0&_a=files) (2024-10-03)


### Bug Fixes

* **Deployment:** correct app service name, trigger on main branch ([c89de4e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c89de4e8dfcdabfe6efd4e918a9daff794a8462b))
* **Deployment:** correct environment variables ([dd216fa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/dd216fab4a097a4f8d2807ce59984c8daa8955e1))
* **Deployment:** no auto trigger, refer correct DevOps environment ([a361eda](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a361edae4ec8fabdaedcd71e88d4273bd4209451))

## [1.2.0-alpha.6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-alpha.5&targetVersion=GTv1.2.0-alpha.6&_a=files) (2024-10-03)


### Bug Fixes

* **CI:** add names for deploy job ([543234b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/543234bad1b022bf1ba787fa7031b6994ca4f049))

## [1.2.0-alpha.5](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-alpha.3&targetVersion=GTv1.2.0-alpha.5&_a=files) (2024-10-03)


### Features

* **CI:** merge changelog creation with deployment ([93999b4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/93999b4aed3c9e7196984a32c4b892956ba43608))
* **CI:** merge changelog generation with deployment ([b1e7b10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b1e7b10c40c99ce0e16122aa5797deb6fb2516e6))


### Bug Fixes

* **Deployment:** no auto trigger, refer correct DevOps environment ([296ddb4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/296ddb4ea5524dbda85183340ba9bce67aae6e64))

## [1.2.0-alpha.4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-alpha.3&targetVersion=GTv1.2.0-alpha.4&_a=files) (2024-10-03)


### Features

* **CI:** merge changelog generation with deployment ([b1e7b10](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b1e7b10c40c99ce0e16122aa5797deb6fb2516e6))


### Bug Fixes

* **Deployment:** no auto trigger, refer correct DevOps environment ([296ddb4](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/296ddb4ea5524dbda85183340ba9bce67aae6e64))

## [1.2.0-alpha.3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.2.0-alpha.2&targetVersion=GTv1.2.0-alpha.3&_a=files) (2024-10-03)


### Bug Fixes

* **Deployment:** correct app service name, trigger on main branch ([a6df400](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a6df4005125b7b879bff4373ad2f4a67b37f3cf4))
* **Deployment:** correct environment variables ([e418bcd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e418bcd7e6163353291707707147e745ff6d4cd9))

## [1.2.0-alpha.2](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0&targetVersion=GTv1.2.0-alpha.2&_a=files) (2024-10-03)


### Features

* add AuthLayout ([2b074c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2b074c10676acfccad35c0697aca2571da8adc01))
* add bare application layout ([1fd642b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1fd642b8909e9389fc7453fc67017a07e4e10144))
* add core layouts ([37e1bdc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/37e1bdc369bcfc7f4cb38a0feceed925cee00cc6)), closes [#12016](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12016)
* add error logs ([6b22d7b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6b22d7ba69face3961b58530f32597d8b86bdbe7)), closes [#12311](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12311)
* add full side layouts ([aaa7537](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/aaa7537fc86f42ba7e961a5f4583747e61a2937f))
* add palette colors ([5abc374](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5abc374b74816640c095f2007fdb46f0ca36b2dd))
* add pull request template for better PR descriptions ([384589c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/384589c02fd56efad5db8272439781bcdbbb3393))
* add server side schema validation for MFA ([ea2bed8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ea2bed8bd9b832e796d6e96681e90e9ce414d69a))
* add test script to package definition ([cb731e3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/cb731e32751d2aa3f5a20786cbc627e6e940f9c2))
* add version number to meta data ([83aee8a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/83aee8a00731a216346ddee0574a609221dff1c3))
* **API architecture:** add error handling and API fetch structure ([c442a97](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c442a973239e346b10a170f4014044411ead5b6e)), closes [#12020](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12020) [#12074](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12074)
* **auth:** add logout route ([1ac6201](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1ac62018ce02dcef53c0752273f3866650f2cb41))
* **Authentication:** add proxy for MCC validation endpoint ([9dc9d0f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9dc9d0f8b1a5d3953d0c61e16b589b03010d103a)), closes [#11831](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11831)
* **Authentication:** add user authentication and app authentication with the API ([8e9b2a8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8e9b2a8d24cb6a35097e796e9f9d3e83651133a2)), closes [#10702](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10702) [#11399](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11399) [#11507](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11507) [#11508](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11508) [#11513](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11513) [#11515](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11515) [#11516](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11516) [#11825](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11825)
* **Changelog:** automatically create changelog for develop branch ([72238b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/72238b983a46d7eae4e46206ae3756f517d49cbc))
* **Changelog:** create beta version changelogs ([e2b6ec0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e2b6ec07c92a29886eb726bf5ef4daeb86f1aa65))
* **CI:** automatically run playwright tests ([85f130d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/85f130d869d8566087eb01e0299ea1e7dce04bc2))
* configure desing system package ([b15de66](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b15de66545e54bcc8039843397ebe2b331120a4b))
* **Design System:** initial setup of the design system ([f8742a9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f8742a9ac8f27b039b64dcd33f117815c91584ae)), closes [#10292](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10292)
* dont redirect to login after callback ([06dd613](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/06dd6133524b85969672ea1ed9126869fcf9d53b))
* **Entity Switching:** add modals to switch masterclients and companies, refers [#12099](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12099), [#12100](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12100) ([8cdbaef](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8cdbaef2fc8265c4dd1a3b6d0031e8d251661f96))
* **entra:** add API authentication with entra ([94eb148](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/94eb14836af3df40371793afc13639b078bf52ca))
* **Entra:** add initial configuration for entra ([7a62743](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7a627430ebe69c10825ad85a2130dfa74f9349f2))
* **entra:** add logic to redirect when required ([160cdce](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/160cdceb37a0c91080a017edd26fd43bc8d9ca76))
* **ESLint:** implemented latest ESLint configuration and ESLint v9 ([e4643d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e4643d86981349e92fd5e5950bf98c9cb05531b0)), closes [#12019](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12019)
* include lucide react icons package ([a0e4fc1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a0e4fc1bcc41edfe52f5fddc40add0d227e16250))
* integrate master client and company selection into the flow, closes [#11520](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11520) ([f62ff17](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f62ff17a6c340dd01c34bdbc58fb7d52ffd8e4a8))
* **landing:** add placeholder landing page ([5e9fde8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5e9fde83b3fdccade53e936bd1444652eb9f5255))
* **Landing:** add placeholder landing page ([7677e1f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7677e1f0b452ede8aa865b991002c6866d6394f1)), closes [#11828](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11828)
* **mfa:** add auth layout and structure ([db9aa14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/db9aa14b6d54058975ea7140332e3fb726f0bfb4))
* **mfa:** add authenticator (QR) set up ([a004daa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a004daae1b036761c8fcc62d7dc6592fc16091e1))
* **mfa:** add authenticator code ([e0b9f3b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e0b9f3b63f8a275e704961337cc4a42c7b903735))
* **mfa:** add email flow ([bc57d74](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bc57d74e7eef714cc6e0ef1493b6825fcdd5507e))
* **mfa:** add logic for authenticator code ([0155922](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0155922b035d99b890b147d7e6a219e36ea52f02))
* **mfa:** add logic to mfa set up ([ee2bf96](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ee2bf967a5b9349dd32ee5e825b9e8d0fecc1eec))
* **mfa:** add missing logic for authenticator ([47de780](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/47de78036e9c060e695fa02d194e4cc7e041ce62))
* **mfa:** add set up and email views ([f9ff8cd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f9ff8cda1773e07f1798211abf2b780d2741a5eb))
* **mfa:** add submit of authenticator setup ([457b404](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/457b404168482854fa3b2e6fc7fe04f3684a07b2))
* **MFA:** create MFA views ([3804c91](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3804c91c0b96300067ce061958a7b1aaeffd4262)), closes [#11830](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11830) [#12036](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12036) [#12037](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12037) [#12038](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12038)
* **mfa:** update logic for email validation ([6051887](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/60518879e9ea7bb2c63379b7da19172e845d8851))
* **Multi factor auth:** add MFA logic ([bd1c7a1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bd1c7a113033ad51f6cb9f90f8bfd792befc68a3)), closes [#12082](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12082) [#12083](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12083) [#12084](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12084) [#12085](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12085)
* **Pipeline:** dev slot deploy pipeline ([034707a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/034707afd7bbb02a46abd0db5b9f17586bc00f04))
* **proxy:** add proxy for MCC validation endpoint ([3cf8935](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3cf8935138c590a85ebb44db33bc7d894e41f777))
* **react-query:** add new architecture for API client and react-query ([9544f08](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9544f083163242044be80aad3d7d5b413569ea4a))
* select Master Client and Company, closes [#11885](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11885), [#11886](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11886) ([f6249f6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f6249f6b59a7369e3628068efa8828e62612b043))
* **session:** update session management and token expiration ([c652810](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c6528109fafa86858d8e5e9c5ae20bad58f4f3b7))
* set Inter font ([e0ee788](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e0ee7880e5a5b0d3e80ef9f1c22af3f23bfb86ed))
* **Simplified Tax Return:** add STR submission logic and 2019 form, related [#10289](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10289), [#11205](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11205), [#11207](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11207), [#11209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11209), [#11211](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11211), [#11212](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11212), [#11223](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11223), [#11224](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11224), [#11225](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11225), [#11237](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11237) ([3f2434d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3f2434de540ba78a0b254878df4372405b88a96a))
* **Simplified Tax Return:** add submission list for completed and draft, ref [#11117](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11117) ([8d3da7b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8d3da7b3736fc516b96a46b38aebfc3214f4db62))
* update session logic and add error handling ([0eae869](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0eae869df66e2f18579822a200edcbf9ff343890))


### Bug Fixes

* add logs for login error, remove infinite loop when login fails ([2365547](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/23655478d7754c6dfba19c0cbfb0f762978b6e1e)), closes [#12311](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12311)
* allow recreation of tags ([acb5422](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/acb5422bba2f6ae9b953f3b5e31c0b68c74097f8))
* **Changelog:** allow creation on develop branch ([dcc2675](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/dcc2675f0bf76c21a5076e847eaad00fdaab636c))
* **Changelog:** develop branch creates alpha releases ([327b4a3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/327b4a39e43fd03a5cfda960494ae0c51bebe6b1))
* **changelog:** fix changelog url after repo update ([8aa8d0c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8aa8d0cdd4bdbc4b43bd1d21ff953aed29c891f9))
* **Changelog:** fix repo url ([94d1493](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/94d14939aa421676dc38a53459ddbb0ab81b1adc)), closes [#12101](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12101)
* **Changelog:** pull changes back to develop branch ([c5d364e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c5d364e9df2a401062ca3a0341cc23c38233e76a))
* **Changelog:** push created changelog to develop branch ([b888291](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b888291e6033c8196222313604d5fb64ed8ccd65))
* **Deployments:** update pipeline config ([a482e7f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a482e7ff9be3e8051aa166c3ebce3e372c5717d6))
* double notifications after MFA, ref [#12454](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12454) ([207e6c7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/207e6c71ff3569dcb76c9b0ad24918ee6b54ddae))
* **entra:** remove x-userid value for sign in ([9ad7b1d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9ad7b1d43876555a1c96a9329a73e45e46daf54f))
* **MFA:** add server side schema validation for MFA, ref [#12453](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12453) ([965e4bd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/965e4bd020eaad038dcc8dd33c47cdbb9883e42b))
* **mfa:** clean mfa data on login ([75edf6a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/75edf6a54bdf88df4e10b2626163acbdd46a64cd))
* modify logic to fix hydration error by expiry message, closes [#12176](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12176) ([0d24017](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0d240179d269f8c1679816ce47daec24a9be0aee))
* **Pipeline:** correct service connection ([5dbb269](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5dbb2695889d1df8e3356c8c53e4742394e17295))
* **Pipelines:** made the azure subscription a configurable variable ([762f12d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/762f12d746b76c9d82494e47ab4cfcd714b561de))
* proper response body content ([5133aec](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5133aeccf5a92236e52bf00fc381d5fe4d668ab7))
* resolving npm audit issues ([0f52593](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0f52593e86b16331be07fb093bddee098d1265e4))
* revert error logging, closes [#12311](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12311) ([391d71f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/391d71f77da852f9ae1d869cf155335cf12ab5ff))
* **Tests:** disable automated tests until some tests are implemented ([8bb8136](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8bb8136f548b8f16de872e922ce377f2b3383458))
* update tooltip text, [#12348](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12348) ([8bd180d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8bd180d3cb5651b70d08adb6f83fbe1e6b3c6a3d))
* use Entra authentication token instead of client credentials ([6497e38](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6497e38864d955e98a1cb8735c702752703bd945))

## [1.2.0-alpha.1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0&targetVersion=GTv1.2.0-alpha.1&_a=files) (2024-10-03)


### Features

* add AuthLayout ([2b074c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2b074c10676acfccad35c0697aca2571da8adc01))
* add bare application layout ([1fd642b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1fd642b8909e9389fc7453fc67017a07e4e10144))
* add core layouts ([37e1bdc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/37e1bdc369bcfc7f4cb38a0feceed925cee00cc6)), closes [#12016](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12016)
* add error logs ([6b22d7b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6b22d7ba69face3961b58530f32597d8b86bdbe7)), closes [#12311](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12311)
* add full side layouts ([aaa7537](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/aaa7537fc86f42ba7e961a5f4583747e61a2937f))
* add palette colors ([5abc374](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5abc374b74816640c095f2007fdb46f0ca36b2dd))
* add pull request template for better PR descriptions ([384589c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/384589c02fd56efad5db8272439781bcdbbb3393))
* add server side schema validation for MFA ([ea2bed8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ea2bed8bd9b832e796d6e96681e90e9ce414d69a))
* add test script to package definition ([cb731e3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/cb731e32751d2aa3f5a20786cbc627e6e940f9c2))
* add version number to meta data ([83aee8a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/83aee8a00731a216346ddee0574a609221dff1c3))
* **API architecture:** add error handling and API fetch structure ([c442a97](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c442a973239e346b10a170f4014044411ead5b6e)), closes [#12020](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12020) [#12074](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12074)
* **auth:** add logout route ([1ac6201](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1ac62018ce02dcef53c0752273f3866650f2cb41))
* **Authentication:** add proxy for MCC validation endpoint ([9dc9d0f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9dc9d0f8b1a5d3953d0c61e16b589b03010d103a)), closes [#11831](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11831)
* **Authentication:** add user authentication and app authentication with the API ([8e9b2a8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8e9b2a8d24cb6a35097e796e9f9d3e83651133a2)), closes [#10702](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10702) [#11399](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11399) [#11507](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11507) [#11508](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11508) [#11513](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11513) [#11515](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11515) [#11516](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11516) [#11825](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11825)
* **Changelog:** automatically create changelog for develop branch ([72238b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/72238b983a46d7eae4e46206ae3756f517d49cbc))
* **CI:** automatically run playwright tests ([85f130d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/85f130d869d8566087eb01e0299ea1e7dce04bc2))
* configure desing system package ([b15de66](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b15de66545e54bcc8039843397ebe2b331120a4b))
* **Design System:** initial setup of the design system ([f8742a9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f8742a9ac8f27b039b64dcd33f117815c91584ae)), closes [#10292](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10292)
* dont redirect to login after callback ([06dd613](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/06dd6133524b85969672ea1ed9126869fcf9d53b))
* **Entity Switching:** add modals to switch masterclients and companies, refers [#12099](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12099), [#12100](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12100) ([8cdbaef](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8cdbaef2fc8265c4dd1a3b6d0031e8d251661f96))
* **entra:** add API authentication with entra ([94eb148](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/94eb14836af3df40371793afc13639b078bf52ca))
* **Entra:** add initial configuration for entra ([7a62743](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7a627430ebe69c10825ad85a2130dfa74f9349f2))
* **entra:** add logic to redirect when required ([160cdce](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/160cdceb37a0c91080a017edd26fd43bc8d9ca76))
* **ESLint:** implemented latest ESLint configuration and ESLint v9 ([e4643d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e4643d86981349e92fd5e5950bf98c9cb05531b0)), closes [#12019](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12019)
* include lucide react icons package ([a0e4fc1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a0e4fc1bcc41edfe52f5fddc40add0d227e16250))
* integrate master client and company selection into the flow, closes [#11520](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11520) ([f62ff17](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f62ff17a6c340dd01c34bdbc58fb7d52ffd8e4a8))
* **landing:** add placeholder landing page ([5e9fde8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5e9fde83b3fdccade53e936bd1444652eb9f5255))
* **Landing:** add placeholder landing page ([7677e1f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7677e1f0b452ede8aa865b991002c6866d6394f1)), closes [#11828](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11828)
* **mfa:** add auth layout and structure ([db9aa14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/db9aa14b6d54058975ea7140332e3fb726f0bfb4))
* **mfa:** add authenticator (QR) set up ([a004daa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a004daae1b036761c8fcc62d7dc6592fc16091e1))
* **mfa:** add authenticator code ([e0b9f3b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e0b9f3b63f8a275e704961337cc4a42c7b903735))
* **mfa:** add email flow ([bc57d74](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bc57d74e7eef714cc6e0ef1493b6825fcdd5507e))
* **mfa:** add logic for authenticator code ([0155922](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0155922b035d99b890b147d7e6a219e36ea52f02))
* **mfa:** add logic to mfa set up ([ee2bf96](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ee2bf967a5b9349dd32ee5e825b9e8d0fecc1eec))
* **mfa:** add missing logic for authenticator ([47de780](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/47de78036e9c060e695fa02d194e4cc7e041ce62))
* **mfa:** add set up and email views ([f9ff8cd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f9ff8cda1773e07f1798211abf2b780d2741a5eb))
* **mfa:** add submit of authenticator setup ([457b404](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/457b404168482854fa3b2e6fc7fe04f3684a07b2))
* **MFA:** create MFA views ([3804c91](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3804c91c0b96300067ce061958a7b1aaeffd4262)), closes [#11830](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11830) [#12036](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12036) [#12037](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12037) [#12038](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12038)
* **mfa:** update logic for email validation ([6051887](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/60518879e9ea7bb2c63379b7da19172e845d8851))
* **Multi factor auth:** add MFA logic ([bd1c7a1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bd1c7a113033ad51f6cb9f90f8bfd792befc68a3)), closes [#12082](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12082) [#12083](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12083) [#12084](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12084) [#12085](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12085)
* **Pipeline:** dev slot deploy pipeline ([034707a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/034707afd7bbb02a46abd0db5b9f17586bc00f04))
* **proxy:** add proxy for MCC validation endpoint ([3cf8935](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3cf8935138c590a85ebb44db33bc7d894e41f777))
* **react-query:** add new architecture for API client and react-query ([9544f08](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9544f083163242044be80aad3d7d5b413569ea4a))
* select Master Client and Company, closes [#11885](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11885), [#11886](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11886) ([f6249f6](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f6249f6b59a7369e3628068efa8828e62612b043))
* **session:** update session management and token expiration ([c652810](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c6528109fafa86858d8e5e9c5ae20bad58f4f3b7))
* set Inter font ([e0ee788](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e0ee7880e5a5b0d3e80ef9f1c22af3f23bfb86ed))
* **Simplified Tax Return:** add STR submission logic and 2019 form, related [#10289](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10289), [#11205](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11205), [#11207](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11207), [#11209](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11209), [#11211](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11211), [#11212](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11212), [#11223](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11223), [#11224](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11224), [#11225](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11225), [#11237](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11237) ([3f2434d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3f2434de540ba78a0b254878df4372405b88a96a))
* **Simplified Tax Return:** add submission list for completed and draft, ref [#11117](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11117) ([8d3da7b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8d3da7b3736fc516b96a46b38aebfc3214f4db62))
* update session logic and add error handling ([0eae869](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0eae869df66e2f18579822a200edcbf9ff343890))


### Bug Fixes

* add logs for login error, remove infinite loop when login fails ([2365547](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/23655478d7754c6dfba19c0cbfb0f762978b6e1e)), closes [#12311](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12311)
* allow recreation of tags ([acb5422](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/acb5422bba2f6ae9b953f3b5e31c0b68c74097f8))
* **Changelog:** allow creation on develop branch ([dcc2675](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/dcc2675f0bf76c21a5076e847eaad00fdaab636c))
* **Changelog:** develop branch creates alpha releases ([327b4a3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/327b4a39e43fd03a5cfda960494ae0c51bebe6b1))
* **changelog:** fix changelog url after repo update ([8aa8d0c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8aa8d0cdd4bdbc4b43bd1d21ff953aed29c891f9))
* **Changelog:** fix repo url ([94d1493](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/94d14939aa421676dc38a53459ddbb0ab81b1adc)), closes [#12101](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12101)
* **Changelog:** pull changes back to develop branch ([c5d364e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c5d364e9df2a401062ca3a0341cc23c38233e76a))
* **Changelog:** push created changelog to develop branch ([b888291](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b888291e6033c8196222313604d5fb64ed8ccd65))
* double notifications after MFA, ref [#12454](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12454) ([207e6c7](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/207e6c71ff3569dcb76c9b0ad24918ee6b54ddae))
* **entra:** remove x-userid value for sign in ([9ad7b1d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9ad7b1d43876555a1c96a9329a73e45e46daf54f))
* **MFA:** add server side schema validation for MFA, ref [#12453](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12453) ([965e4bd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/965e4bd020eaad038dcc8dd33c47cdbb9883e42b))
* **mfa:** clean mfa data on login ([75edf6a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/75edf6a54bdf88df4e10b2626163acbdd46a64cd))
* modify logic to fix hydration error by expiry message, closes [#12176](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12176) ([0d24017](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0d240179d269f8c1679816ce47daec24a9be0aee))
* **Pipeline:** correct service connection ([5dbb269](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5dbb2695889d1df8e3356c8c53e4742394e17295))
* **Pipelines:** made the azure subscription a configurable variable ([762f12d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/762f12d746b76c9d82494e47ab4cfcd714b561de))
* proper response body content ([5133aec](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5133aeccf5a92236e52bf00fc381d5fe4d668ab7))
* resolving npm audit issues ([0f52593](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0f52593e86b16331be07fb093bddee098d1265e4))
* revert error logging, closes [#12311](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12311) ([391d71f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/391d71f77da852f9ae1d869cf155335cf12ab5ff))
* **Tests:** disable automated tests until some tests are implemented ([8bb8136](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8bb8136f548b8f16de872e922ce377f2b3383458))
* update tooltip text, [#12348](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12348) ([8bd180d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8bd180d3cb5651b70d08adb6f83fbe1e6b3c6a3d))
* use Entra authentication token instead of client credentials ([6497e38](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6497e38864d955e98a1cb8735c702752703bd945))

## [1.2.0-alpha.0](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branches?baseVersion=GTv1.1.0&targetVersion=GTv1.2.0-alpha.0&_a=files) (2024-09-12)


### Features

* add AuthLayout ([2b074c1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/2b074c10676acfccad35c0697aca2571da8adc01))
* add bare application layout ([1fd642b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1fd642b8909e9389fc7453fc67017a07e4e10144))
* add core layouts ([37e1bdc](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/37e1bdc369bcfc7f4cb38a0feceed925cee00cc6)), closes [#12016](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12016)
* add full side layouts ([aaa7537](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/aaa7537fc86f42ba7e961a5f4583747e61a2937f))
* add palette colors ([5abc374](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5abc374b74816640c095f2007fdb46f0ca36b2dd))
* add test script to package definition ([cb731e3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/cb731e32751d2aa3f5a20786cbc627e6e940f9c2))
* **API architecture:** add error handling and API fetch structure ([c442a97](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c442a973239e346b10a170f4014044411ead5b6e)), closes [#12020](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12020) [#12074](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12074)
* **auth:** add logout route ([1ac6201](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/1ac62018ce02dcef53c0752273f3866650f2cb41))
* **Authentication:** add proxy for MCC validation endpoint ([9dc9d0f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9dc9d0f8b1a5d3953d0c61e16b589b03010d103a)), closes [#11831](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11831)
* **Authentication:** add user authentication and app authentication with the API ([8e9b2a8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8e9b2a8d24cb6a35097e796e9f9d3e83651133a2)), closes [#10702](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10702) [#11399](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11399) [#11507](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11507) [#11508](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11508) [#11513](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11513) [#11515](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11515) [#11516](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11516) [#11825](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11825)
* **Changelog:** automatically create changelog for develop branch ([72238b9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/72238b983a46d7eae4e46206ae3756f517d49cbc))
* **CI:** automatically run playwright tests ([85f130d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/85f130d869d8566087eb01e0299ea1e7dce04bc2))
* configure desing system package ([b15de66](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/b15de66545e54bcc8039843397ebe2b331120a4b))
* **Design System:** initial setup of the design system ([f8742a9](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f8742a9ac8f27b039b64dcd33f117815c91584ae)), closes [#10292](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/10292)
* dont redirect to login after callback ([06dd613](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/06dd6133524b85969672ea1ed9126869fcf9d53b))
* **entra:** add API authentication with entra ([94eb148](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/94eb14836af3df40371793afc13639b078bf52ca))
* **Entra:** add initial configuration for entra ([7a62743](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7a627430ebe69c10825ad85a2130dfa74f9349f2))
* **entra:** add logic to redirect when required ([160cdce](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/160cdceb37a0c91080a017edd26fd43bc8d9ca76))
* **ESLint:** implemented latest ESLint configuration and ESLint v9 ([e4643d8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e4643d86981349e92fd5e5950bf98c9cb05531b0)), closes [#12019](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12019)
* include lucide react icons package ([a0e4fc1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a0e4fc1bcc41edfe52f5fddc40add0d227e16250))
* **landing:** add placeholder landing page ([5e9fde8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5e9fde83b3fdccade53e936bd1444652eb9f5255))
* **Landing:** add placeholder landing page ([7677e1f](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/7677e1f0b452ede8aa865b991002c6866d6394f1)), closes [#11828](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11828)
* **mfa:** add auth layout and structure ([db9aa14](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/db9aa14b6d54058975ea7140332e3fb726f0bfb4))
* **mfa:** add authenticator (QR) set up ([a004daa](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/a004daae1b036761c8fcc62d7dc6592fc16091e1))
* **mfa:** add authenticator code ([e0b9f3b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e0b9f3b63f8a275e704961337cc4a42c7b903735))
* **mfa:** add email flow ([bc57d74](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bc57d74e7eef714cc6e0ef1493b6825fcdd5507e))
* **mfa:** add logic for authenticator code ([0155922](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0155922b035d99b890b147d7e6a219e36ea52f02))
* **mfa:** add logic to mfa set up ([ee2bf96](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/ee2bf967a5b9349dd32ee5e825b9e8d0fecc1eec))
* **mfa:** add missing logic for authenticator ([47de780](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/47de78036e9c060e695fa02d194e4cc7e041ce62))
* **mfa:** add set up and email views ([f9ff8cd](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/f9ff8cda1773e07f1798211abf2b780d2741a5eb))
* **mfa:** add submit of authenticator setup ([457b404](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/457b404168482854fa3b2e6fc7fe04f3684a07b2))
* **MFA:** create MFA views ([3804c91](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3804c91c0b96300067ce061958a7b1aaeffd4262)), closes [#11830](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/11830) [#12036](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12036) [#12037](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12037) [#12038](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12038)
* **mfa:** update logic for email validation ([6051887](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/60518879e9ea7bb2c63379b7da19172e845d8851))
* **Multi factor auth:** add MFA logic ([bd1c7a1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/bd1c7a113033ad51f6cb9f90f8bfd792befc68a3)), closes [#12082](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12082) [#12083](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12083) [#12084](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12084) [#12085](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12085)
* **Pipeline:** dev slot deploy pipeline ([034707a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/034707afd7bbb02a46abd0db5b9f17586bc00f04))
* **proxy:** add proxy for MCC validation endpoint ([3cf8935](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/3cf8935138c590a85ebb44db33bc7d894e41f777))
* **react-query:** add new architecture for API client and react-query ([9544f08](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9544f083163242044be80aad3d7d5b413569ea4a))
* **session:** update session management and token expiration ([c652810](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/c6528109fafa86858d8e5e9c5ae20bad58f4f3b7))
* set Inter font ([e0ee788](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/e0ee7880e5a5b0d3e80ef9f1c22af3f23bfb86ed))
* update session logic and add error handling ([0eae869](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0eae869df66e2f18579822a200edcbf9ff343890))


### Bug Fixes

* **Changelog:** allow creation on develop branch ([dcc2675](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/dcc2675f0bf76c21a5076e847eaad00fdaab636c))
* **Changelog:** develop branch creates alpha releases ([327b4a3](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/327b4a39e43fd03a5cfda960494ae0c51bebe6b1))
* **changelog:** fix changelog url after repo update ([8aa8d0c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/8aa8d0cdd4bdbc4b43bd1d21ff953aed29c891f9))
* **Changelog:** fix repo url ([94d1493](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/94d14939aa421676dc38a53459ddbb0ab81b1adc)), closes [#12101](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/12101)
* **entra:** remove x-userid value for sign in ([9ad7b1d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/9ad7b1d43876555a1c96a9329a73e45e46daf54f))
* **mfa:** clean mfa data on login ([75edf6a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/75edf6a54bdf88df4e10b2626163acbdd46a64cd))
* **Pipeline:** correct service connection ([5dbb269](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5dbb2695889d1df8e3356c8c53e4742394e17295))
* **Pipelines:** made the azure subscription a configurable variable ([762f12d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/762f12d746b76c9d82494e47ab4cfcd714b561de))
* proper response body content ([5133aec](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/5133aeccf5a92236e52bf00fc381d5fe4d668ab7))
* resolving npm audit issues ([0f52593](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/0f52593e86b16331be07fb093bddee098d1265e4))
* use Entra authentication token instead of client credentials ([6497e38](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Public%20Portal/commit/6497e38864d955e98a1cb8735c702752703bd945))

## 1.1.0 (2024-08-06)


### Features

* add env file ([455917b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/455917bc8bc21354d1aae8e1760428a075bee1b1))
* **Changelog:** add changelog generation package ([c89c316](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/c89c3161f56d7d348f50dfad1d4cca3488b426c1))
* **Changelog:** add configuration ([dc43ef1](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/dc43ef187aca2f374d486de49f340a8c45874067))
* **Changelog:** add run script ([e81e0ff](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/e81e0ffe8b16f57b28bd7a45d824bb70d5ca8e28))
* **CI:** setup changelog pipeline ([dd42c6c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/dd42c6cded381d560d85dd50960d65a01af685b1))
* **CI:** setup eslint pipeline ([118588a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/118588a74e5640375f5553396ee5e51b15b6750a))
* **ESlint:** add @netpro/eslint-config package ([1ffb93d](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/1ffb93d8c64853cd4ed498bca547c8cde90f920d))
* **ESlint:** update configuration ([719b21e](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/719b21e718a0360312c3d850bb378e7cd52a31ca))
* initial commit ([3977959](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/39779593013f1524665d611fbb88ef38135d2ea1))
* initial release ([a509c4b](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/a509c4bcc57d208754fc6e63dd4d529db0cebe7b))
* **Node:** add nvm version file ([6671ad8](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/6671ad8652c0e37c4266d81b455c7d80af1d8ba4))
* **Node:** updated version dependency ([e9a1121](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/e9a1121eab56371d62b895f7a3f61e32a3551d7d))
* **NPM:** add NetPro private repo configuration ([b84330c](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/b84330cb5d1502bf2beeac4ac9b4af8420a3df93))


### Bug Fixes

* add application name to config ([47be010](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/47be0102c25e143042a5bbd81bc0444f77fe21b9))
* updated engine version dependency ([7682f1a](https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Application/commit/7682f1a9ac0fe00b246f3850eba82363346ce983))
