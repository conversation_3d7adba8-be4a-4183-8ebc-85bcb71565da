import { useLoaderData } from "@remix-run/react";
import { SummaryTable } from "./table/SummaryTable";
import { SummaryTableData } from "./table/SummaryTableData";
import { SummaryTableRow } from "./table/SummaryTableRow";
import type {
  RelevantActivityDeclarationSchemaType,
} from "~/lib/economic-substance/types/bahamas/relevant-activity-declaration-schema";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { formatToISODateString } from "~/lib/utilities/format";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.$id.summary";

export function RelevantActivities() {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { relevantActivities } = submissionData[Pages.RELEVANT_ACTIVITY_DECLARATION] as RelevantActivityDeclarationSchemaType
  const selectedRelevantActivities = relevantActivities?.filter(activity => activity.selected === "true")

  return (
    <div>
      <h2 className="text-blue-700 font-bold mb-4">Relevant Activities</h2>
      <SummaryTable>
        <thead className="font-bold">
          <SummaryTableRow>
            <SummaryTableData>From</SummaryTableData>
            <SummaryTableData>To</SummaryTableData>
            <SummaryTableData>Relevant Activity or Activities conducted</SummaryTableData>
          </SummaryTableRow>
        </thead>
        <tbody>
          {selectedRelevantActivities?.map(activity => (
            <SummaryTableRow key={activity.id}>
              <SummaryTableData>{formatToISODateString(new Date(activity.startDate ?? "N/A"), true)}</SummaryTableData>
              <SummaryTableData>{formatToISODateString(new Date(activity.endDate ?? "N/A"), true)}</SummaryTableData>
              <SummaryTableData>{activity.label}</SummaryTableData>
            </SummaryTableRow>
          ))}
        </tbody>
      </SummaryTable>
    </div>
  )
}
