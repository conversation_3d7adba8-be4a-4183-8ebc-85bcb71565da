import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/react";

/*
 * This loader is used to handle the callback from the payment provider after the payment is completed.
 *
 * To avoid redirects from thrid-party payment providers, the payment provider will redirect to this URL
 * and it will return the required data to the client to handle the payment completion and preserve the user session.
 */
export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<{
  paymentId: string
  tokenId: string
  transactionId: string
}>> {
  // No session validations are being done here since cookie is not available after redirect from the payment provider

  const { id: paymentId } = params;
  if (!paymentId) {
    throw new Response("Missing paymentId", { status: 400 });
  }

  if (!process.env.APPLICATION_BASE_URL) {
    throw new Response("Missing APPLICATION_BASE_URL", { status: 500 });
  }

  const referer = request.headers.get("referer");
  if (!referer || !referer.startsWith(process.env.APPLICATION_BASE_URL)) {
    throw new Response("Invalid referer", { status: 403 });
  }

  const origin = request.headers.get("origin");
  if (origin !== "null") {
    throw new Response("Invalid origin", { status: 403 });
  }

  const url = new URL(request.url);
  const tokenId = url.searchParams.get("token-id");
  const transactionId = url.searchParams.get("transactionId");

  if (!tokenId || !transactionId) {
    throw new Response("Missing tokenId or transactionId", { status: 400 });
  }

  return json({ paymentId, tokenId, transactionId }, {
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
  });
}
