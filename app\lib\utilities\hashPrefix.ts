const HASH_PREFIX = "__HASH__";

export function encodeHashPrefix(value: string): string {
  return value.startsWith("#") ? `${HASH_PREFIX}${value.slice(1)}` : value;
}

export function decodeHashPrefix(value: string): string {
  return value.startsWith(HASH_PREFIX) ? `#${value.slice(HASH_PREFIX.length)}` : value;
}

export function encodeFields<T extends Record<string, any>>(obj: T, keys: (keyof T)[]): T {
  const result: T = { ...obj };
  for (const key of keys) {
    const value = obj[key];
    if (typeof value === "string") {
      (result[key] as string) = encodeHashPrefix(value);
    }
  }

  return result;
}

export function decodeFields<T extends Record<string, any>>(obj: T, keys: (keyof T)[]): T {
  const result: T = { ...obj };
  for (const key of keys) {
    const value = obj[key];
    if (typeof value === "string") {
      (result[key] as string) = decodeHashPrefix(value);
    }
  }

  return result;
}
