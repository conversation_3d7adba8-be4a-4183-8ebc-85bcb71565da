import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import {
  Button,
  Checkbox,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  PhonePrefix,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from "@netpro/design-system";
import { Link, Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { type JSX, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import type { FinalizeSchemaType } from "~/lib/economic-substance/types/bahamas/finalize-schema";
import { EntityRelation, finalizeSchema } from "~/lib/economic-substance/types/bahamas/finalize-schema";
import { ECONOMIC_SUBSTANCE_FORM_ID } from "~/lib/economic-substance/utilities/constants";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";

export function Finalize(): JSX.Element {
  const { submissionData, setIsSubmitting } = useSubmission();
  const data = useMemo(() => submissionData[Pages.FINALIZE] as FinalizeSchemaType, [submissionData]);
  const navigation = useNavigation();
  const fetcher = useFetcher();
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading" || fetcher.state === "submitting";

  // Update context when submission state changes
  useEffect(() => {
    setIsSubmitting(fetcher.state === "submitting");
  }, [fetcher.state, setIsSubmitting]);

  const form = useForm<FinalizeSchemaType>({
    resolver: zodResolver(finalizeSchema),
    shouldFocusError: false,
    defaultValues: {
      confirmationDeclaration: "false",
      authorityToActOnBehalf: "false",
      legitimateInterestForProcessing: "false",
      acknowledgeSubmissionFee: "false",
      declarantName: data?.declarantName ?? "",
      entityRelation: data?.entityRelation ?? undefined,
      otherEntityRelation: data?.otherEntityRelation ?? "",
      telephone: data?.telephone ?? {
        countryCode: "",
        prefix: "",
        number: "",
      },
    },
  });
  const { formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  function onSubmit(data: FinalizeSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const watchEntityRelation = form.watch("entityRelation")

  return (
    <div className="relative">
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={ECONOMIC_SUBSTANCE_FORM_ID}>
          <p>Declaration and confirmation page</p>
          <div className="flex flex-col space-y-5 lg:w-1/2 sm:w-full py-5">
            <FormField
              control={form.control}
              name="confirmationDeclaration"
              render={({ field, fieldState }) => (
                <FormItem>
                  <div className="flex flex-row items-top space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        invalid={!!fieldState.error}
                        onCheckedChange={value => field.onChange(String(value))}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      I confirm that the information provided above is true and accurate to the best
                      of my knowledge and belief
                      and that by submission of this information to the Registered Agent, I have
                      provided all the information required to complete the economic substance
                      self-assessment.*
                    </FormLabel>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="authorityToActOnBehalf"
              render={({ field, fieldState }) => (
                <FormItem>
                  <div className="flex flex-row items-top space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        invalid={!!fieldState.error}
                        onCheckedChange={value => field.onChange(String(value))}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      Please confirm you have the authority to act on behalf of the company.*
                    </FormLabel>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="legitimateInterestForProcessing"
              render={({ field, fieldState }) => (
                <FormItem>
                  <div className="flex flex-row items-top space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        invalid={!!fieldState.error}
                        onCheckedChange={value => field.onChange(String(value))}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      I confirm and acknowledge that the Registered Agent
                      has a legitimate interest for processing any personal data provided above;
                      specifically, in order to ensure the Registered Agent’s
                      and the Entity’s compliance with relevant Bahamas law. I further acknowledge
                      that the processing of such personal data may include
                      its transfer to Bahamas competent authorities and that the Registered Agent’s
                      processing of any personal data will be done
                      in accordance with the
                      {" "}
                      <Button variant="link" className="p-0 h-auto" asChild>
                        <Link to="https://www.tridenttrust.com/legal/privacy-data-protection" target="blank">
                          Trident Trust Data Privacy Policy
                        </Link>
                      </Button>
                      , which I have read and
                      understood*
                    </FormLabel>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="acknowledgeSubmissionFee"
              render={({ field, fieldState }) => (
                <FormItem>
                  <div className="flex flex-row items-top space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        invalid={!!fieldState.error}
                        onCheckedChange={value => field.onChange(String(value))}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      I confirm and acknowledge that the US$200 submission fee is included within
                      the annual licence fee invoice and that settlement of this invoice is due and
                      payable in order to complete the submission process.*
                    </FormLabel>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex-col space-y-2">
            <FormField
              control={form.control}
              name="entityRelation"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>Relation to entity*</FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                        <SelectTrigger invalid={!!fieldState.error}>
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(EntityRelation).map(er => (
                          <SelectItem key={er} value={er}>
                            {er}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchEntityRelation === EntityRelation.OTHER && (
              <FormField
                control={form.control}
                name="otherEntityRelation"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Please specify*</FormLabel>
                    <FormControl className="md:w-1/2 sm:w-full">
                      <Textarea
                        invalid={!!fieldState.error}
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <FormField
              control={form.control}
              name="declarantName"
              render={({ field, fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <FormLabel>Name of person stating the declaration*</FormLabel>
                  <FormControl>
                    <Input
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="telephone"
              render={({ fieldState }) => (
                <FormItem className="lg:w-1/3 md:w-1/2 sm:w-full">
                  <FormLabel>Telephone Number*</FormLabel>
                  <div className="flex">
                    <FormField
                      control={form.control}
                      name="telephone.countryCode"
                      render={({ field }) => (
                        <FormControl>
                          <PhonePrefix
                            invalid={!!fieldState.error}
                            onChange={field.onChange}
                            onPrefixChange={value => form.setValue("telephone.prefix", value)}
                            className="min-w-20 max-w-fit rounded-r-none"
                            value={field.value}
                            disabled={isSubmitting}
                          />
                        </FormControl>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="telephone.number"
                      render={({ field }) => (
                        <FormControl>
                          <Input
                            className="rounded-l-none"
                            type="number"
                            invalid={!!fieldState.error}
                            {...field}
                            disabled={isSubmitting}
                          />
                        </FormControl>
                      )}
                    />
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </RemixForm>
      </Form>
      <LoadingState
        isLoading={isSubmitting}
        message="Saving..."
      />
    </div>
  )
}
