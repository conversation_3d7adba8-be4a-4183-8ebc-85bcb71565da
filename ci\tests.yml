trigger: none
#trigger:
#  - main
#  - feat/*
#  - fix/*
#  - develop

pool:
  vmImage: ubuntu-latest

jobs:
  - job: 'InstallDependencies'
    displayName: 'Install Node.js, Playwright, and Dependencies'
    steps:
      - checkout: self
        clean: true
        persistCredentials: true
        displayName: 'Checkout repository'

      - task: NodeTool@0
        inputs:
          versionSource: 'fromFile'
          versionFilePath: './.nvmrc'
          checkLatest: true
        displayName: 'Install Node.js'

      # Cache node_modules directory for npm dependencies
      - task: Cache@2
        inputs:
          key: 'npm | "$(Agent.OS)" | package.json'
          path: '$(Build.SourcesDirectory)/node_modules'
          cacheHitVar: 'npmCacheHit'
        displayName: 'Cache node_modules'

      # Install npm dependencies if not restored from cache
      - task: Npm@1
        inputs:
          command: 'install'
          workingDir: '.'
          verbose: true
        condition: ne(variables['npmCacheHit'], 'true')
        displayName: 'Install npm dependencies'

      # Cache Playwright browser cache
      - task: Cache@2
        inputs:
          key: 'playwright | "$(Agent.OS)"'
          path: '$(HOME)/.cache/ms-playwright'
          cacheHitVar: 'playwrightCacheHit'
        displayName: 'Cache Playwright browsers'

      # Install Playwright if not restored from cache
      - script: |
          npx playwright install --with-deps
        condition: ne(variables['playwrightCacheHit'], 'true')
        displayName: 'Install Playwright headless browser(s)'

      # Zip node_modules directory before publishing it as an artifact
      - task: ArchiveFiles@2
        inputs:
          rootFolderOrFile: '$(Build.SourcesDirectory)/node_modules'
          includeRootFolder: false
          archiveType: 'zip'
          archiveFile: '$(Build.ArtifactStagingDirectory)/node_modules.zip'
        displayName: 'Zip node_modules'

      # Publish zipped node_modules as an artifact
      - task: PublishPipelineArtifact@1
        inputs:
          targetPath: '$(Build.ArtifactStagingDirectory)/node_modules.zip'
          artifactName: 'node_modules'
        displayName: 'Publish node_modules.zip as artifact'

      # Publish Playwright browser cache as an artifact
      - task: PublishPipelineArtifact@1
        inputs:
          targetPath: '$(HOME)/.cache/ms-playwright'
          artifactName: 'playwright'
        displayName: 'Publish Playwright cache as artifact'

  - job: 'RunTests'
    displayName: 'Run Unit Tests'
    dependsOn: 'InstallDependencies'
    steps:
      - checkout: self
        clean: true
        persistCredentials: true
        displayName: 'Checkout repository'

      - task: NodeTool@0
        inputs:
          versionSource: 'fromFile'
          versionFilePath: './.nvmrc'
          checkLatest: true
        displayName: 'Install Node.js'

      # Download zipped node_modules artifact
      - task: DownloadPipelineArtifact@2
        inputs:
          artifact: 'node_modules'
          targetPath: '$(Build.SourcesDirectory)/'
        displayName: 'Download node_modules.zip artifact'

      # Unzip the node_modules.zip file after downloading
      - task: ExtractFiles@1
        inputs:
          archiveFilePatterns: '$(Build.SourcesDirectory)/node_modules.zip'
          destinationFolder: '$(Build.SourcesDirectory)/node_modules'
        displayName: 'Unzip node_modules.zip'

      # Download Playwright cache artifact
      - task: DownloadPipelineArtifact@2
        inputs:
          artifact: 'playwright'
          targetPath: '$(HOME)/.cache/ms-playwright'
        displayName: 'Download Playwright cache artifact'

      # Debug line to find the context we're working in
      - script: |
          ls -la .

      # Run Vitest unit & component tests
      - task: Npm@1
        inputs:
          command: 'custom'
          workingDir: '.'
          customCommand: 'run test:vitest:ci -- --dir=$(Build.SourcesDirectory)'
        displayName: 'Run Vitest Unit & Component tests'

      # Publish test results to the pipeline
      - task: PublishTestResults@2
        condition: succeededOrFailed()
        inputs:
          testResultsFiles: 'test-results/junit.xml'
          testRunTitle: 'Vitest Tests'
          failTaskOnFailedTests: true
          mergeTestResults: true
          testRunSystem: 'Vitest'
        displayName: 'Publish test results to pipeline'
