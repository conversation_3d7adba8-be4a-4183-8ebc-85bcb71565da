import type { ReactNode } from "react";
import { useMemo } from "react";
import type { z } from "zod";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import type { PageSlug as PageSlugBahamas } from "../utilities/form-pages-bahamas";
import type { PageSlug as PageSlugBVI } from "../utilities/form-pages-bvi";
import { Pages as PagesBahamas } from "../utilities/form-pages-bahamas";
import { Pages as PagesBVI } from "../utilities/form-pages-bvi";
import { formStepsBVI } from "../utilities/form-steps-bvi";
import { formStepsBahamas } from "~/lib/economic-substance/utilities/form-steps-bahamas";

export type FormStep = {
  name: string
  page: PageSlugBahamas | PageSlugBVI
  component: (args?: any) => ReactNode
  validationSchema: z.ZodEffects<any, any> | z.ZodObject<any, any>
  previousPage: string | ((submission: Record<string, any>) => string | null) | null
  nextPage: string | ((submission: Record<string, any>) => string | null) | null
};

// Use in components/client side to benefit from React hooks
export function useFormSteps(jurisdictionName: string): FormStep[] {
  let formSteps;
  if (jurisdictionName === "Bahamas") {
    formSteps = formStepsBahamas;
  } else if (jurisdictionName === "BVI") {
    formSteps = formStepsBVI;
  } else {
    throw new Error("Invalid jurisdiction name");
  }

  return formSteps;
}

export function getFirstStep(jurisdictionName: string): FormStep {
  let formSteps;
  if (jurisdictionName === "Bahamas") {
    formSteps = formStepsBahamas;
  } else if (jurisdictionName === "BVI") {
    formSteps = formStepsBVI;
  } else {
    throw new Error("Invalid jurisdiction name");
  }

  return formSteps[0];
}

export function useCurrentStep(page: string, jurisdictionName: string): FormStep | undefined {
  return useMemo(() => getCurrentStep(page, jurisdictionName), [page, jurisdictionName]);
}

export function useNextStep(submission: Record<string, any>, page: string, jurisdictionName: string): string | null {
  return useMemo(() => getNextStep(submission, page, jurisdictionName), [submission, page, jurisdictionName]);
}

export function usePreviousStep(submission: Record<string, any>, page: string, jurisdictionName: string): string | null {
  return useMemo(() => getPreviousStep(submission, page, jurisdictionName), [submission, page, jurisdictionName]);
}

// Use in server side (loaders, actions)
export function getCurrentStep(page: string, jurisdictionName: string): FormStep | undefined {
  let formSteps;
  if (jurisdictionName === "Bahamas") {
    formSteps = formStepsBahamas;
  } else if (jurisdictionName === "BVI") {
    formSteps = formStepsBVI;
  } else {
    throw new Error("Invalid jurisdiction name");
  }

  return formSteps.find(step => step.page === page);
}

export function getNextStep(submission: Record<string, any>, page: string, jurisdictionName: string): string | null {
  let formSteps;
  if (jurisdictionName === "Bahamas") {
    formSteps = formStepsBahamas;
  } else if (jurisdictionName === "BVI") {
    formSteps = formStepsBVI;
  } else {
    throw new Error("Invalid jurisdiction name");
  }

  const current = formSteps.find(step => step.page === page);
  if (!current) {
    return null;
  }

  if (typeof current.nextPage === "function") {
    return current.nextPage(submission);
  } else {
    return current.nextPage;
  }
}

export function getPreviousStep(submission: Record<string, any>, page: string, jurisdictionName: string): string | null {
  let formSteps;
  if (jurisdictionName === "Bahamas") {
    formSteps = formStepsBahamas;
  } else if (jurisdictionName === "BVI") {
    formSteps = formStepsBVI;
  } else {
    throw new Error("Invalid jurisdiction name");
  }

  const current = formSteps.find(step => step.page === page);
  if (!current) {
    return null;
  }

  if (typeof current.previousPage === "function") {
    return current.previousPage(submission);
  } else {
    return current.previousPage;
  }
}

export function requireValidPage(params: ActionFunctionArgs["params"] | LoaderFunctionArgs["params"], jurisdictionName: string): {
  page: PageSlugBahamas | PageSlugBVI
} {
  let pages;
  if (jurisdictionName === "Bahamas") {
    pages = PagesBahamas;
  } else if (jurisdictionName === "BVI") {
    pages = PagesBVI;
  } else {
    throw new Error("Invalid jurisdiction name");
  }

  // Compare pageName param against Pages values to validate the page name
  const page = params?.pageName as (PageSlugBahamas | PageSlugBVI);
  const validPages = Object.values(pages);
  if (!validPages.includes(page)) {
    throw new Response("Requested page does not exist.", { status: 404 });
  }

  return { page };
}
