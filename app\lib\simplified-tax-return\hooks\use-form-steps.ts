import { useMemo } from "react";
import type { z } from "zod";
import type { JSX } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { formSteps2019 } from "../utilities/form-steps-2019";
import { formSteps2020 } from "../utilities/form-steps-2020";
import { formSteps2021 } from "../utilities/form-steps-2021";
import { formSteps2022 } from "../utilities/form-steps-2022";
import { formSteps2023 } from "../utilities/form-steps-2023";
import { formSteps2024 } from "../utilities/form-steps-2024";
import type { PageSlug } from "~/lib/simplified-tax-return/utilities/form-pages";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";

export type FormStep = {
  name: string
  page: PageSlug
  component: (args?: any) => JSX.Element
  validationSchema: z.ZodEffects<any, any> | z.ZodObject<any, any>
  previousPage: string | ((submission: Record<string, any>) => string | null) | null
  nextPage: string | ((submission: Record<string, any>) => string | null) | null
};

// Add more years here, make sure to add the form steps in the utilities folder
export type FormYear = 2019 | 2020 | 2021 | 2022 | 2023 | 2024;

export const formSteps: Record<FormYear, FormStep[]> = {
  2019: formSteps2019,
  2020: formSteps2020,
  2021: formSteps2021,
  2022: formSteps2022,
  2023: formSteps2023,
  2024: formSteps2024,
};

// Use in components/client side to benefit from React hooks
export function useFormSteps(year: FormYear): FormStep[] {
  return formSteps[year];
}

export function getFirstStep(year: FormYear): FormStep {
  return formSteps[year][0];
}

export function useCurrentStep(year: FormYear, page: string): FormStep | undefined {
  return useMemo(() => getCurrentStep(year, page), [year, page]);
}

export function useNextStep(submission: Record<string, any>, year: FormYear, page: string): string | null {
  return useMemo(() => getNextStep(submission, year, page), [submission, year, page]);
}

export function usePreviousStep(submission: Record<string, any>, year: FormYear, page: string): string | null {
  return useMemo(() => getPreviousStep(submission, year, page), [submission, year, page]);
}

// Use in server side (loaders, actions)
export function getCurrentStep(year: FormYear, page: string): FormStep | undefined {
  const steps = formSteps[year];

  return steps.find(step => step.page === page);
}

export function getNextStep(submission: Record<string, any>, year: FormYear, page: string): string | null {
  const steps = formSteps[year];
  const current = steps.find(step => step.page === page);
  if (!current) {
    return null;
  }

  if (typeof current.nextPage === "function") {
    return current.nextPage(submission);
  } else {
    return current.nextPage;
  }
}

export function getPreviousStep(submission: Record<string, any>, year: FormYear, page: string): string | null {
  const steps = formSteps[year];
  const current = steps.find(step => step.page === page);
  if (!current) {
    return null;
  }

  if (typeof current.previousPage === "function") {
    return current.previousPage(submission);
  } else {
    return current.previousPage;
  }
}

export function requireValidPage(params: ActionFunctionArgs["params"] | LoaderFunctionArgs["params"]): {
  page: PageSlug
} {
  // Compare pageName param against Pages values to validate the page name
  const page = params?.pageName as PageSlug;
  const validPages = Object.values(Pages);
  if (!validPages.includes(page)) {
    throw new Response("Requested page does not exist.", { status: 404 });
  }

  return { page };
}
