import errorMessages from "./utilities/error-messages";

type RequestOptions = {
  method?: string
  headers?: Record<string, string>
  body?: any
  params?: Record<string, string | number | boolean | undefined | null>
  cache?: RequestCache
};

function buildUrlWithParams(
  url: string,
  params?: RequestOptions["params"],
): string {
  if (!params) {
    return url;
  }

  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(
      ([, value]) => value !== undefined && value !== null,
    ),
  );
  if (Object.keys(filteredParams).length === 0) {
    return url;
  }

  const queryString = new URLSearchParams(
    filteredParams as Record<string, string>,
  ).toString();

  return `${url}?${queryString}`;
}

async function fetchApi<T>(
  url: string,
  accessToken: string,
  userId: string,
  options: RequestOptions = {},
): Promise<T> {
  const {
    method = "GET",
    headers = {},
    body,
    params,
    cache = "no-store",
  } = options;
  const fullUrl = buildUrlWithParams(`${process.env.API_BASE_URL}/api/v1${url}`, params);
  const response = await fetch(fullUrl, {
    method,
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${accessToken}`,
      "x-userid": userId,
      ...headers,
    },
    body: body ? JSON.stringify(body) : undefined,
    cache,
  });

  if (!response.ok) {
    const responseBody = await response.text();
    console.error(responseBody);
    const message = responseBody;
    const statusCode = response.status;
    const statusText = response.statusText;
    if (message) {
      const { exceptionMessage, code } = JSON.parse(message);
      const errorMessage = errorMessages[code] || exceptionMessage || "An unknown error occurred";

      throw new Response(errorMessage, {
        status: statusCode,
        statusText,
      });
    }

    throw new Response(message, { status: statusCode, statusText });
  }

  const contentType = response.headers.get("Content-Type");
  if (contentType && contentType.includes("application/json")) {
    // Handle JSON responses
    return response.json();
  }

  return null as T;
}

export const client = {
  get<T>(url: string, accessToken: string, userId: string, options?: RequestOptions): Promise<T> {
    return fetchApi<T>(url, accessToken, userId, { ...options, method: "GET" });
  },
  post<T>(url: string, accessToken: string, userId: string, body?: any, options?: RequestOptions): Promise<T> {
    return fetchApi<T>(url, accessToken, userId, { ...options, method: "POST", body });
  },
  put<T>(url: string, accessToken: string, userId: string, body?: any, options?: RequestOptions): Promise<T> {
    return fetchApi<T>(url, accessToken, userId, { ...options, method: "PUT", body });
  },
  patch<T>(url: string, accessToken: string, userId: string, body?: any, options?: RequestOptions): Promise<T> {
    return fetchApi<T>(url, accessToken, userId, { ...options, method: "PATCH", body });
  },
  delete<T>(url: string, accessToken: string, userId: string, options?: RequestOptions): Promise<T> {
    return fetchApi<T>(url, accessToken, userId, { ...options, method: "DELETE" });
  },
};
