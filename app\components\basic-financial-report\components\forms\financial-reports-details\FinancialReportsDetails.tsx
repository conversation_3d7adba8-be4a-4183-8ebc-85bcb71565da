import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Button, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Label, RadioGroup, RadioGroupItem, Textarea, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Link, Form as RemixForm, useFetcher, useLoaderData, useLocation, useNavigation, useParams } from "@remix-run/react";
import { ChevronLeft, FileSearch, Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { FileUploadDialog } from "../../dialogs/financial-period/FileUploadDialog";
import type { FileSchemaType } from "~/lib/basic-financial-report/types/file-schema";
import { fileSchema } from "~/lib/basic-financial-report/types/file-schema";
import type { FinancialReportsSchemaType } from "~/lib/basic-financial-report/types/financial-reports-details-schema";
import { financialReportsSchema } from "~/lib/basic-financial-report/types/financial-reports-details-schema";
import { BASIC_FINANCIAL_REPORT_FORM_ID } from "~/lib/basic-financial-report/utilities/constants";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { convertMappedDocumentsToFileObject } from "~/lib/utilities/files";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import type { BasicFinancialReportData } from "~/routes/_main._card.basic-financial-report.$id.$pageName";
import type { FinancialPeriodSchemaType } from "~/lib/basic-financial-report/types/financial-period-schema";
import { formatDate } from "~/lib/utilities/format";

export type FileFieldName = keyof Pick<FinancialReportsSchemaType, "accountingRecords">;

export function FinancialReportsDetails(): ReactNode {
  const loader = useLoaderData<BasicFinancialReportData>()
  const location = useLocation()
  const documentsArray = convertMappedDocumentsToFileObject(loader.mappedDocuments)
  const { submissionData, setCanContinue } = useSubmission();
  const { id } = useParams()
  const [open, setOpen] = useState(false);
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const [fieldName, setFieldName] = useState< FileFieldName | undefined>()
  const data = useMemo(() => submissionData[Pages.FINANCIAL_REPORTS_DETAILS] as FinancialReportsSchemaType, [submissionData]);
  const form = useForm<FinancialReportsSchemaType>({
    resolver: zodResolver(financialReportsSchema),
    shouldFocusError: false,
    defaultValues: {
      comments: "",
    },
  });
  const fileForm = useForm<FileSchemaType>({
    resolver: zodResolver(fileSchema),
    shouldFocusError: false,
  });
  const { reset, formState, setValue } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
    if (documentsArray && Object.values(documentsArray).length > 0) {
      setValue("accountingRecords", { files: [documentsArray.accountingRecordsDocumentId] })
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, reset, setValue]);

  const fetcher = useFetcher();
  const fetcherCreateDocument = useFetcher<string>();

  function onSubmit(data: FinancialReportsSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const watchfinancialReportsAccurate = form.watch("financialReportsAccurate")
  const watchAccountingRecords = form.watch("accountingRecords")
  const { startFiscalYear, endFiscalYear } = submissionData[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType
  const dateRange = `${startFiscalYear ? formatDate(startFiscalYear) : "N/A"} to ${endFiscalYear ? formatDate(endFiscalYear) : "N/A"}`

  function onSubmitFile(data: FileSchemaType): void {
    if (fieldName) {
      form.setValue(fieldName, data)
      const formData = new FormData()
      const fileData = data.files[0]
      formData.append("file", fileData)
      formData.append("location", location.pathname)
      fetcherCreateDocument.submit(formData, { action: "/document/create", encType: "multipart/form-data", method: "post" })
    }

    setOpen(false)
  }

  const onOpenDialog = (fieldName: FileFieldName): void => {
    setFieldName(fieldName)
    const files = form.getValues(fieldName)
    fileForm.reset(files, { keepDefaultValues: true })
    setOpen(true)
  }

  useEffect(() => {
    const documentId = fetcherCreateDocument.data
    if (documentId && fieldName) {
      const keyName = `${fieldName}DocumentId`
      fetcherCreateDocument.submit({ data: JSON.stringify({ [keyName]: documentId }), location: location.pathname }, { action: `/basic-financial-report/${id}/pages/${Pages.FINANCIAL_REPORTS_DETAILS}/documents/${documentId}/update`, method: "post" })
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcherCreateDocument.data])

  useEffect(() => {
    if (watchfinancialReportsAccurate === "false") {
      setCanContinue(false)
    }

    if (watchfinancialReportsAccurate === "true") {
      setCanContinue(true)
    }
  }, [setCanContinue, watchfinancialReportsAccurate])

  return (
    <>
      {fieldName && (
        <FileUploadDialog
          form={fileForm}
          open={open}
          setOpen={setOpen}
          onSubmit={onSubmitFile}
        />
      )}
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={BASIC_FINANCIAL_REPORT_FORM_ID} className="space-y-5">
          <div className="flex-col space-y-7">
            <div className="flex flex-col gap-2">
              <Label>Please upload your Accounting Records for the fiscal year</Label>
              <Button className="w-fit" size="sm" asChild>
                <Link to={`/basic-financial-report/${id}/summary`} target="_blank">
                  <FileSearch className="mr-2 size-4" />
                  Preview
                </Link>
              </Button>
            </div>

            <FormField
              control={form.control}
              name="financialReportsAccurate"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Are the Financial Reports accurate?
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                      disabled={isSubmitting}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchfinancialReportsAccurate === "false" && (
              <div className="flex flex-col gap-2">
                <Label>Please return to the previous pages:</Label>
                <Button asChild className="w-fit" size="sm">
                  <Link to={`/basic-financial-report/${id}/${Pages.EQUITY_DETAILS}`}>
                    <ChevronLeft className="mr-2 size-4" />
                    Equity
                  </Link>
                </Button>
                <Button asChild className="w-fit" size="sm">
                  <Link to={`/basic-financial-report/${id}/${Pages.INCOME_DETAILS}`}>
                    <ChevronLeft className="mr-2 size-4" />
                    Income
                  </Link>
                </Button>
                <Button asChild className="w-fit" size="sm">
                  <Link to={`/basic-financial-report/${id}/${Pages.EXPENSE_DETAILS}`}>
                    <ChevronLeft className="mr-2 size-4" />
                    Expenses
                  </Link>
                </Button>
                <Button asChild className="w-fit" size="sm">
                  <Link to={`/basic-financial-report/${id}/${Pages.LIABILITIES_DETAILS}`}>
                    <ChevronLeft className="mr-2 size-4" />
                    Liabilities
                  </Link>
                </Button>
                <Button asChild className="w-fit" size="sm">
                  <Link to={`/basic-financial-report/${id}/${Pages.NON_CURRENT_ASSETS_DETAILS}`}>
                    <ChevronLeft className="mr-2 size-4" />
                    Non-Current Assets
                  </Link>
                </Button>
              </div>
            )}
            <FormField
              control={form.control}
              name="accountingRecords"
              render={() => (
                <FormItem className="flex flex-col">
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        {`Please upload your Accounting Records for the fiscal year ${dateRange}*`}
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      <p>
                        Accounting Records, in relation to a legal person or legal arrangement,
                        is defined as documents in respect of the legal person's or legal arrangement's assets and liabilities,
                        the receipts and expenditure, sales, purchases and other transactions to which the legal person and legal
                        arrangment is a party to. It implies that accounting records (including the underlying documents), can take
                        on many forms and includes: (a) bank statements, (b) receipts, (c) invoices, (d) vouchers, (e)
                        title documents, (f) contracts and agreements, (g) ledgers, and (h) any other documentation underpinning
                        a transaction.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  <FormControl className="md:w-fit sm:w-full">
                    <Button size="sm" onClick={() => onOpenDialog("accountingRecords")} type="button" disabled={isSubmitting}>
                      <Plus className="size-4 mr-2" />
                      {`${watchAccountingRecords ? "File uploaded" : "  Upload file"}`}
                    </Button>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="comments"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>Please indicate any comments/notes that you wish Trident to be aware of. These notes are for Trident only, and will not appear on the statement.</FormLabel>
                  <FormControl className="md:w-1/2 sm:w-full">
                    <Textarea
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </RemixForm>
      </Form>
    </>
  )
}
