import { createCookieSessionStorage } from "@remix-run/node";
import type {
  ActionFunctionArgs,
  LoaderFunctionArgs,
  SessionData,
} from "@remix-run/node";
import type { SessionData as SessionDataValues } from "../types/session-type";

export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: "pcp-application-session",
    secrets: [process.env.SESSION_SECRET || "pcp-app-session"],
    sameSite: "lax",
    path: "/",
    // 2 hours in seconds
    maxAge: 60 * 60 * 2,
    httpOnly: true,
    secure: process.env.NODE_ENV === "production", // only use https in production
  },
});

export const { commitSession, destroySession, getSession } = sessionStorage;
/**
 * These session data keys should never reach the client.
 */
const unsafeSessionDataKeys = ["pkce"];

export function clientSafeSessionData(session: SessionData): Omit<SessionData, typeof unsafeSessionDataKeys[number]> {
  const { data } = session;

  unsafeSessionDataKeys.forEach((key) => {
    delete data[key];
  });

  return data;
}

export async function getSessionData(request: ActionFunctionArgs["request"] | LoaderFunctionArgs["request"]): Promise<SessionDataValues> {
  const session = await getSession(request.headers.get("cookie"));

  return session.data;
}
