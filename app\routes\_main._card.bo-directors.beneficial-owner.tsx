import { <PERSON><PERSON>, Separator } from "@netpro/design-system"
import type { LoaderFunctionArgs } from "@remix-run/node"
import { json } from "@remix-run/node"
import { useLoaderData } from "@remix-run/react"
import type { ReactNode } from "react"
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb"
import { CenteredMessage } from "~/components/errors/CenteredMessage"
import BeneficialOwnerTable from "~/features/bo-directors/components/BeneficialOwnerTable"
import { RequestAssistanceButton } from "~/features/bo-directors/components/RequestAssistanceModal"
import { isCorporateOwner } from "~/features/bo-directors/helpers/isCorporateOwner"
import { isIndividualOwner } from "~/features/bo-directors/helpers/isIndividualOwner"
import { authHeaders } from "~/lib/auth/utils/auth-headers"
import { hasEmptyFields } from "~/lib/bo-directors/utilities/bo-dir-empty-fields"
import { getRequiredBOFields } from "~/lib/bo-directors/utilities/bo-directors-get-required-columns"
import { middleware } from "~/lib/middlewares.server";
import type { BoDirOfficerType } from "~/lib/types/bo-dir-officer-type"
import { Jurisdictions } from "~/lib/utilities/jurisdictions"
import { clientGetCompanyBeneficialOwners } from "~/services/api-generated"

const title = "Beneficial Owners for" as const
const breadCrumbList = [
  {
    href: "/",
    name: "Ownership & Officers",
  },
]

export const handle = {
  breadcrumb: (): ReactNode => <Breadcrumb data={breadCrumbList} />,
  title,
}

export async function loader({ request }: LoaderFunctionArgs) {
  const { company } = await middleware([
    "auth",
    "mfa",
    "terms",
    "requireMcc",
    "requireCompany",
    "requireBoDirModule",
  ], request);
  const { data: beneficialOwnersData, error } = await clientGetCompanyBeneficialOwners({
    headers: await authHeaders(request),
    path: { companyId: company.companyId },
  });
  if (error) {
    throw json({ error }, { status: 500 });
  }

  let { beneficialOwners } = beneficialOwnersData;
  beneficialOwners = beneficialOwners || [];
  const individualOwners = beneficialOwners.filter(isIndividualOwner);
  const corporateOwners = beneficialOwners.filter(isCorporateOwner);
  const hasMissingFields = beneficialOwners.some(owner => hasEmptyFields(owner, getRequiredBOFields(owner.officerTypeCode as BoDirOfficerType)));

  return json({
    company,
    individualOwners,
    corporateOwners,
    hasMissingFields,
  })
}

export default function BeneficialOwners(): ReactNode {
  const { company, individualOwners, corporateOwners, hasMissingFields } = useLoaderData<typeof loader>()
  const isNevis = company.jurisdictionName === Jurisdictions.NEVIS;
  let individualCode: BoDirOfficerType = "VGTP01";
  let corporateCode: BoDirOfficerType = "VGTP02";
  if (isNevis) {
    individualCode = "KNTP01";
    corporateCode = "KNTP02";
  }

  return (
    <div className="flex flex-col w-full justify-between">
      {
        company.isActive
          ? (
              <div className="px-4 py-2.5">
                {hasMissingFields && (
                  <div className="mb-4 mt-2">
                    <Alert variant="error" title="Mandatory data is missing">
                      Mandatory data is missing for the below person(s). Please click
                      "Request update" and provide the missing information.
                    </Alert>
                  </div>
                )}

                {(!individualOwners.length && !corporateOwners.length)
                  ? (

                      <CenteredMessage title="No Beneficial Owners found">
                        <RequestAssistanceButton
                          companyId={company.companyId}
                          assistanceRequestType="NoBeneficialOwner"
                          assistanceRequestComments="Assistance needed for Beneficial Owners"
                        />
                      </CenteredMessage>
                    )
                  : (
                      <div className="flex flex-col gap-5">
                        {individualOwners.length > 0 && (
                          <BeneficialOwnerTable
                            title="Individual Beneficial Owners"
                            items={individualOwners}
                            type="INDIVIDUAL_BO"
                            requiredFields={getRequiredBOFields(individualCode)}
                            visibleColumns={["name", "countryOfBirth", "dateOfBirth"]}
                            jurisdictionName={company.jurisdictionName}
                          />
                        )}
                        {corporateOwners.length > 0 && (
                          <>
                            {individualOwners.length > 0 && (
                              <div className="mt-4">
                                <Separator />
                              </div>
                            )}
                            <BeneficialOwnerTable
                              title="Corporate Beneficial Owners"
                              items={corporateOwners}
                              type="CORPORATE_BO"
                              requiredFields={getRequiredBOFields(corporateCode)}
                              visibleColumns={["name", "countryOfFormation", "incorporationNumber"]}
                              jurisdictionName={company.jurisdictionName}
                            />
                          </>
                        )}
                      </div>
                    )}
              </div>
            )
          : (
              <div className="px-4 py-2.5">
                <div className="mb-4 mt-2">
                  <Alert variant="error" title="Company is closed">
                    Information for Closed Companies is not available within this system.
                    If you believe the Company's status is incorrect, please contact your usual
                    Trident representative or click the button below.
                  </Alert>
                </div>
                <div className="flex flex-col items-center justify-center">
                  <RequestAssistanceButton
                    companyId={company.companyId}
                    assistanceRequestType="NoBeneficialOwner"
                    assistanceRequestComments="Assistance needed for Beneficial Owners"
                  />
                </div>
              </div>
            )
      }
    </div>
  )
}
