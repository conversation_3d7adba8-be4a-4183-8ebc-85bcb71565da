import type { JS<PERSON> } from "react";
import { Link, Outlet, isRouteErrorResponse, useMatches, useRouteError } from "@remix-run/react";
import { Search, ShieldX } from "lucide-react";
import { Button } from "@netpro/design-system";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { middleware } from "~/lib/middlewares.server";
import { sessionStorage } from "~/lib/auth/utils/session.server";
import { CenteredMessage } from "~/components/errors/CenteredMessage";

export const strEntityTypeGuard = ["IBC", "LLC"]

export async function loader({ request }: LoaderFunctionArgs) {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireStrModule"], request);

  const session = await sessionStorage.getSession(
    request.headers.get("Cookie"),
  );

  if (!strEntityTypeGuard.includes(session.get("currentCompany").entityType)) {
    throw new Response("Unauthorized", { status: 401 });
  }

  return null;
}

export function ErrorBoundary(): JSX.Element {
  const error = useRouteError();
  const matches = useMatches();
  const currentPath = matches[matches.length - 1].pathname;
  let title = "Something went wrong. Please try again later.";
  const subtitle = "Use the menu on the left to navigate to another page.";
  let Icon = ShieldX;
  let enableNav = false;

  if (isRouteErrorResponse(error)) {
    switch (error.status) {
      case 404:
        Icon = Search;
        title = "Page not found.";
        break;
      case 412:
        title = error.data;
        break;
      default:
        enableNav = true;
    }
  }

  return (
    // Using the {" "} to prevent navigation buttons from this component
    <CenteredMessage IconComponent={Icon} title={title} subtitle={subtitle}>
      {isRouteErrorResponse(error) && !currentPath.includes("/new") && enableNav && (
        <Button asChild>
          <Link to="./new">Return to the first submission page</Link>
        </Button>
      )}
      {" "}
    </CenteredMessage>
  );
}

export default function SimplifiedTaxReturnLayout(): JSX.Element {
  return <Outlet />
}
