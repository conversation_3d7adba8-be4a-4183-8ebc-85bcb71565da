import { <PERSON><PERSON>, <PERSON><PERSON>, DialogContent } from "@netpro/design-system";
import type { ActionFunctionArgs, LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { Form, json, useFetcher, useLoaderData, useNavigate } from "@remix-run/react";
import type { JSX } from "react";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import type { GetInboxMessageResponse } from "~/services/api-generated";
import { createMessageReadStatus, getInboxMessage } from "~/services/api-generated";

export async function action({ request, params }: ActionFunctionArgs) {
  await middleware(["auth", "terms"], request);
  const { id } = params;
  if (!id) {
    throw new Error("Message ID is required");
  }

  const { error } = await createMessageReadStatus({
    headers: await authHeaders(request),
    path: { messageId: id },
  });

  if (error) {
    return json({ success: false });
  }

  return json({ success: true });
}

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<GetInboxMessageResponse>> {
  await middleware(["auth", "terms"], request);
  const { id } = params;
  if (!id) {
    throw new Error("Message ID is required");
  }

  const { data, error } = await getInboxMessage({
    headers: await authHeaders(request),
    path: { messageId: id },
  });

  if (error || !data) {
    throw new Response("Failed to fetch message", { status: 404 });
  }

  return json(data);
}

export default function InboxMessageDetails(): JSX.Element {
  const navigate = useNavigate();
  const message = useLoaderData<GetInboxMessageResponse>();
  const fetcher = useFetcher();
  const isSubmitting = fetcher.state === "submitting";

  return (
    <Dialog open onOpenChange={() => navigate("/inbox")}>
      <DialogContent className="flex min-w-[800px] flex-col">
        <div className="text-lg font-semibold text-gray-800 mb-6">
          {message.subject}
        </div>
        <div className="border border-gray-300 rounded-md p-4 text-sm mb-6 max-h-[400px] overflow-auto">
          {message?.body?.split(/\r\n|\n/).map((line, index) => (
            <p key={index} className="mb-2">{line}</p>
          ))}
          {message.legalEntities && message.legalEntities.length > 0 && (
            <div className="mt-4">
              <strong>Affected companies:</strong>
              <ul className="list-disc list-inside">
                {message.legalEntities.map((entity, idx) => (
                  <li key={idx}>{entity}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
        {message.urlAttachments && message.urlAttachments.length > 0 && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-600 mb-2">Attached URLs</div>
            <ul className="list-disc list-inside text-sm text-blue-600">
              {message.urlAttachments.map(attachment => (
                <li key={attachment}>
                  <a href={attachment} target="_blank" rel="noopener noreferrer">
                    {attachment}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}
        {message.inboxAttachments && message.inboxAttachments.length > 0 && (
          <div className="mb-6">
            <div className="text-sm font-medium text-gray-600 mb-2">Attachments</div>
            <ul className="list-disc list-inside text-sm text-blue-600">
              {message.inboxAttachments.map(attachment => (
                <li key={attachment.id}>
                  <a
                    href={`/api/download/${attachment.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {attachment.filename}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}
        <div className="flex justify-end">
          <Form method="post">
            <Button
              size="sm"
              variant="default"
              type="submit"
              disabled={isSubmitting || message.isRead}
            >
              {isSubmitting ? "Updating..." : message.isRead ? "Read" : "Mark as Read"}
            </Button>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
