import { type ActionFunctionArgs, json } from "@remix-run/node";
import { z } from "zod";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import type { ErrorResponse } from "~/lib/types/error-response";
import { middleware } from "~/lib/middlewares.server";
import { clientConfirmBeneficialOwner, clientConfirmDirector } from "~/services/api-generated";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

const confirmationSchema = z.object({
  relationId: z.string({
    required_error: "Relation ID is required.",
  }),
})

export async function action({ params, request }: ActionFunctionArgs): Promise<Response> {
  await middleware([
    "auth",
    "mfa",
    "terms",
    "requireMcc",
    "requireCompany",
    "requireBoDirModule",
  ], request);
  const session = await getSession(request.headers.get("Cookie"));

  if (request.method !== "PUT") {
    return json<ErrorResponse>({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  const { id, kind } = params;

  if (!id) {
    throw new Response("Missing id", { status: 400 });
  }

  if (!kind || !["beneficial-owner", "director"].includes(kind)) {
    throw new Response("Invalid officer type", { status: 400 });
  }

  let service;
  if (kind === "beneficial-owner") {
    service = clientConfirmBeneficialOwner;
  } else {
    service = clientConfirmDirector;
  }

  const { data: validatedData, error: parseError } = confirmationSchema.safeParse({ relationId: id });

  if (parseError) {
    const errorMessages = parseError.errors.map(err => err.message).join(", ");
    session.flash("notification", { title: "Validation Error", message: errorMessages, variant: "error" });

    return json({
      success: false,
      error: "Validation error",
    }, {
      headers: { "Set-Cookie": await commitSession(session) },
      status: 400,
    });
  }

  const { data, error } = await service({
    headers: await authHeaders(request),
    path: { relationId: id },
    body: validatedData,
  });

  if (error) {
    session.flash("notification", { title: "Error", message: "An error occurred while processing your request", variant: "error" });

    return json<ErrorResponse>({
      success: false,
      error: "An error occurred while processing your request",
    }, {
      status: 500,
      headers: { "Set-Cookie": await commitSession(session) },
    });
  }

  return json({ success: true, data });
}
