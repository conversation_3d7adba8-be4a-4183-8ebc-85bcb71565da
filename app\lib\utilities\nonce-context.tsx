import type { ReactNode } from "react";
import { createContext, useMemo } from "react";

export const NonceContext = createContext<{ nonce: string }>({ nonce: "" });

export function NonceProvider({ nonce, children }: { nonce: string, children: ReactNode }): ReactNode {
  const value = useMemo(() => ({ nonce }), [nonce]);

  return (
    <NonceContext.Provider value={value}>
      {children}
    </NonceContext.Provider>
  );
}
