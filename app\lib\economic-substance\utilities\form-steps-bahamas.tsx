import type { ReactNode } from "react";
import type { FormStep } from "../hooks/use-form-steps";
import { financialPeriodSchema } from "../types/bahamas/financial-period-schema";
import { entityDetailsSchema } from "../types/bahamas/entity-details-schema";
import { relevantActivityDeclarationSchema } from "../types/bahamas/relevant-activity-declaration-schema";
import { taxPayerIdentificationSchema } from "../types/bahamas/tax-payer-identification-schema";
import { businessSchema } from "../types/bahamas/business-schema";
import { intellectualPropertyBusinessSchema } from "../types/bahamas/intellectual-property-business-schema";
import { finalizeSchema } from "../types/bahamas/finalize-schema";
import { Pages } from "./form-pages-bahamas";
import { getNextSectionFromActivity, getPreviousSectionFromActivity } from "./form-sections-bahamas";
import { FinancialPeriod } from "~/components/economic-substance/bahamas/forms/financial-period/FinancialPeriod";
import { EntityDetails } from "~/components/economic-substance/bahamas/forms/entity-details/EntityDetails";
import {
  RelevantActivityDeclaration,
} from "~/components/economic-substance/bahamas/forms/relevant-activity-declaration/RelevantActivityDeclaration";
import {
  TaxPayerIdentification,
} from "~/components/economic-substance/bahamas/forms/tax-payer-identification/TaxPayerIdentification";
import { HoldingBusiness } from "~/components/economic-substance/bahamas/forms/holding-business/HoldingBusiness";
import { BusinessForm } from "~/components/economic-substance/bahamas/forms/business/BusinessForm";
import {
  IntellectualPropertyBusiness,
} from "~/components/economic-substance/bahamas/forms/intellectual-property-business/IntellectualPropertyBusiness";
import { SupportingDetails } from "~/components/economic-substance/bahamas/forms/supporting-details/SupportingDetails";
import { Finalize } from "~/components/economic-substance/bahamas/forms/finalize/Finalize";

export const formStepsBahamas: FormStep[] = [
  {
    name: "Financial Period",
    page: Pages.FINANCIAL_PERIOD,
    component: (): ReactNode => <FinancialPeriod />,
    validationSchema: financialPeriodSchema,
    previousPage: null,
    nextPage: Pages.ENTITY_DETAILS,
  },
  {
    name: "Entity Details",
    page: Pages.ENTITY_DETAILS,
    component: (): ReactNode => <EntityDetails />,
    validationSchema: entityDetailsSchema,
    previousPage: Pages.FINANCIAL_PERIOD,
    nextPage: Pages.RELEVANT_ACTIVITY_DECLARATION,
  },
  {
    name: "Relevant Activity Declaration",
    page: Pages.RELEVANT_ACTIVITY_DECLARATION,
    component: (): ReactNode => <RelevantActivityDeclaration />,
    validationSchema: relevantActivityDeclarationSchema,
    previousPage: Pages.ENTITY_DETAILS,
    nextPage: (submission: Record<string, any>): string | null => {
      if (submission[Pages.RELEVANT_ACTIVITY_DECLARATION].relevantActivities[0].selected === "true") {
        return Pages.SUPPORTING_DETAILS
      } else {
        return Pages.TAX_PAYER_IDENTIFICATION
      }
    },
  },
  {
    name: "Tax Payer Identification",
    page: Pages.TAX_PAYER_IDENTIFICATION,
    component: (): ReactNode => <TaxPayerIdentification />,
    validationSchema: taxPayerIdentificationSchema,
    previousPage: Pages.RELEVANT_ACTIVITY_DECLARATION,
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.TAX_PAYER_IDENTIFICATION)
    },
  },
  {
    name: "Holding Business",
    page: Pages.HOLDING_BUSINESS,
    component: (): ReactNode => <HoldingBusiness key={Pages.HOLDING_BUSINESS} />,
    validationSchema: taxPayerIdentificationSchema,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.HOLDING_BUSINESS)
    },
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.HOLDING_BUSINESS)
    },
  },
  {
    name: "Finance and Leasing Busines",
    page: Pages.FINANCE_LEASING_BUSINESS,
    component: (): ReactNode => <BusinessForm key={Pages.FINANCE_LEASING_BUSINESS} pageSlug={Pages.FINANCE_LEASING_BUSINESS} />,
    validationSchema: businessSchema as any,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.FINANCE_LEASING_BUSINESS)
    },
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.FINANCE_LEASING_BUSINESS)
    },
  },
  {
    name: "Banking Business",
    page: Pages.BANKING_BUSINESS,
    component: (): ReactNode => <BusinessForm key={Pages.BANKING_BUSINESS} pageSlug={Pages.BANKING_BUSINESS} />,
    validationSchema: businessSchema as any,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.BANKING_BUSINESS)
    },
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.BANKING_BUSINESS)
    },
  },
  {
    name: "Insurance Business",
    page: Pages.INSURANCE_BUSINESS,
    component: (): ReactNode => <BusinessForm key={Pages.INSURANCE_BUSINESS} pageSlug={Pages.INSURANCE_BUSINESS} />,
    validationSchema: businessSchema as any,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.INSURANCE_BUSINESS)
    },
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.INSURANCE_BUSINESS)
    },
  },
  {
    name: "Fund Management Business",
    page: Pages.FUND_MANAGEMENT_BUSINESS,
    component: (): ReactNode => <BusinessForm key={Pages.FUND_MANAGEMENT_BUSINESS} pageSlug={Pages.FUND_MANAGEMENT_BUSINESS} />,
    validationSchema: businessSchema as any,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.FUND_MANAGEMENT_BUSINESS)
    },
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.FUND_MANAGEMENT_BUSINESS)
    },
  },
  {
    name: "Headquarters Business",
    page: Pages.HEADQUARTERS_BUSINESS,
    component: (): ReactNode => <BusinessForm key={Pages.HEADQUARTERS_BUSINESS} pageSlug={Pages.HEADQUARTERS_BUSINESS} />,
    validationSchema: businessSchema as any,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.HEADQUARTERS_BUSINESS)
    },
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.HEADQUARTERS_BUSINESS)
    },
  },
  {
    name: "Shipping Business",
    page: Pages.SHIPPING_BUSINESS,
    component: (): ReactNode => <BusinessForm key={Pages.SHIPPING_BUSINESS} pageSlug={Pages.SHIPPING_BUSINESS} />,
    validationSchema: businessSchema as any,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.SHIPPING_BUSINESS)
    },
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.SHIPPING_BUSINESS)
    },
  },
  {
    name: "Intellectual Property Business",
    page: Pages.INTELLECTUAL_PROPERTY_BUSINESS,
    component: (): ReactNode => <IntellectualPropertyBusiness key={Pages.INTELLECTUAL_PROPERTY_BUSINESS} />,
    validationSchema: intellectualPropertyBusinessSchema as any,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.INTELLECTUAL_PROPERTY_BUSINESS)
    },
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.INTELLECTUAL_PROPERTY_BUSINESS)
    },
  },
  {
    name: "Distribution and Service Centre Business",
    page: Pages.DISTRIBUTION_SERVICE_CENTRE_BUSINESS,
    component: (): ReactNode => <BusinessForm key={Pages.DISTRIBUTION_SERVICE_CENTRE_BUSINESS} pageSlug={Pages.DISTRIBUTION_SERVICE_CENTRE_BUSINESS} />,
    validationSchema: businessSchema as any,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.DISTRIBUTION_SERVICE_CENTRE_BUSINESS)
    },
    nextPage: (submission: Record<string, any>): string | null => {
      return getNextSectionFromActivity(submission, Pages.DISTRIBUTION_SERVICE_CENTRE_BUSINESS)
    },
  },
  {
    name: "Supporting Details",
    page: Pages.SUPPORTING_DETAILS,
    component: (): ReactNode => <SupportingDetails />,
    validationSchema: intellectualPropertyBusinessSchema as any,
    previousPage: (submission: Record<string, any>): string | null => {
      return getPreviousSectionFromActivity(submission, Pages.SUPPORTING_DETAILS)
    },
    nextPage: Pages.FINALIZE,
  },
  {
    name: "Finalize",
    page: Pages.FINALIZE,
    component: (): ReactNode => <Finalize />,
    validationSchema: finalizeSchema,
    previousPage: Pages.SUPPORTING_DETAILS,
    nextPage: Pages.CONFIRMATION,
  },
];
