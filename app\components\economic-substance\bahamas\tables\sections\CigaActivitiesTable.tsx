import { <PERSON><PERSON>, <PERSON>rollArea, <PERSON><PERSON>Bar, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { <PERSON>ci<PERSON>, X } from "lucide-react";
import type { JSX } from "react"
import { ActivityEnum, type CigaActivitySchemaType } from "~/lib/economic-substance/types/bahamas/ciga-schema";
import { activityOptions } from "~/lib/economic-substance/utilities/ciga";

type Props = {
  activities: (CigaActivitySchemaType & { formArrayId: string })[]
  onSelect: (income: CigaActivitySchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function CigaActivitiesTable({
  activities,
  onSelect,
  onDelete,
  disabled,
}: Props): JSX.Element {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>CIGA Description</TableHead>
              {!disabled && <TableHead></TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {!activities.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No activities available
                </TableCell>
              </TableRow>
            )}
            {activities.length > 0 && activities.map((item, index) => (
              <TableRow key={item.formArrayId}>
                <TableCell>{item.description === ActivityEnum.OTHER_PLEASE_SPECIFY ? `${item.description} - ${item.otherActivity}` : activityOptions.find(a => a.value === item.description)?.label}</TableCell>
                {!disabled && (
                  <TableCell className="flex justify-end gap-2">
                    <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(item, index)} disabled={disabled}>
                      <Pencil className="mr-2 size-4" />
                      Edit
                    </Button>
                    <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                      <X className="mr-2 size-4" />
                      Remove
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
