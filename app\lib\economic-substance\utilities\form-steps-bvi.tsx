import type { ReactNode } from "react";
import type { FormStep } from "../hooks/use-form-steps";
import { financialPeriodSchema } from "../types/bvi/financial-period-schema";
import { Pages } from "./form-pages-bvi";
import { FinancialPeriod } from "~/components/economic-substance/bvi/forms/financial-period/FinancialPeriod";

export const formStepsBVI: FormStep[] = [
  {
    name: "Financial Period",
    page: Pages.FINANCIAL_PERIOD,
    component: (): ReactNode => <FinancialPeriod />,
    validationSchema: financialPeriodSchema,
    previousPage: null,
    nextPage: Pages.ENTITY_DETAILS,
  },
  /*
   * {
   *   name: "Entity Details",
   *   page: Pages.ENTITY_DETAILS,
   *   component: (): ReactNode => (
   *     <EquityDetails />
   *   ),
   *   validationSchema: equityDetailsSchema,
   *   previousPage: Pages.FINANCIAL_PERIOD,
   *   nextPage: Pages.RELEVANT_ACTIVITY_DECLARATION,
   * },
   * {
   *   name: "Relevant Activity Declaration",
   *   page: Pages.RELEVANT_ACTIVITY_DECLARATION,
   *   component: (): ReactNode => <IncomeDetails />,
   *   validationSchema: incomeDetailsSchema,
   *   previousPage: Pages.ENTITY_DETAILS,
   *   nextPage: Pages.TAX_PAYER_IDENTIFICATION,
   * },
   * {
   *   name: "Tax Payer Identification",
   *   page: Pages.TAX_PAYER_IDENTIFICATION,
   *   component: (): ReactNode => <ExpenseDetails />,
   *   validationSchema: expenseDetailsSchema,
   *   previousPage: Pages.RELEVANT_ACTIVITY_DECLARATION,
   *   nextPage: Pages.HOLDING_BUSINESS,
   * },
   * {
   *   name: "Holding Business",
   *   page: Pages.HOLDING_BUSINESS,
   *   component: (): ReactNode => <LiabilitiesDetails />,
   *   validationSchema: liabilitiesDetailsSchema,
   *   previousPage: Pages.TAX_PAYER_IDENTIFICATION,
   *   nextPage: Pages.FINANCE_LEASING_BUSINESS,
   * },
   * {
   *   name: "Finance Leasing Business",
   *   page: Pages.FINANCE_LEASING_BUSINESS,
   *   component: (): ReactNode => <NonCurrentAssetsDetails />,
   *   validationSchema: nonCurrentAssetsDetailsSchema,
   *   previousPage: Pages.HOLDING_BUSINESS,
   *   nextPage: Pages.INTELLECTUAL_PROPERTY_BUSINESS,
   * },
   * {
   *   name: "Supporting Details",
   *   page: Pages.INTELLECTUAL_PROPERTY_BUSINESS,
   *   component: (): ReactNode => <CurrentAssetsDetails />,
   *   validationSchema: currentAssetsDetailsSchema,
   *   previousPage: Pages.FINANCE_LEASING_BUSINESS,
   *   nextPage: Pages.SUPPORTING_DETAILS,
   * },
   * {
   *   name: "Financial Reports details",
   *   page: Pages.SUPPORTING_DETAILS,
   *   component: (): ReactNode => <FinancialReportsDetails />,
   *   validationSchema: financialReportsSchema,
   *   previousPage: Pages.INTELLECTUAL_PROPERTY_BUSINESS,
   *   nextPage: Pages.FINALIZE,
   * },
   * {
   *   name: "Finalize",
   *   page: Pages.FINALIZE,
   *   component: (): ReactNode => <Finalize />,
   *   validationSchema: finalizeSchema,
   *   previousPage: (submission: Record<string, unknown>): string | null => {
   *     const { tridentAccountingRecordsTool } = submission[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType
   */

  /*
   *     if (tridentAccountingRecordsTool === "true") {
   *       return Pages.SUPPORTING_DETAILS
   *     }
   */

  /*
   *     if (tridentAccountingRecordsTool === "false") {
   *       return Pages.FINANCIAL_PERIOD
   *     }
   */

  /*
   *     return null
   *   },
   *   nextPage: Pages.CONFIRMATION,
   * },
   */

];
