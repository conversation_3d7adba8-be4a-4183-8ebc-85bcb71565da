import { type ActionFunctionArgs, type TypedResponse, json, redirect } from "@remix-run/node";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { mapDocumentIdsToKeys } from "~/lib/basic-financial-report/utilities/documents";
import { getCurrentStep } from "~/lib/economic-substance/hooks/use-form-steps";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";
import { middleware } from "~/lib/middlewares.server";
import { getFlattenedSubmission } from "~/lib/submission/utilities/submission-data-set";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";
import { removeCategoryKeys } from "~/lib/utilities/files";
import { clientGetSubmission, clientPutSubmissionDataSet } from "~/services/api-generated";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

export async function action({ request, params }: ActionFunctionArgs): Promise<TypedResponse<null> | undefined> {
  const { company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireEsModule"], request);
  const session = await getSession(request.headers.get("Cookie"));
  const { id, pageName } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  if (!pageName) {
    throw new Error("Page Name is required");
  }

  const formData = await request.formData();
  const data = JSON.parse(formData.get("data") as string) as Record<string, string>;
  const bodyDocumentIds = Object.values(data)
  const location = formData.get("location") as string || "/"
  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found")
  }

  const currentStep = getCurrentStep(pageName, company.jurisdictionName);
  if (!currentStep) {
    throw new Error("Current step is not available");
  }

  const unflattenedData = getUnflattenedDataSet(submission);
  const unflattenedDataWithoutDocumentsCategory = removeCategoryKeys(unflattenedData[pageName], Object.keys(data)[0])
  // Update the submission dataset with the new data but keep the other pages intact
  const unflattenedNewSubmission = {
    ...unflattenedData,
    [pageName]: { ...unflattenedDataWithoutDocumentsCategory, ...data },
  };
  const newSubmissionData = getFlattenedSubmission(unflattenedNewSubmission);
  const oldDocumentsId = submission.documentIds ?? [];
  // Combine existing document IDs with the new document ID, ensuring uniqueness
  const documentIds = Array.from(new Set([...oldDocumentsId, ...bodyDocumentIds]));
  let filteredDocumentIds: string[] = documentIds;
  if (submission.documentIds?.length) {
    // Map document IDs to their corresponding keys in the tax payer identification step, if applicable
    const taxPayerIdentificationDocuments = unflattenedNewSubmission[Pages.TAX_PAYER_IDENTIFICATION]
      ? mapDocumentIdsToKeys(unflattenedNewSubmission[Pages.TAX_PAYER_IDENTIFICATION], documentIds)
      : {};
    const intellectualPropertyBusinessDocuments = unflattenedNewSubmission[Pages.INTELLECTUAL_PROPERTY_BUSINESS]
      ? mapDocumentIdsToKeys(unflattenedNewSubmission[Pages.INTELLECTUAL_PROPERTY_BUSINESS], documentIds)
      : {};
    const supportingDetailsDocuments = unflattenedNewSubmission[Pages.SUPPORTING_DETAILS]
      ? mapDocumentIdsToKeys(unflattenedNewSubmission[Pages.SUPPORTING_DETAILS], documentIds)
      : {};
    // Combine valid document IDs from both steps into a single set to ensure uniqueness
    const validDocumentIds = new Set([
      ...Object.keys(taxPayerIdentificationDocuments),
      ...Object.keys(intellectualPropertyBusinessDocuments),
      ...Object.keys(supportingDetailsDocuments),
    ]);

    // Filter the original document IDs to include only those present in the valid document IDs set
    filteredDocumentIds = validDocumentIds.size ? documentIds.filter(id => validDocumentIds.has(id)) : documentIds;
    // This step ensures that only the document IDs linked to the current form steps are preserved.
  }

  try {
    const { error } = await clientPutSubmissionDataSet({
      headers: await authHeaders(request),
      path: { submissionId: id },
      body: { id, dataSet: newSubmissionData, documentIds: filteredDocumentIds },
    })

    if (error) {
      session.flash("notification", { title: "Error!", message: error.exceptionMessage, variant: "error" });

      return redirect(location, {
        headers: { "Set-Cookie": await commitSession(session) },
      });
    }

    session.flash("notification", {
      title: "Success!",
      message: "The file(s) has been uploaded successfully",
      variant: "success",
    });

    return json(null, { headers: { "Set-Cookie": await commitSession(session) } });
  } catch (error) {
    if (error instanceof Response) {
      const errorMessage = await error.text();

      if (errorMessage) {
        session.flash("notification", {
          title: "An Error has occurred",
          message: errorMessage,
          variant: "error",
        });

        return json(null, { status: error.status, headers: { "Set-Cookie": await commitSession(session) } });
      }
    } else {
      throw error;
    }
  }
}
