import { client } from "~/lib/api-client";
import type { MethodKeys } from "~/lib/auth/utils/mfa";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export type MFAMethod = {
  userId: string
  mfaMethod: MethodKeys
}

export function getMfaMethod({ accessToken, userId }: ClientRequestHeaders): Promise<MFAMethod> {
  return client.get(`/security/mfa/users/${userId}/method`, accessToken, userId);
}
