import { <PERSON><PERSON>, <PERSON><PERSON>, cn } from "@netpro/design-system";
import { Link } from "@remix-run/react";
import { FileDown } from "lucide-react";
import { useContext } from "react";
import { formatDate, getTimezoneFromJurisdiction, shortMonthFormat } from "~/lib/utilities/format";
import type { InvoiceDTO } from "~/services/api-generated";
import { SessionContext } from "~/components/session-context";

type InvoiceRowProps = {
  invoice: InvoiceDTO
};

export function InvoiceRow({ invoice }: InvoiceRowProps): JSX.Element {
  const { company } = useContext(SessionContext);
  const timezone = getTimezoneFromJurisdiction(company?.jurisdictionName);
  const formattedPaidAt = invoice?.paidDate ? formatDate(invoice.paidDate, { timezone, formatStr: shortMonthFormat }) : "";

  return (
    <div
      className="flex items-center justify-between py-3 border-2 border-transparent hover:border-blue-600 group px-5 transition-all duration-200 rounded-md"
    >
      <div className="flex items-end space-x-4">
        <div>
          <div className="flex gap-2.5">
            <p className="text-lg font-inter font-semibold">{invoice.companyName}</p>
          </div>
          <div className="flex">
            <div className="flex border-gray-600 border-opacity-10 pr-2 border-r-2 mr-2 ">
              <div className="flex space-x-2">
                <p className="text-sm font-inter text-gray-60">Incorporation number</p>
                <p className="text-sm font-inter text-gray-600 font-semibol">{invoice.incorporationNr}</p>
              </div>
            </div>
            <div className={cn("flex", formattedPaidAt ? "border-gray-600 border-opacity-10 pr-2 border-r-2 mr-2" : "")}>
              <div className="flex  space-x-2">
                <p className="text-sm font-inter text-gray-60">Financial Period</p>
                <p className="text-sm font-inter text-gray-600 font-semibol">{invoice.financialYear}</p>
              </div>
            </div>
            {formattedPaidAt && (
              <div className={cn("flex", invoice.txId ? "border-gray-600 border-opacity-10 pr-2 border-r-2 mr-2" : "")}>
                <div className="flex space-x-2">
                  <p className="text-sm font-inter text-gray-60">Paid</p>
                  <p className="text-sm font-inter text-gray-600 font-semibol">{formattedPaidAt}</p>
                </div>
              </div>
            )}
            {invoice.txId && (
              <div className="flex">
                <div className="flex space-x-2">
                  <p className="text-sm font-inter text-gray-60">TX ID #</p>
                  <p className="text-sm font-inter text-gray-600 font-semibol">{invoice.txId}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center">
        <div className="grid grid-cols-3 gap-4">
          <div className="flex items-center">
            {invoice.paidDate
              ? (
                  <Badge className="text-nowrap" variant="success">Paid</Badge>
                )
              : (
                  <Badge className="text-nowrap" variant="destructive">Pending payment</Badge>
                )}
          </div>
          <div>
            <Button variant="outline" asChild>
              <Link to={`/invoices/${invoice.id}/file`} target="_blank">
                <FileDown className="text-blue-500 size-4 mr-2" />
                Download
              </Link>
            </Button>
          </div>
          <div className="flex items-center justify-end">
            <div className="text-end">
              <h3 className="font-semibold w-full text-sm">
                {`${invoice.currencySymbol} ${invoice.amount}`}
              </h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
