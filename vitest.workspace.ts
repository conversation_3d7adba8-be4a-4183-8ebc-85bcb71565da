import { defineWorkspace } from "vitest/config";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineWorkspace([
  {
    test: {
      name: "component",
      include: ["**/*.test.tsx"],
      exclude: ["**/*.test.ts", "node_modules"],
      browser: {
        provider: "playwright",
        headless: true,
        enabled: true,
        name: "chromium",
      },
    },
    plugins: [tsconfigPaths()],
  },
  {
    test: {
      name: "unit",
      include: ["**/*.test.ts"],
      exclude: ["**/*.test.tsx", "node_modules"],
    },
    plugins: [tsconfigPaths()],
  },
])
