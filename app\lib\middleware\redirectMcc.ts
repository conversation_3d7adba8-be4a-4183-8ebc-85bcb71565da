import { redirect } from "@remix-run/node";
import { commitSession, getSession, getSessionData } from "~/lib/auth/utils/session.server";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";
import type { BasicMasterClient } from "~/features/master-clients/types/generic";

type ReturnData = {
  masterClient: BasicMasterClient
};

export default async function redirectMcc(
  { request }: MiddlewareProps,
): MiddlewareResponse<never | ReturnData> {
  const session = await getSession(request.headers.get("Cookie"));
  const { currentMasterClient } = await getSessionData(request);
  if (!currentMasterClient) {
    throw redirect("/master-clients", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    });
  }

  return { masterClient: currentMasterClient };
}
