import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import type { ReactNode } from "react";
import { getClientCredentialsToken } from "~/lib/auth/utils/authentication.server";
import { commitSession, getSession } from "~/lib/auth/utils/session.server";
import { securitySetUserSignedIn } from "~/services/api-generated";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse> {
  const session = await getSession(request.headers.get("Cookie"));
  // Get session variables from Microsoft Entra authentication result
  const objectId = session.get("objectId") as string;
  const userEmail = session.get("userEmail") as string;
  const { accessToken, expiresOn } = await getClientCredentialsToken();
  const { data: user, error } = await securitySetUserSignedIn({ headers: {
    Authorization: `Bearer ${accessToken}`,
  } as any, query: {
    email: userEmail,
    objectId,
  } });

  if (error) {
    throw new Response("We were not able to sign you in at the moment.", { status: 412 });
  }

  if (user.isBlocked) {
    throw new Response("Your account has been blocked.", { status: 403 });
  }

  const { id, isActive, displayName } = user;

  // Set the accessToken to call the APIs
  session.set("accessToken", accessToken);
  session.set("accessTokenExpiresOn", expiresOn);

  session.set("isActive", isActive);
  session.set("userId", id);
  session.set("userName", displayName);

  const redirectUrl = session.get("redirect") as string | undefined;

  return redirect(redirectUrl || "/dashboard", {
    headers: { "Set-Cookie": await commitSession(session) },
  });
}

export default function AuthApplication(): ReactNode {
  return <p>Redirecting to the Private Client Portal...</p>;
}
