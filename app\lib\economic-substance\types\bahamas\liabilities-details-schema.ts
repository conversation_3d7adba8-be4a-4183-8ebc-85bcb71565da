import { z } from "zod";
import { nonEmptyString, preprocessArray, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";

export const liabilitySchema = z.object({
  description: nonEmptyString("Description"),
  current: stringNumber({ invalidTypeMessage: "Current amount is required.", greaterThan: 0 }),
  nonCurrent: stringNumber({ invalidTypeMessage: "Non-Current amount is required.", greaterThan: 0 }),
})

export const liabilitiesDetailsSchema = z.object({
  companyLiabilities: stringBoolean(),
  loans: preprocessArray(z.array(liabilitySchema)).optional(),
  accountPayableAccrual: preprocessArray(z.array(liabilitySchema)).optional(),
  otherLiabilities: preprocessArray(z.array(liabilitySchema)).optional(),
}).refine(data =>
  !(data.companyLiabilities === "true" && !(data.loans?.length || data.accountPayableAccrual?.length || data.otherLiabilities?.length)), {
  message: "You must add at least one liability in either the Loans, Accounts Payable And Accrual, or Other Liabilities table.",
  path: ["loans", 0],
});

export type LiabilitiesDetailsSchemaType = z.infer<typeof liabilitiesDetailsSchema>;
export type LiabilitySchemaType = z.infer<typeof liabilitySchema>;
