import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@netpro/design-system";

type Props = {
  open: boolean
  onOpenChange?: (open: boolean) => void
  onCloseDeleteConfirmation: () => void
  onDelete: () => void
  isSubmitting: boolean
}
export function DeletePropertyDialog({ open, onOpenChange, onCloseDeleteConfirmation, onDelete, isSubmitting }: Props) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Are you sure you want to delete the property?</DialogTitle>
        </DialogHeader>
        <DialogFooter className="pt-4">
          <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation} disabled={isSubmitting}>Cancel</Button>
          <Button type="button" variant="destructive" onClick={onDelete} disabled={isSubmitting}>Yes, delete this property</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
