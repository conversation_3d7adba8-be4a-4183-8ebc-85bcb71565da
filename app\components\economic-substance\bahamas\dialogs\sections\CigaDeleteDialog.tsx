import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@netpro/design-system";

type Props = {
  open: boolean
  onOpenChange?: (open: boolean) => void
  onCloseDeleteConfirmation: () => void
  onDelete: () => void
  isSubmitting: boolean
}
export function CigaDeleteDialog({ open, onOpenChange, onCloseDeleteConfirmation, onDelete, isSubmitting }: Props) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            This will delete all the CIGA activities that you just added,
            are you sure?
          </DialogTitle>
        </DialogHeader>
        <DialogFooter className="pt-4">
          <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation} disabled={isSubmitting}>Cancel</Button>
          <Button type="button" variant="destructive" onClick={onDelete} disabled={isSubmitting}>Yes, delete it</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
