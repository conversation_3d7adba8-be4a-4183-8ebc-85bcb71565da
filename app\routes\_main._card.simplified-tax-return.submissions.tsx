import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import type { JSX } from "react";
import { Button } from "@netpro/design-system";
import { ChevronRight } from "lucide-react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { requireActiveModule } from "~/features/modules/api/get-modules";
import { Modules } from "~/lib/utilities/modules";
import { getSubmissionsData } from "~/features/companies/api/get-submissions";
import type { Submission } from "~/features/submissions/api/get-submission";
import { SubmissionRow } from "~/features/submissions/components/SubmissionRow";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { middleware } from "~/lib/middlewares.server";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { CenteredMessage } from "~/components/errors/CenteredMessage";

const title = "Completed Submissions for" as const;
const breadCrumbList = [
  {
    href: "/simplified-tax-return/new",
    name: "Simplified Tax Returns",
  },
];

type LoaderResponse = {
  submissions: Submission[]
}

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse<LoaderResponse | never>> {
  const { userId, accessToken, company } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  const { module } = await requireActiveModule({ request, key: Modules.SIMPLIFIED_TAX_RETURN, companyId: company.companyId });
  const submissions = (await getSubmissionsData({ company, module, accessToken, userId }))?.filter(
    submission => submission.statusText === SubmissionStatusNames.Submitted,
  );

  return json({ submissions });
}

export default function STRSubmissions(): JSX.Element {
  const { submissions } = useLoaderData<typeof loader>();

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 py-2.5">
        {submissions && submissions.length > 0
          ? (
              submissions.map(submission => (
                <SubmissionRow
                  submission={submission}
                  key={submission.id}
                  moduleUrl="simplified-tax-return"
                  continuePageName={Pages.ADDRESS_OF_HEAD_OFFICE}
                />
              ))
            )
          : (
              <CenteredMessage title="No submissions have been completed">
                <Button asChild>
                  <Link to="/simplified-tax-return/new">
                    File new submission
                    <ChevronRight />
                  </Link>
                </Button>
              </CenteredMessage>
            )}
      </div>
    </div>
  );
}
