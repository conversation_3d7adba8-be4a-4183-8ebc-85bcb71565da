import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Label, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { IncomeTable } from "../../tables/IncomeTable";
import { IncomeDialog } from "../../dialogs/IncomeDialog";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { BASIC_FINANCIAL_REPORT_FORM_ID } from "~/lib/basic-financial-report/utilities/constants";
import { Currency } from "~/lib/basic-financial-report/utilities/currencies";
import type { IncomeSchemaType } from "~/lib/basic-financial-report/types/income-schema";
import { incomeSchema } from "~/lib/basic-financial-report/types/income-schema";
import type { ExpenseDetailsSchemaType } from "~/lib/basic-financial-report/types/expense-details-schema";
import { expenseDetailsSchema } from "~/lib/basic-financial-report/types/expense-details-schema";

type FieldNameType = "otherCompanyExpenses" | "otherPeriodPaidExpenses" | "otherPeriodNotPaidExpenses"

export function ExpenseDetails(): ReactNode {
  const { submissionData } = useSubmission();
  const data = useMemo(() => submissionData[Pages.EXPENSE_DETAILS] as ExpenseDetailsSchemaType, [submissionData]);
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const [open, setOpen] = useState(false);
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [incomeIndex, setIncomeIndex] = useState<number | undefined>();
  const [fieldName, setFieldName] = useState< FieldNameType | undefined>()
  const form = useForm<ExpenseDetailsSchemaType>({
    resolver: zodResolver(expenseDetailsSchema),
    shouldFocusError: false,
    defaultValues: {
      portfolioManagementFeesPeriod: "",
      companyAdministrationFeesPeriod: "",
      loanInterestPayments: "",
      bankCharges: "",
      taxWithheld: "",
      portfolioManagementFees: "",
      companyAdministrationFees: "",
      otherCompanyExpenses: [],
      beginningInterestPayable: "",
      otherPeriodPaidExpenses: [],
      endingInterestPayableLoans: "",
      otherPeriodNotPaidExpenses: [],
      dividendsPaidShareholders: "",
      dividendsNotPaidShareholders: "",
      totalPaymentsPurchaseSecuritiesInvestments: "",
    },

  });
  const incomeForm = useForm<IncomeSchemaType>({
    resolver: zodResolver(incomeSchema),
    defaultValues: { description: "", amount: "" },
  });
  const otherCompanyExpensesArray = useFieldArray({
    control: form.control,
    name: "otherCompanyExpenses",
    keyName: "formArrayId",
  });
  const otherPeriodPaidExpensesArray = useFieldArray({
    control: form.control,
    name: "otherPeriodPaidExpenses",
    keyName: "formArrayId",
  });
  const otherPeriodNotPaidExpensesArray = useFieldArray({
    control: form.control,
    name: "otherPeriodNotPaidExpenses",
    keyName: "formArrayId",
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    reset(data, { keepDefaultValues: true });
  }, [data, reset]);

  function addIncome(fieldName: FieldNameType): void {
    incomeForm.reset();
    setFieldName(fieldName)
    setIncomeIndex(undefined);
    setOpen(true);
  }

  function onSubmitIncome(data: IncomeSchemaType): void {
    if (fieldName === "otherCompanyExpenses") {
      if (incomeIndex !== undefined) {
        otherCompanyExpensesArray.update(incomeIndex, data);
      } else {
        otherCompanyExpensesArray.append(data);
      }
    }

    if (fieldName === "otherPeriodPaidExpenses") {
      if (incomeIndex !== undefined) {
        otherPeriodPaidExpensesArray.update(incomeIndex, data);
      } else {
        otherPeriodPaidExpensesArray.append(data);
      }
    }

    if (fieldName === "otherPeriodNotPaidExpenses") {
      if (incomeIndex !== undefined) {
        otherPeriodNotPaidExpensesArray.update(incomeIndex, data);
      } else {
        otherPeriodNotPaidExpensesArray.append(data);
      }
    }

    setOpen(false);
  }

  function onSelect(fieldName: FieldNameType, income: IncomeSchemaType, index: number): void {
    setFieldName(fieldName)
    incomeForm.reset(income, { keepDefaultValues: true });
    setIncomeIndex(index);
    setOpen(true);
  }

  function onDelete(): void {
    if (fieldName === "otherCompanyExpenses") {
      otherCompanyExpensesArray.remove(incomeIndex);
    }

    if (fieldName === "otherPeriodPaidExpenses") {
      otherPeriodPaidExpensesArray.remove(incomeIndex);
    }

    if (fieldName === "otherPeriodNotPaidExpenses") {
      otherPeriodNotPaidExpensesArray.remove(incomeIndex);
    }

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(fieldName: FieldNameType, index: number): void {
    setFieldName(fieldName)
    setIncomeIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setIncomeIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  const fetcher = useFetcher();

  function onSubmit(data: ExpenseDetailsSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  return (
    <>
      <IncomeDialog
        open={open}
        setOpen={setOpen}
        form={incomeForm}
        onSubmit={onSubmitIncome}
      />
      <Dialog open={openDeletedConfirmation} onOpenChange={setOpenDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete the property?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation} disabled={isSubmitting}>Cancel</Button>
            <Button type="button" variant="destructive" onClick={onDelete} disabled={isSubmitting}>Yes, delete this property</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={BASIC_FINANCIAL_REPORT_FORM_ID} className="space-y-5">
          <div className="flex-col space-y-7">
            <div>
              <Tooltip delayDuration={0}>
                <Label>
                  <p className="flex gap-1">
                    Expenses
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                </Label>
                <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                  <p>
                    An expense is the cost of operations that a company incurs to generate revenue.
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div>
              <Label>Administrative and Operational Expenses</Label>
            </div>
            <FormField
              control={form.control}
              name="portfolioManagementFeesPeriod"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Please indicate any portfolio management fees and related services paid by the company for the period*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="companyAdministrationFeesPeriod"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Please indicate any company administration fees paid by the company for the period. (e.g. Director/Officer fees, Registered agent fees and other Trident invoices)*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="loanInterestPayments"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Please indicate any loan interest payments made for the period, if any.*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div>
              <Label>Please indicate other expenses paid by the company for the period. Kindly specify, if any:</Label>
            </div>
            <FormField
              control={form.control}
              name="bankCharges"
              render={({ field, fieldState }) => (
                <FormItem>
                  <Tooltip delayDuration={0}>
                    <FormLabel>
                      <p className="flex gap-1">
                        Bank charges*
                        <TooltipTrigger asChild>
                          <Info className="flex shrink-0 size-4" />
                        </TooltipTrigger>
                      </p>
                    </FormLabel>
                    <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                      <p>
                        The term bank charge covers all charges and fees made by a bank to their customers. In common parlance, the term often relates to charges in respect of personal current accounts or checking account.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="taxWithheld"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Tax withheld*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div>
              <Label>Please indicate the company expenses paid by the shareholder/s:</Label>
            </div>
            <FormField
              control={form.control}
              name="portfolioManagementFees"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Portfolio management fees and related services*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="companyAdministrationFees"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Company administration fees*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="otherCompanyExpenses"
              control={form.control}
              render={({ fieldState }) => (
                <FormItem>
                  <FormLabel>Other expense. Please specify</FormLabel>
                  {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                  <FormControl>
                    <IncomeTable
                      disabled={isSubmitting}
                      incomes={otherCompanyExpensesArray.fields}
                      onSelect={(income, index) => onSelect("otherCompanyExpenses", income, index)}
                      onDelete={index => onOpenDeleteConfirmation("otherCompanyExpenses", index)}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button size="sm" onClick={() => addIncome("otherCompanyExpenses")} type="button" disabled={isSubmitting}>
                <Plus className="mr-2 size-4 text-white" />
                Add Other Expense
              </Button>
            </div>
            <div>
              <Label>Please indicate prior period expenses paid this period:</Label>
            </div>
            <FormField
              control={form.control}
              name="beginningInterestPayable"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Beginning interest payable*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="otherPeriodPaidExpenses"
              control={form.control}
              render={({ fieldState }) => (
                <FormItem>
                  <FormLabel>Other expense. Please specify</FormLabel>
                  {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                  <FormControl>
                    <IncomeTable
                      disabled={isSubmitting}
                      incomes={otherPeriodPaidExpensesArray.fields}
                      onSelect={(income, index) => onSelect("otherPeriodPaidExpenses", income, index)}
                      onDelete={index => onOpenDeleteConfirmation("otherPeriodPaidExpenses", index)}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button size="sm" onClick={() => addIncome("otherPeriodPaidExpenses")} type="button" disabled={isSubmitting}>
                <Plus className="mr-2 size-4 text-white" />
                Add Other Expense
              </Button>
            </div>
            <div>
              <Label>Please indicate any expenses incurred this period but not yet paid:</Label>
            </div>
            <FormField
              control={form.control}
              name="endingInterestPayableLoans"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Ending interest payable on loans*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="otherPeriodNotPaidExpenses"
              control={form.control}
              render={({ fieldState }) => (
                <FormItem>
                  <FormLabel>Other expense. Please specify</FormLabel>
                  {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                  <FormControl>
                    <IncomeTable
                      disabled={isSubmitting}
                      incomes={otherPeriodNotPaidExpensesArray.fields}
                      onSelect={(income, index) => onSelect("otherPeriodNotPaidExpenses", income, index)}
                      onDelete={index => onOpenDeleteConfirmation("otherPeriodNotPaidExpenses", index)}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex justify-end">
              <Button size="sm" onClick={() => addIncome("otherPeriodNotPaidExpenses")} type="button" disabled={isSubmitting}>
                <Plus className="mr-2 size-4 text-white" />
                Add Other Expense
              </Button>
            </div>
            <div>
              <Tooltip delayDuration={0}>
                <Label>
                  <p className="flex gap-1">
                    Dividends:
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                </Label>
                <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                  <p>
                    A dividend is a distribution of profits by a corporation to its shareholders. When a corporation earns a profit or surplus, it is able to pay a proportion of the profit as a dividend to shareholders.
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <FormField
              control={form.control}
              name="dividendsPaidShareholders"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Please indicate any dividends paid to shareholder/s for the period.*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="dividendsNotPaidShareholders"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Please indicate any dividends declared but not yet paid to shareholder/s for the period, if any.*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div>
              <Label>
                <p>Securities Investment purchases:</p>
              </Label>
            </div>
            <FormField
              control={form.control}
              name="totalPaymentsPurchaseSecuritiesInvestments"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Please indicate the TOTAL payments for the purchase of securities investments for the period.*</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      disabled={isSubmitting}
                      type="number"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </RemixForm>
      </Form>
    </>
  )
}
