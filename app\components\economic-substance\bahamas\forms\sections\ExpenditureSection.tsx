import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@netpro/design-system";
import { useNavigation } from "@remix-run/react";
import { useFormContext } from "react-hook-form";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import type { ExpenditureSchemaType } from "~/lib/economic-substance/types/bahamas/expenditure-schema";
import { Currency } from "~/lib/economic-substance/utilities/currencies";

export function ExpenditureSection() {
  const form = useFormContext<ExpenditureSchemaType>()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"

  return (
    <>
      <p className="text-md font-bold">Expenditure</p>
      <FormField
        control={form.control}
        name="totalExpenditureRelevantActivity"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Total expenditure incurred in the operations of the relevant activity during
                the financial period (including outsourcing, if applicable).*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <CurrencyInput
                currencyName={Currency.USD}
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="totalExpenditureBahamas"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Total expenditure incurred in the Bahamas in the operations
                of the relevant activity during the financial period (including outsourcing, if applicable)*.
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <CurrencyInput
                currencyName={Currency.USD}
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
                type="number"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  )
}
