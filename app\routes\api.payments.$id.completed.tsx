import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { json } from "@remix-run/react";
import type { SubmittedTransaction } from "~/features/payments/api/submit-transaction";
import { submitTransaction } from "~/features/payments/api/submit-transaction";
import { middleware } from "~/lib/middlewares.server";

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<SubmittedTransaction>> {
  const { userId, accessToken } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  // Get the paymentId, transactionId and tokenId from the URL
  const { id: paymentId } = params;
  if (!paymentId) {
    throw new Response("Missing paymentId", { status: 400 });
  }

  const url = new URL(request.url);
  const tokenId = url.searchParams.get("tokenId");
  const transactionId = url.searchParams.get("transactionId");

  if (!tokenId || !transactionId) {
    throw new Response("Missing tokenId or transactionId", { status: 400 });
  }

  const transactionResult = await submitTransaction({
    paymentId,
    data: { tokenId, transactionId },
    accessToken,
    userId,
  });

  return json(transactionResult);
}
