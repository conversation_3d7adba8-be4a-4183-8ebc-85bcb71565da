import { type LoaderFunctionArgs, type TypedResponse, redirect } from "@remix-run/node";
import type { ReactNode } from "react";
import { getAccessToken } from "~/lib/auth/utils/authentication.server";

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse> {
  const url = new URL(request.url);
  const params = new URLSearchParams(url.search);
  const code = params.get("error_subcode");
  if (code === "cancel") {
    return redirect("/");
  }

  return getAccessToken(request, "/auth/application");
}

export default function AuthCallback(): ReactNode {
  return <p>Redirecting to the Private Client Portal...</p>;
}
