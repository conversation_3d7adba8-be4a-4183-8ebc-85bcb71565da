import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Button, Combobox, Form, FormControl, FormField, FormItem, FormMessage } from "@netpro/design-system";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { Form as RemixForm, useLoaderData, useSearchParams } from "@remix-run/react";
import { FilterX } from "lucide-react";
import type { ReactNode } from "react";
import { useForm } from "react-hook-form";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { CenteredMessage } from "~/components/errors/CenteredMessage";
import { Pagination } from "~/components/ui/filters/Pagination";
import type { Company } from "~/features/companies/api/get-companies";
import { getCompanies } from "~/features/companies/api/get-companies";
import { InvoiceRow } from "~/features/payments/components/InvoiceRow";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { middleware } from "~/lib/middlewares.server";
import type { paymentsFilterSchemaType } from "~/lib/payments/types/payment-schema";
import { paymentsFilterSchema } from "~/lib/payments/types/payment-schema";
import { getPaginationParams } from "~/lib/utilities/get-pagination-params";
import type { InvoiceDTOPaginatedResponse } from "~/services/api-generated";
import { clientGetInvoices } from "~/services/api-generated";

const title = "Invoices" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Payments",
  },
];

export const handle = {
  breadcrumb: (): JSX.Element => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request }: LoaderFunctionArgs): Promise<{
  invoices: InvoiceDTOPaginatedResponse
  companies: Company[]
}> {
  const { userId, accessToken, masterClient } = await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany"], request);
  let filters = {};
  const url = new URL(request.url);
  const searchParams = Object.fromEntries(url.searchParams.entries());
  const { pageNumber, pageSize } = await getPaginationParams({ request });

  if (searchParams) {
    // Validate params
    paymentsFilterSchema.parse(searchParams);
    // Set filters
    filters = {
      companyId: searchParams.company,
      financialYear: searchParams.financialYear,
    }
  }

  const { data: invoices, error } = await clientGetInvoices({
    headers: await authHeaders(request),
    query: {
      ...filters,
      MasterClientId: masterClient.masterClientId,
      PageSize: pageSize,
      PageNumber: pageNumber,
    },
  });

  if (error || !invoices) {
    throw new Response("Failed to fetch invoices", { status: 500 });
  }

  const { companies } = await getCompanies({
    masterClientId: masterClient.masterClientId as string,
    accessToken,
    userId,
    params: {
      includeInactive: true,
    },
  });

  return { invoices, companies };
}

export default function PaymentInvoices(): ReactNode {
  const { invoices, companies } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const form = useForm<paymentsFilterSchemaType>({
    resolver: zodResolver(paymentsFilterSchema),
    defaultValues: {
      company: searchParams.get("company") ?? "all",
      financialYear: searchParams.get("financialYear") ?? "all",
    },
  });
  const companiesOptions = companies.map(company => ({
    value: company.companyId,
    label: company.companyName,
  }));
  companiesOptions.unshift({ value: "all", label: "All companies" });
  // Generate financial years options from 2019 to this year
  const availableFinancialYears = Array.from({ length: new Date().getFullYear() + 1 - 2019 }, (_, i) => 2019 + i);
  const financialYearsOptions = [
    { value: "all", label: "All financial years" },
    ...availableFinancialYears.map(year => ({ value: year.toString(), label: year.toString() })),
  ];

  function handleSubmitFilter(data: paymentsFilterSchemaType): void {
    setSearchParams((prev) => {
      if (data.company === "all" || !data.company) {
        prev.delete("company");
      } else if (data.company) {
        prev.set("company", data.company);
      }

      if (data.financialYear === "all" || !data.financialYear) {
        prev.delete("financialYear");
      } else if (data.financialYear) {
        prev.set("financialYear", data.financialYear);
      }

      return prev;
    });
  }

  function clearFilters(): void {
    form.setValue("company", "all");
    form.setValue("financialYear", "all");
    form.handleSubmit(handleSubmitFilter)();
  }

  return (
    <div className="flex flex-col w-full justify-between">
      <div className="px-4 py-1">
        <Form {...form}>
          <RemixForm noValidate>
            <div className="grid md:grid-cols-4 gap-2 items-center">
              <FormField
                control={form.control}
                name="company"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="w-full">
                      <Combobox
                        placeholder="All companies"
                        searchText="Search for a companies..."
                        noResultsText="No companies found."
                        items={companiesOptions}
                        onChange={(value) => {
                          field.onChange(value);
                          form.handleSubmit(handleSubmitFilter)();
                        }}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="financialYear"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="w-full">
                      <Combobox
                        placeholder="All financial years"
                        searchText="Search for a financial year..."
                        noResultsText="No financial years found."
                        items={financialYearsOptions}
                        onChange={(value) => {
                          field.onChange(value);
                          form.handleSubmit(handleSubmitFilter)();
                        }}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div>
                <Button type="button" onClick={clearFilters} className="h-9">
                  <FilterX className="mr-2 size-4" />
                  Reset filters
                </Button>
              </div>
            </div>
          </RemixForm>
        </Form>
        <div className="mt-8">
          {invoices.data && invoices?.data.length > 0 && (
            <>
              {invoices?.data.map(invoice => (
                <InvoiceRow invoice={invoice} key={invoice.id} />
              ))}
              <Pagination totalItems={invoices.totalItemCount as number} />
            </>
          )}
          {invoices.data && invoices?.data.length === 0 && (
            <CenteredMessage title="No invoices found" />
          )}
        </div>
      </div>
    </div>
  )
}
