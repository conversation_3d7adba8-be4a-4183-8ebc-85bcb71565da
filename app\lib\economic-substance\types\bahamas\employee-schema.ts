import { z } from "zod";
import { nonEmptyString, preprocessArray, stringNumber } from "~/lib/utilities/zod-validators";

export const FORM_ID = "employee-form"

export const employeeSchema = z.object({
  fullName: nonEmptyString("Full Name"),
  qualification: nonEmptyString("Qualification"),
  yearsOfExperience: nonEmptyString("Years of Relevant Experience"),
  contractType: nonEmptyString("Contract Type"),
})

export type EmployeeSchemaType = z.infer<typeof employeeSchema>

export const employeesSchema = z.object({
  totalEmployeesEntity: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true }),
  totalEmployeesRelevantActivity: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true }),
  totalEmployeesBahamas: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true }),
  employees: preprocessArray(z.array(employeeSchema)),
})
  .refine((data) => {
  // Ensure totalEmployeesRelevantActivity is less than or equal to totalEmployeesEntity
    if (!(Number.parseFloat(data.totalEmployeesRelevantActivity) <= Number.parseFloat(data.totalEmployeesEntity))) {
      return false;
    }

    return true;
  }, {
    path: ["totalEmployeesRelevantActivity"],
    message: "Ensure total number of employees relevant activity is less than or equal to total employees of the entity",
  })
  .refine((data) => {
    // Ensure totalEmployeesBahamas is less than or equal to totalEmployeesRelevantActivity
    if (!(Number.parseFloat(data.totalEmployeesBahamas) <= Number.parseFloat(data.totalEmployeesRelevantActivity))) {
      return false;
    }

    return true;
  }, {
    path: ["totalEmployeesBahamas"],
    message: "Ensure total number of employees present in the Bahamas is less than or equal to total number of employees relevant activity",
  })
  .refine((data) => {
    // Ensure the number of employees added is greater than or equal to totalEmployeesBahamas
    const totalBahamasEmployees = Number.parseFloat(data.totalEmployeesBahamas);
    const addedEmployeesCount = data.employees.length;
    if (!(addedEmployeesCount >= totalBahamasEmployees)) {
      return false;
    }

    return true;
  }, {
    path: ["employees", 0],
    message: "Ensure the number of employees added is greater than or equal to total employees present in the Bahamas",
  })

export type EmployeesSchemaType = z.infer<typeof employeesSchema>

export function getEmployeesDefaultValues(data: EmployeesSchemaType | undefined): EmployeesSchemaType {
  return {
    totalEmployeesEntity: data?.totalEmployeesEntity ?? "",
    totalEmployeesRelevantActivity: data?.totalEmployeesRelevantActivity ?? "",
    totalEmployeesBahamas: data?.totalEmployeesBahamas ?? "",
    employees: data?.employees ?? [],
  };
}
