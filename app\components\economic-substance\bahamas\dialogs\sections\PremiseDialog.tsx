import {
  Button,
  Combobox,
  Dialog,
  DialogContent,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  ScrollArea,
  ScrollBar,
} from "@netpro/design-system";
import { type ReactNode, useMemo } from "react";
import type { UseFormReturn } from "react-hook-form";
import { Form as RemixForm } from "@remix-run/react"

import type { PremiseSchemaType } from "~/lib/economic-substance/types/bahamas/premises-schema";
import { getCountryOptions } from "~/lib/utilities/countries";
import { FORM_ID } from "~/lib/economic-substance/types/bahamas/employee-schema";

  type Props = {
    open: boolean
    setOpen: (open: boolean) => void
    onSubmit: (data: PremiseSchemaType) => void
    form: UseFormReturn<PremiseSchemaType>
  }

export function PremiseDialog({
  open,
  setOpen,
  onSubmit,
  form,
}: Props): ReactNode {
  const countryOptions = useMemo(() => getCountryOptions(), []);
  function handleFormSubmit(e: React.FormEvent) {
    // avoid to trigger parent form
    e.preventDefault();
    e.stopPropagation()
    form.handleSubmit(onSubmit)();
  }

  return (
    <Dialog open={open} onOpenChange={setOpen} modal>
      <DialogContent
        className="max-w-screen-sm"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <ScrollArea className="pr-3">
          <Form {...form}>
            <RemixForm onSubmit={handleFormSubmit} className="p-2" noValidate id={FORM_ID}>
              <DialogHeader>
                <DialogTitle>Premise Details</DialogTitle>
              </DialogHeader>
              <div className="flex-col space-y-2 pt-4">
                <FormField
                  control={form.control}
                  name="addressLine1"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Address Line 1*</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="addressLine2"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Address Line 2</FormLabel>
                      <FormControl>
                        <Input
                          invalid={!!fieldState.error}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field, fieldState }) => (
                    <FormItem className="md:w-1/2 sm:w-full">
                      <FormLabel>Country*</FormLabel>
                      <FormControl>
                        <Combobox
                          placeholder="Select a country"
                          searchText="Search..."
                          noResultsText="No countries found."
                          items={countryOptions}
                          onChange={field.onChange}
                          value={field.value}
                          invalid={!!fieldState.error}
                          disabled
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter className="pt-4">
                <div className="flex w-full justify-end">
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => setOpen(false)} type="button">Cancel</Button>
                    <Button size="sm" variant="default" type="submit" form={FORM_ID}>Save</Button>
                  </div>
                </div>
              </DialogFooter>
            </RemixForm>
          </Form>
          <ScrollBar />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
