import {
  Button,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  RadioGroup,
  RadioGroupItem,
  Textarea,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@netpro/design-system";
import { useFetcher, useLoaderData, useNavigation, useParams } from "@remix-run/react";
import { useForm, useFormContext } from "react-hook-form";
import { Info, Plus } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { FileUploadDialog } from "../../dialogs/financial-period/FileUploadDialog";
import type { IntellectualPropertySchemaType } from "~/lib/economic-substance/types/bahamas/intellectual-property-schema";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { Currency } from "~/lib/economic-substance/utilities/currencies";
import { convertMappedDocumentsToFileObject, groupDocuments } from "~/lib/utilities/files";
import type { EconomicSubstanceData } from "~/routes/_main._card.economic-substance.$id.$pageName";
import type { FileSchemaType } from "~/lib/economic-substance/types/bahamas/file-schema";
import { fileSchema } from "~/lib/economic-substance/types/bahamas/file-schema";
import { Pages } from "~/lib/economic-substance/utilities/form-pages-bahamas";

export type FileFieldName = keyof Pick<IntellectualPropertySchemaType, "files_BusinessPlanDocument" | "files_EvidenceDocument" | "files_AdditionalEvidence">;

export function IntellectualProperty() {
  const { id } = useParams()
  const form = useFormContext<IntellectualPropertySchemaType>()
  const loader = useLoaderData<EconomicSubstanceData>()
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const { watch, setValue } = form
  const { isHighRiskIpEntity, files_BusinessPlanDocument, files_EvidenceDocument, files_AdditionalEvidence } = watch()
  // FILE HANDLING SECTION
  const documentsArray = convertMappedDocumentsToFileObject(loader.mappedDocuments)
  const [fileFieldName, setFileFieldName] = useState<FileFieldName | undefined>()
  const fetcherCreateDocument = useFetcher<string[]>();
  const fetcherUpdateDocument = useFetcher();
  const [openUploadFile, setOpenUploadFile] = useState(false);
  const fileForm = useForm<FileSchemaType>({
    resolver: zodResolver(fileSchema),
    shouldFocusError: false,
  });

  function onSubmitFile(data: FileSchemaType): void {
    if (fileFieldName) {
      form.setValue(fileFieldName, data)
      const formData = new FormData()
      const filesData = data.files
      filesData.forEach((fileData) => {
        formData.append("files", fileData)
      });
      formData.append("location", location.pathname)
      fetcherCreateDocument.submit(formData, {
        action: "/documents/create",
        encType: "multipart/form-data",
        method: "post",
      })
    }

    setOpenUploadFile(false)
  }

  const onOpenUploadDialog = (fieldName: FileFieldName): void => {
    setFileFieldName(fieldName)
    const files = form.getValues(fieldName)
    fileForm.reset(files, { keepDefaultValues: true })
    setOpenUploadFile(true)
  }

  useEffect(() => {
    const documentIds = fetcherCreateDocument.data

    if (documentIds && fileFieldName) {
      const data: Record<string, string> = {}
      documentIds.forEach((documentId, index) => {
        /*
         * To ensure proper document handling, all document keys must:
         * - Begin with "documentId"
         * - Be separated by underscores ("_")
         * - End with an index to uniquely identify each document
         */
        const keyName = `documentId_${fileFieldName}_${index}`
        data[keyName] = documentId
      });
      fetcherUpdateDocument.submit({
        data: JSON.stringify(data),
        location: location.pathname,
      }, {
        action: `/economic-substance/${id}/pages/${Pages.INTELLECTUAL_PROPERTY_BUSINESS}/documents/update`,
        method: "post",
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcherCreateDocument.data])

  useEffect(() => {
    if (documentsArray) {
      // Group documents by type
      const businessPlanDocumentFiles = groupDocuments("files_BusinessPlanDocument", documentsArray);
      const evidenceDocumentFiles = groupDocuments("files_EvidenceDocument", documentsArray);
      const additionalEvidenceFiles = groupDocuments("files_AdditionalEvidence", documentsArray);
      // Update form values
      setValue("files_BusinessPlanDocument", businessPlanDocumentFiles.length > 0 ? { files: businessPlanDocumentFiles } : undefined);
      setValue("files_EvidenceDocument", evidenceDocumentFiles.length > 0 ? { files: evidenceDocumentFiles } : undefined);
      setValue("files_AdditionalEvidence", additionalEvidenceFiles.length > 0 ? { files: additionalEvidenceFiles } : undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setValue]);

  return (
    <>
      {fileFieldName && (
        <FileUploadDialog
          form={fileForm}
          open={openUploadFile}
          setOpen={setOpenUploadFile}
          onSubmit={onSubmitFile}
        />
      )}
      <p className="text-md font-bold">Intellectual Property</p>
      <FormField
        control={form.control}
        name="isHighRiskIpEntity"
        render={({ field, fieldState }) => (
          <FormItem>
            <Tooltip delayDuration={0}>
              <FormLabel>
                <p className="flex gap-1">
                  Is the entity a high-risk intellectual property entity?*
                  <TooltipTrigger asChild>
                    <Info className="flex shrink-0 size-4" />
                  </TooltipTrigger>
                </p>
              </FormLabel>
              <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                <p>
                  A “high risk IP legal entity” is defined in ESA section 2 as a legal
                  entity which carries on an intellectual property business and which:
                </p>
                <div>
                  (a) acquired the intellectual property asset
                  <ul className="list-disc pl-10">
                    <li>
                      (i) from an affiliate; or
                    </li>
                    <li>
                      (ii) inconsideration for funding research and development by another person
                      situated
                      in a country or territory other than the Virgin Islands; and
                    </li>
                  </ul>
                  (b) licences the intellectual property asset to one or more affiliates
                  or otherwise generates income from the asset in consequence of activities
                  (such as facilitating sale agreements) performed by foreign affiliates
                </div>
              </TooltipContent>
            </Tooltip>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                value={field.value}
                invalid={!!fieldState.error}
                className="flex flex-row space-x-2"
                disabled={isSubmitting}
              >
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="true" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Yes
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-2 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="false" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    No
                  </FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="relevantIpAsset"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Identify the relevant IP asset which it holds*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/3 sm:w-full">
              <Input
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="incomeGenerationExplanation"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Explain how the intellectual property is being used to generate income.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/2 sm:w-full">
              <Textarea
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="employeeResponsibility"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                Identify the decisions for which each employee is responsible
                for in respect of the generation of income from the intellectual property.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/2 sm:w-full">
              <Textarea
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="strategicDecisionsBahamas"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                The nature and history of the strategic decisions (if any) taken by the entity in the
                Bahamas*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/2 sm:w-full">
              <Textarea
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="tradingActivitiesBahamas"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              <p className="flex gap-1">
                The nature and history of the trading activities (if any carried out in the Bahamas by
                which)
                the intellectual property assets is exploited for the purpose of generating income from
                third parties.*
              </p>
            </FormLabel>
            <FormControl className="md:w-1/2 sm:w-full">
              <Textarea
                invalid={!!fieldState.error}
                {...field}
                disabled={isSubmitting}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {isHighRiskIpEntity === "true" && (
        <>
          <FormField
            control={form.control}
            name="grossIncomeRoyalties"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>
                  <p className="flex gap-1">
                    Gross income through Royalties, if applicable.
                  </p>
                </FormLabel>
                <FormControl className="md:w-1/3 sm:w-full">
                  <CurrencyInput
                    currencyName={Currency.USD}
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                    type="number"
                    min={0}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="grossIncomeSaleIpAsset"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>
                  <p className="flex gap-1">
                    Gross income through Gains from sale of IP asset, if applicable.
                  </p>
                </FormLabel>
                <FormControl className="md:w-1/3 sm:w-full">
                  <CurrencyInput
                    currencyName={Currency.USD}
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                    type="number"
                    min={0}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="grossIncomeOtherSources"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>
                  <p className="flex gap-1">
                    Gross income through Others, if applicable.
                  </p>
                </FormLabel>
                <FormControl className="md:w-1/3 sm:w-full">
                  <CurrencyInput
                    currencyName={Currency.USD}
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                    type="number"
                    min={0}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="businessPlanExplanation"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>
                  <p className="flex gap-1">
                    Provide detailed business plans which explain the commercial rationale of
                    holding the intellectual property assets in the Bahamas.*
                  </p>
                </FormLabel>
                <FormControl className="md:w-1/2 sm:w-full">
                  <Textarea
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="files_BusinessPlanDocument"
            render={() => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  <p className="flex gap-1">
                    Upload business plan document*
                  </p>
                </FormLabel>
                <FormControl className="md:w-fit sm:w-full">
                  <Button
                    size="sm"
                    onClick={() => onOpenUploadDialog("files_BusinessPlanDocument")}
                    type="button"
                    disabled={isSubmitting}
                  >
                    <Plus className="size-4 mr-2" />
                    {`${files_BusinessPlanDocument ? "File uploaded" : "  Upload file"}`}
                  </Button>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="decisionMakingEvidenceExplanation"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>
                  <p className="flex gap-1">
                    Provide concrete evidence that decision-making is
                    taking place within the Bahamas, including but not limited to, minutes of
                    meetings which have taken place in the Bahamas.*
                  </p>
                </FormLabel>
                <FormControl className="md:w-1/2 sm:w-full">
                  <Textarea
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="files_EvidenceDocument"
            render={() => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  <p className="flex gap-1">
                    Upload evidence document*
                  </p>
                </FormLabel>
                <FormControl className="md:w-fit sm:w-full">
                  <Button
                    size="sm"
                    onClick={() => onOpenUploadDialog("files_EvidenceDocument")}
                    type="button"
                    disabled={isSubmitting}
                  >
                    <Plus className="size-4 mr-2" />
                    {`${files_EvidenceDocument ? "File uploaded" : "  Upload file"}`}
                  </Button>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="additionalComplianceExplanation"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel>
                  <p className="flex gap-1">
                    Any other facts and matters necessary to demonstrate compliance?*
                  </p>
                </FormLabel>
                <FormControl className="md:w-1/2 sm:w-full">
                  <Textarea
                    invalid={!!fieldState.error}
                    {...field}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="files_AdditionalEvidence"
            render={() => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  <p className="flex gap-1">
                    Optional: Upload evidence document
                  </p>
                </FormLabel>
                <FormControl className="md:w-fit sm:w-full">
                  <Button
                    size="sm"
                    onClick={() => onOpenUploadDialog("files_AdditionalEvidence")}
                    type="button"
                    disabled={isSubmitting}
                  >
                    <Plus className="size-4 mr-2" />
                    {`${files_AdditionalEvidence ? "File uploaded" : "  Upload file"}`}
                  </Button>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}
    </>
  )
}
