import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@netpro/design-system";
import { useNavigate } from "@remix-run/react";
import { Calendar, CreditCard } from "lucide-react";
import type { ReactNode } from "react";

export function PendingPaymentsTabs({ currentTab }: { currentTab: "invoices" | "annual-invoices" }): ReactNode {
  const navigate = useNavigate();

  return (
    <Tabs defaultValue={currentTab} onValueChange={value => navigate(`/payments/pending/${value}`)} className="mb-5">
      <TabsList>
        <TabsTrigger value="invoices">
          <span className="flex items-center space-x-2">
            <CreditCard className="-ml-0.5 mr-2 size-5" />
            Unpaid Invoices
          </span>
        </TabsTrigger>
        <TabsTrigger value="annual-invoices">
          <span className="flex items-center space-x-2">
            <Calendar className="-ml-0.5 mr-2 size-5" />
            Unpaid Annual Invoices
          </span>
        </TabsTrigger>
      </TabsList>
    </Tabs>
  )
}
