import type { ReactNode } from "react";
import { formatDate, getTimezoneFromJurisdiction, shortMonthFormat } from "~/lib/utilities/format";
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";

export function BoDirCell<T extends Partial<DirectorDTO> | Partial<BeneficialOwnerDTO>>({
  field,
  item,
  jurisdictionName,
}: {
  field: string
  item: T
  jurisdictionName?: string
}): ReactNode {
  const isDateField = (key: string): key is "appointmentDate" | "dateOfBirth" | "dateOfIncorporation" =>
    ["appointmentDate", "dateOfBirth", "dateOfIncorporation"].includes(key);

  const timezone = getTimezoneFromJurisdiction(jurisdictionName);

  return (
    <>
      {isDateField(field) && item[field as keyof T]
        ? formatDate(String(item[field as keyof T]), { timezone, formatStr: shortMonthFormat })
        : typeof item[field as keyof T] === "string"
          ? (item[field as keyof T] as string)
          : ""}

      {field === "countryOfBirth" && item.countryOfBirth && item.placeOfBirth && (
        <span className="ml-1">
          {`(${item.placeOfBirth})`}
        </span>
      )}
    </>
  );
}
