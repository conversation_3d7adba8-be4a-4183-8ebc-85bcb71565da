import type { ReactNode } from "react";
import { formatDate } from "~/lib/utilities/format";
import type { BeneficialOwnerDTO, DirectorDTO } from "~/services/api-generated";

export function BoDirCell<T extends Partial<DirectorDTO> | Partial<BeneficialOwnerDTO>>({
  field,
  item,
}: {
  field: string
  item: T
}): ReactNode {
  const isDateField = (key: string): key is "appointmentDate" | "dateOfBirth" | "dateOfIncorporation" =>
    ["appointmentDate", "dateOfBirth", "dateOfIncorporation"].includes(key);

  return (
    <>
      {isDateField(field) && item[field as keyof T]
        ? formatDate(new Date(String(item[field as keyof T])).toISOString())
        : typeof item[field as keyof T] === "string"
          ? (item[field as keyof T] as string)
          : ""}

      {field === "countryOfBirth" && item.countryOfBirth && item.placeOfBirth && (
        <span className="ml-1">
          {`(${item.placeOfBirth})`}
        </span>
      )}
    </>
  );
}
