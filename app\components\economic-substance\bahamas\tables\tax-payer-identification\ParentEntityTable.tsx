import { <PERSON><PERSON>, <PERSON>roll<PERSON><PERSON>, <PERSON>rollBar, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { Pencil, X } from "lucide-react";
import type { JSX } from "react"
import type { ParentEntitySchemaType } from "~/lib/economic-substance/types/bahamas/tax-payer-identification-schema";

type Props = {
  parentEntities: (ParentEntitySchemaType & { formArrayId: string })[]
  onSelect: (income: ParentEntitySchemaType, index: number) => void
  onDelete: (index: number) => void
  disabled: boolean
}

export function ParentEntityTable({
  parentEntities,
  onSelect,
  onDelete,
  disabled,
}: Props): JSX.Element {
  return (
    <div className="border-gray-200 border mt-4">
      <ScrollArea>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Parent Entity Name</TableHead>
              <TableHead>Parent Entity Alternative Name</TableHead>
              <TableHead>Parent Entity's Jurisdiction of Formation</TableHead>
              <TableHead>Parent Entity's Incorporation Number</TableHead>
              <TableHead>Parent Entity Taxpayer Identification Number</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {!parentEntities.length && (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500">
                  No parent entities available
                </TableCell>
              </TableRow>
            )}
            {parentEntities.length > 0 && parentEntities.map((pe, index) => (
              <TableRow key={pe.formArrayId}>
                <TableCell>{pe.name}</TableCell>
                <TableCell>{pe.alternativeName}</TableCell>
                <TableCell>{pe.jurisdictionOfFormation}</TableCell>
                <TableCell>{pe.incorporationNumber}</TableCell>
                <TableCell>{pe.taxpayerIdentificationNumber}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  <Button type="button" size="sm" variant="secondary" onClick={() => onSelect(pe, index)} disabled={disabled}>
                    <Pencil className="mr-2 size-4" />
                    Edit
                  </Button>
                  <Button type="button" size="sm" variant="destructive" onClick={() => onDelete(index)} disabled={disabled}>
                    <X className="mr-2 size-4" />
                    Remove
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
