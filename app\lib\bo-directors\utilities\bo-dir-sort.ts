export type SortConfig = {
  key: string
  direction: "ascending" | "descending"
};

export function sortItems<T>(items: T[], sortConfig: SortConfig | null): T[] {
  if (!sortConfig) {
    return items;
  }

  return [...items].sort((a, b) => {
    const { key, direction } = sortConfig;
    if (a[key as keyof T] < b[key as keyof T]) {
      return direction === "ascending" ? -1 : 1;
    }

    if (a[key as keyof T] > b[key as keyof T]) {
      return direction === "ascending" ? 1 : -1;
    }

    return 0;
  });
}
