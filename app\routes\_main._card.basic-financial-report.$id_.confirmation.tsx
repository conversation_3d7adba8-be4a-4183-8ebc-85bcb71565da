import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { type ReactNode, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Separator } from "@netpro/design-system";
import { ChevronRight, Download } from "lucide-react";
import { Breadcrumb } from "~/components/breadcrumbs/Breadcrumb";
import { SubmissionStatusNames } from "~/lib/submission/utilities/submission-status";
import { middleware } from "~/lib/middlewares.server";
import type { DocumentDTO } from "~/services/api-generated";
import { clientGetSubmission, getApiV1CommonDocumentsByDocumentId } from "~/services/api-generated";
import { getUnflattenedDataSet } from "~/lib/submission/utilities/submission-data-set-auto";

import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { fileDataToUrl } from "~/lib/utilities/files";
import { authHeaders } from "~/lib/auth/utils/auth-headers";

const title = "Submission for" as const;
const breadCrumbList = [
  {
    href: "/",
    name: "Basic Financial Report",
  },
];

export const handle = {
  breadcrumb: (): ReactNode => <Breadcrumb data={breadCrumbList} />,
  title,
};

export async function loader({ request, params }: LoaderFunctionArgs): Promise<TypedResponse<never> | {
  submissionId: string
  isPaid: boolean | null | undefined
  summaryReport: DocumentDTO | undefined
}> {
  await middleware(["auth", "mfa", "terms", "requireMcc", "requireCompany", "requireBfrModule"], request);
  const { id } = params;

  if (!id) {
    throw new Error("Submission ID is required");
  }

  const { data: submission } = await clientGetSubmission({
    headers: await authHeaders(request),
    path: { submissionId: id },
    query: { includeFormDocument: true },
  });

  if (!submission) {
    throw new Error("Submission not found");
  }

  // Validate finalize step and status
  if (submission.status !== SubmissionStatusNames.Submitted) {
    throw new Error("Submission is not in submitted status");
  }

  const submissionData = getUnflattenedDataSet(submission);
  const documentId = submissionData[Pages.FINANCIAL_PERIOD].summaryReportDocumentId
  let summaryReport
  if (submission.documentIds?.length && documentId) {
    const { data: document } = await getApiV1CommonDocumentsByDocumentId({
      headers: await authHeaders(request),
      path: { documentId },
    });

    summaryReport = document
  }

  return { submissionId: id, isPaid: submission.isPaid, summaryReport };
}

export default function BasicFinancialReportConfirmation(): ReactNode {
  const { submissionId, isPaid, summaryReport } = useLoaderData<typeof loader>();
  const [summaryReportUrl, setSummaryReportUrl] = useState<string | undefined>()

  useEffect(() => {
    if (summaryReport) {
      const url = fileDataToUrl(summaryReport)
      setSummaryReportUrl(url)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div className="w-full flex flex-col gap-5 px-4 pt-1 pb-5">
      <div className="flex flex-col gap-1">
        <p>
          Thank you for submitting your declaration.
        </p>
        <p>
          Click download for the summary of the declaration
          {isPaid ? "." : " or proceed to pay to complete your submission."}
        </p>
      </div>
      {!isPaid && (
        <Alert variant="info" title="Important note">
          Please note that the declaration will not be submitted by your Registered Agent to the tax office until payment
          is received.
        </Alert>
      )}
      <div className="flex gap-x-2">
        {summaryReportUrl && (
          <Button type="button" size="sm" asChild>
            <Link to={summaryReportUrl} reloadDocument>
              <Download className="size-4 mr-2 text-white" />
              Download
            </Link>
          </Button>
        )}
        {!summaryReportUrl && (
          <Button type="button" size="sm" asChild>
            <Link to={`/basic-financial-report/${submissionId}/summary`} target="_blank">
              <Download className="size-4 mr-2 text-white" />
              Download
            </Link>
          </Button>
        )}
      </div>
      <div>
        <Separator />
        <div className="flex justify-end space-x-2 py-4">
          {isPaid
            ? (
                <Button type="button" asChild>
                  <Link to="/basic-financial-report/submissions">
                    Back to submissions
                    <ChevronRight className="size-4 ml-2 text-white" />
                  </Link>
                </Button>
              )
            : (
                <Button type="button" asChild>
                  <Link to="/payments/pending">
                    Proceed to Pending Payments
                    <ChevronRight className="size-4 ml-2 text-white" />
                  </Link>
                </Button>
              )}
        </div>
      </div>
    </div>
  );
}
