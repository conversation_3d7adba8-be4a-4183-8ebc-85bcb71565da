import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import type { ReactNode } from "react";
import { Outlet, json } from "@remix-run/react";
import { z } from "zod";
import { commitSession } from "~/lib/auth/utils/session.server";
import { userPreferences } from "~/lib/cookies.server";
import type { PageRange } from "~/lib/hooks/usePaginationParams";
import { PAGINATION } from "~/lib/hooks/usePaginationParams";
import { middleware } from "~/lib/middlewares.server";
import { flashNotification } from "~/lib/utilities/flash-notification";

const PageSizeSchema = z.object({
  // tablePageSize should be in the array PAGINATION.PAGE_RANGE
  tablePageSize: z
    .number({
      message: "Not a valid page size number.",
    })
    .optional()
    .refine(value => PAGINATION.PAGE_RANGE.includes(value as PageRange), {
      message: `Page size must be one of ${PAGINATION.PAGE_RANGE.join(", ")}`,
    }),
})

export async function action({ request }: LoaderFunctionArgs): Promise<TypedResponse> {
  await middleware(["auth"], request);

  const body = await request.formData()
  const validatedResult = PageSizeSchema.safeParse({
    tablePageSize: Number(body.get("tablePageSize") ?? 0),
  });
  if (!validatedResult.success) {
    const session = await flashNotification(request, {
      variant: "error",
      message: validatedResult.error.errors[0].message,
    });

    return json({
      errors: validatedResult.error.errors[0].message,
    }, {
      status: 400,
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    })
  }

  const cookieHeader = request.headers.get("Cookie");
  const cookie = (await userPreferences.parse(cookieHeader)) || {};
  const tablePageSize = body.get("tablePageSize");

  if (tablePageSize != null) {
    cookie.tablePageSize = tablePageSize
  }

  return json(null, { headers: {
    "Set-Cookie": await userPreferences.serialize(cookie),
  } })
}

export default function Layout(): ReactNode {
  return <Outlet />;
}
