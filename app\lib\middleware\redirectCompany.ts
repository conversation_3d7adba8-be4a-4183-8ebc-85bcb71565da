import { redirect } from "@remix-run/node";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";
import requireCompany from "~/lib/middleware/requireCompany";

export default async function redirectCompany(
  { request }: MiddlewareProps,
): MiddlewareResponse {
  try {
    await requireCompany({ request });
  } catch (ResponseError) {
    throw redirect("/companies");
  }
}
