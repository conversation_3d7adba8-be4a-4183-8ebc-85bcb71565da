import { Button, Checkbox, Label } from "@netpro/design-system";
import { Link } from "@remix-run/react";
import { FileDown } from "lucide-react";
import type { InvoiceDTO } from "~/services/api-generated";

type PaymentRowProps = {
  payment: InvoiceDTO
  currencyId: string | undefined
  selectedPayments: string[]
  onSelect: (id: string, amount: number | undefined, checked: boolean, currencyId: string | undefined) => void
};

export function PaymentRow({ payment, onSelect, currencyId, selectedPayments }: PaymentRowProps): JSX.Element {
  const handleSelect = (checked: boolean): void => {
    onSelect(payment.id!, payment.amount, checked, payment.currencyId);
  }

  return (
    <div
      className="flex items-center justify-between py-3 border-2 border-transparent hover:border-blue-600 group px-5 transition-all duration-200 rounded-md"
      onClick={() => handleSelect(!selectedPayments.includes(payment.id!))}
    >
      <div className="flex items-center space-x-4">
        <Checkbox
          id={payment.id}
          checked={selectedPayments.includes(payment.id!)}
          disabled={Boolean(currencyId) && payment.currencyId !== currencyId}
        />
        <Label
          htmlFor={payment.id}
        />
        <div>
          <p className="text-lg font-semibold">{payment.companyName}</p>
          <div className="flex">
            <div className="flex border-gray-600 border-opacity-10 pr-2 border-r-2 mr-2 ">
              <div className="flex space-x-2">
                <p className="text-sm text-gray-600">Incorporation number</p>
                <p className="text-sm text-gray-600 font-semibold">{payment.incorporationNr}</p>
              </div>
            </div>
            <div className="flex">
              <div className="flex space-x-2">
                <p className="text-sm text-gray-600">Financial year</p>
                <p className="text-sm text-gray-600 font-semibold">{payment.financialYear}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <div className="grid grid-cols-2">
          <div>
            <Button variant="outline" asChild>
              <Link to={`/invoices/${payment.id}/file`} target="_blank">
                <FileDown className="text-blue-500 size-4 mr-2" />
                {" "}
                Invoice
              </Link>
            </Button>
          </div>
          <div className="flex items-center justify-end">
            <div className="text-end">
              <h3 className="font-semibold w-full text-sm">
                {`${payment.currencySymbol} ${payment.amount}`}
              </h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
