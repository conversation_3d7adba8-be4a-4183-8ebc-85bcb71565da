import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle, Form, FormControl, FormField, FormItem, FormLabel, FormMessage, Label, RadioGroup, RadioGroupItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Form as RemixForm, useFetcher, useNavigation } from "@remix-run/react";
import { Info, Plus } from "lucide-react";
import type { ReactNode } from "react";
import { useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { CashBankAccountDialog } from "../../dialogs/current-assets-details/CashBankAccountDialog";
import { CashBankAccountTable } from "../../tables/current-assets-details/CashBankAccountTable";
import { Pages } from "~/lib/basic-financial-report/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { useScrollToError } from "~/lib/utilities/use-scroll-to-error";
import { ValidationAlert } from "~/components/errors/ValidationAlert";
import { BASIC_FINANCIAL_REPORT_FORM_ID } from "~/lib/basic-financial-report/utilities/constants";
import { CurrencyInput } from "~/components/ui/inputs/CurrencyInput";
import { Currency } from "~/lib/basic-financial-report/utilities/currencies";
import type { CashBankAccountSchemaType, CurrentAssetsDetailsSchemaType } from "~/lib/basic-financial-report/types/current-assets-details-schema";
import { cashBankAccountSchema, currentAssetsDetailsSchema } from "~/lib/basic-financial-report/types/current-assets-details-schema";
import { useCalculation } from "~/lib/basic-financial-report/hooks/current-assets-details/use-calculation";
import { formatDate } from "~/lib/utilities/format";

export function CurrentAssetsDetails(): ReactNode {
  const { submissionData, setCanContinue } = useSubmission();
  const { cashReceivedIncome, cashSetupCapital, cashPaidExpenses, cashReceivedPaidLoans, cashReceivedInvestmentsSold, cashPaidInvestmentsPurchased, cashReceivedSalePaidNonCurrentAssets } = useCalculation(submissionData)
  const startDate = formatDate(submissionData[Pages.FINANCIAL_PERIOD].startFiscalYear);
  const data = useMemo(() => submissionData[Pages.CURRENT_ASSETS_DETAILS] as CurrentAssetsDetailsSchemaType, [submissionData]);
  const navigation = useNavigation()
  const isSubmitting = navigation.state === "submitting" || navigation.state === "loading"
  const [open, setOpen] = useState(false);
  const [openDeletedConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [incomeIndex, setIncomeIndex] = useState<number | undefined>();
  const form = useForm<CurrentAssetsDetailsSchemaType>({
    resolver: zodResolver(currentAssetsDetailsSchema),
    shouldFocusError: false,
    defaultValues: {
      cashSetupCapital: cashSetupCapital.toString(),
      cashReceivedIncome: cashReceivedIncome.toString(),
      cashPaidExpenses: cashPaidExpenses.toString(),
      cashReceivedPaidLoans: cashReceivedPaidLoans.toString(),
      cashReceivedInvestmentsSold: cashReceivedInvestmentsSold.toString(),
      cashPaidInvestmentsPurchased: cashPaidInvestmentsPurchased.toString(),
      cashReceivedSalePaidNonCurrentAssets: cashReceivedSalePaidNonCurrentAssets.toString(),
    },
  });
  const watchCashBankAccounts = form.watch("cashBankAccounts")
  // These values cannot be retrieved from default values because they need this current form calculations
  const totalCashBalance = watchCashBankAccounts ? Number((watchCashBankAccounts.reduce((acc, cur) => acc + Number(cur.amount), 0))) : 0
  const otherCashTransactions = totalCashBalance - (cashSetupCapital + cashReceivedIncome + cashPaidExpenses + cashReceivedPaidLoans + cashReceivedInvestmentsSold + cashPaidInvestmentsPurchased + cashReceivedSalePaidNonCurrentAssets)
  const assetForm = useForm<CashBankAccountSchemaType>({
    resolver: zodResolver(cashBankAccountSchema),
    defaultValues: { bankName: "", amount: "" },
  });
  const cashBankAccountArray = useFieldArray({
    control: form.control,
    name: "cashBankAccounts",
    keyName: "formArrayId",
  });
  const { reset, formState } = form;
  const [canFocus, setCanFocus] = useState(true)
  useScrollToError(formState, canFocus, setCanFocus);

  function onError(): void {
    setCanFocus(true)
  }

  useEffect(() => {
    if (data) {
      reset({ companyCash: data.companyCash, cashBankAccounts: data.cashBankAccounts, reviewPreviousInfo: data.reviewPreviousInfo }, { keepDefaultValues: true });
    }
  }, [data, reset]);

  function addCashBankAccount(): void {
    assetForm.reset();

    setIncomeIndex(undefined);
    setOpen(true);
  }

  function onSubmitCashBankAccount(data: CashBankAccountSchemaType): void {
    if (incomeIndex !== undefined) {
      cashBankAccountArray.update(incomeIndex, data);
    } else {
      cashBankAccountArray.append(data);
    }

    setOpen(false);
  }

  function onSelect(income: CashBankAccountSchemaType, index: number): void {
    assetForm.reset(income, { keepDefaultValues: true });
    setIncomeIndex(index);
    setOpen(true);
  }

  function onDelete(): void {
    cashBankAccountArray.remove(incomeIndex);

    setOpenDeleteConfirmation(false);
  }

  function onOpenDeleteConfirmation(index: number): void {
    setIncomeIndex(index);
    setOpenDeleteConfirmation(true);
  }

  function onCloseDeleteConfirmation(): void {
    setIncomeIndex(undefined);
    setOpenDeleteConfirmation(false);
  }

  const fetcher = useFetcher();

  function onSubmit(data: CurrentAssetsDetailsSchemaType): void {
    fetcher.submit({ data: JSON.stringify(data) }, {
      method: "post",
    });
  }

  const handleCompanyCashChange = (value: string) => {
    if (value === "false") {
      form.setValue("cashBankAccounts", [])
    }
  }
  const watchCompanyCash = form.watch("companyCash")
  const watchReviewPreviousInfo = form.watch("reviewPreviousInfo")

  useEffect(() => {
    if (watchReviewPreviousInfo === "true") {
      setCanContinue(false)
    }

    if (watchReviewPreviousInfo === "false") {
      setCanContinue(true)
    }
  }, [setCanContinue, watchReviewPreviousInfo])

  return (
    <>
      <CashBankAccountDialog
        open={open}
        setOpen={setOpen}
        form={assetForm}
        onSubmit={onSubmitCashBankAccount}
      />
      <Dialog open={openDeletedConfirmation} onOpenChange={setOpenDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure you want to delete the property?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={onCloseDeleteConfirmation} disabled={isSubmitting}>Cancel</Button>
            <Button type="button" variant="destructive" onClick={onDelete} disabled={isSubmitting}>Yes, delete this property</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Form {...form}>
        <RemixForm onSubmit={form.handleSubmit(onSubmit, onError)} noValidate id={BASIC_FINANCIAL_REPORT_FORM_ID} className="space-y-5">
          <div className="flex-col space-y-7">
            <div>
              <Tooltip delayDuration={0}>
                <Label>
                  <p className="flex gap-1">
                    Current Assets
                    <TooltipTrigger asChild>
                      <Info className="flex shrink-0 size-4" />
                    </TooltipTrigger>
                  </p>
                </Label>
                <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                  <p>
                    Current Assets is any asset which can reasonably be expected to be sold, consumed or exhausted through the normal operations of the business with the current fiscal year or operating cycle or financial year.
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <FormField
              control={form.control}
              name="companyCash"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Does the company own any cash or cash equivalents?*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value)
                        handleCompanyCashChange(value)
                      }}
                      value={field.value}
                      invalid={!!fieldState.error}
                      disabled={isSubmitting}
                      className="flex flex-row space-x-2"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            { watchCompanyCash === "true" && (
              <>
                <FormField
                  name="cashBankAccounts"
                  control={form.control}
                  render={({ fieldState }) => (
                    <FormItem>
                      <FormLabel>
                        <p>{`Please indicate the balance as at ${startDate}:`}</p>
                      </FormLabel>
                      {fieldState.invalid && <ValidationAlert fieldState={fieldState} />}
                      <FormControl>
                        <CashBankAccountTable
                          cashBankAccounts={cashBankAccountArray.fields}
                          onSelect={onSelect}
                          onDelete={onOpenDeleteConfirmation}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="flex justify-end">
                  <Button size="sm" onClick={addCashBankAccount} type="button" disabled={isSubmitting}>
                    <Plus className="mr-2 size-4 text-white" />
                    Add Cash/Bank Account
                  </Button>
                </div>
              </>
            )}
            <div className="space-y-3">
              <div>
                <Tooltip delayDuration={0}>
                  <Label>
                    <p className="flex gap-1">
                      Bank Reconciliation
                      <TooltipTrigger asChild>
                        <Info className="flex shrink-0 size-4" />
                      </TooltipTrigger>
                    </p>
                  </Label>
                  <TooltipContent className="w-96 p-5 space-y-3 font-inter" side="right">
                    <p>
                      A bank reconciliation is the process by which the bank account balance in an entity’s books of account is reconciled to the balance reported by the financial institution in the most recent bank statement.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div>HERE IS THE LIST OF THE TRANSACTION/S, FROM PREVIOUS PAGES, AFFECTING THE CASH BALANCE OF THE ENTITY. FOR REVIEW PURPOSES ONLY.</div>
            </div>
            <FormField
              control={form.control}
              name="cashSetupCapital"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Cash Set-up for Capital</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cashReceivedIncome"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Cash Received from Income</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cashPaidExpenses"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Cash Paid for Expenses</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cashReceivedPaidLoans"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Cash Received/(Paid) from Loans</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cashReceivedInvestmentsSold"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Cash Received for Investments Solds</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cashPaidInvestmentsPurchased"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Cash Paid for Investments Purchased</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cashReceivedSalePaidNonCurrentAssets"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Cash Received from Sale/(Paid for Purchase) of Non-Current Assets</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      type="number"
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="otherCashTransactions"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Other Cash Transactions</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      value={otherCashTransactions}
                      type="number"
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="totalCashBalance"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel>
                    <p>Total Cash Balance</p>
                  </FormLabel>
                  <FormControl className="lg:w-1/3 md:w-1/2 sm:w-full">
                    <CurrencyInput
                      currencyName={Currency.USD}
                      invalid={!!fieldState.error}
                      {...field}
                      value={totalCashBalance}
                      type="number"
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div>
              <Label>
                {`The Total Cash Balance is not reconciled by $ ${otherCashTransactions}`}
              </Label>
            </div>
            <FormField
              control={form.control}
              name="reviewPreviousInfo"
              render={({ field, fieldState }) => (
                <FormItem className="space-y-3 py-2">
                  <FormLabel>
                    <p className="flex gap-1">
                      Do you want to review previous information entered in the Trident Accounting Records tool?*
                    </p>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      invalid={!!fieldState.error}
                      className="flex flex-row space-x-2"
                      disabled={isSubmitting}
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="true" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Yes
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="false" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          No
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {watchReviewPreviousInfo === "true" && <div>Please go back to the previous screens and make the adjustments.</div>}
            {watchReviewPreviousInfo === "false" && <div>{`The amount of $ ${otherCashTransactions} will be classified as "Other Cash Transactions" and will be recorded as "Other Expenses".`}</div>}
          </div>
        </RemixForm>
      </Form>
    </>
  )
}
