import { Table, TableBody, Table<PERSON>ell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import type { JSX } from "react";
import type { CorporateAccountingInformationType } from "~/lib/simplified-tax-return/types/corporate-accounting-records/2021/corporate-accounting-records-schema";
import { Pages } from "~/lib/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/lib/submission/context/use-submission";
import { formatYesNoBoolean } from "~/lib/utilities/format";

export function CorporateAccountingRecordsSummary(): JSX.Element {
  const { submissionData } = useSubmission();
  const corporateAccountingInformation = submissionData[Pages.CORPORATE_ACCOUNTING_RECORDS] as CorporateAccountingInformationType;

  return (
    <section id="corporate-details-section">
      <Table className="pointer-events-none border border-blue-600">
        <TableBody>
          <TableRow className="border border-blue-600">
            <TableCell className="w-2/3 py-1">
              <span>
                Have you generated any income in the financial reporting period that would be
                assessable in the absence of the exemptions conferred under
                Section 224 of the Companies Act, Section 136 of the Nevis Business Corporations Ordinance or
                Section 96 of the Nevis Limited Liability Company Ordinance?
              </span>
            </TableCell>
            <TableCell className="w-1/3 text-center py-1">
              <span className="font-semibold ">{formatYesNoBoolean(corporateAccountingInformation?.assessableIncomeGenerated) ?? "N/A"}</span>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="w-2/3 py-1 ">
              <div className="flex flex-col">
                <span>
                  i. has there been a change in your activities since December 31, 2018; or
                </span>
                <span>
                  ii. have you acquired new assets after December 31, 2018?
                </span>
              </div>
            </TableCell>
            <TableCell className="w-1/3 text-center py-1">
              <span className="font-semibold ">{formatYesNoBoolean(corporateAccountingInformation?.activitiesCondition) ?? "N/A"}</span>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      {corporateAccountingInformation?.assessableIncomeGenerated === "true" && corporateAccountingInformation?.activitiesCondition === "false" && (
        <Table className="pointer-events-none ">
          <TableHeader>
            <TableRow className="text-center">
              <TableHead className="w-1/6 text-center font-semibold text-black border border-blue-600 py-1 ">
                Description of assessable income for the calendar year 2021
              </TableHead>
              <TableHead className="w-1/6 text-center font-semibold text-black border border-blue-600 py-1 ">
                Related party intellectual property
              </TableHead>
              <TableHead className="w-1/6 text-center font-semibold text-black border border-blue-600 py-1 ">
                Non-Related intellectual property
              </TableHead>
              <TableHead className="w-1/6 text-center font-semibold text-black border border-blue-600 py-1 ">
                Non intellectual property
              </TableHead>
              <TableHead className="w-1/6 text-center font-semibold text-black border border-blue-600 py-1 ">
                Amount (XCD) of assessable income for the calendar year 2021
              </TableHead>
              <TableHead className="w-1/6 text-center font-semibold text-black border border-blue-600 py-1 ">
                Amount of assessable income earned Jan - Jun 2021
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {
              corporateAccountingInformation?.accountingActivities?.map(activity => (
                <TableRow key={activity.description}>
                  <TableCell className="w-1/6 text-center border border-blue-600 py-1 ">
                    <span>{activity.description}</span>
                  </TableCell>
                  <TableCell className="w-1/6 text-center border border-blue-600 py-1">
                    <span>{formatYesNoBoolean(activity.relatedPartyIntellectualProperty)}</span>
                  </TableCell>
                  <TableCell className="w-1/6 text-center border border-blue-600 py-1">
                    <span>{formatYesNoBoolean(activity.nonRelatedIntellectualProperty)}</span>
                  </TableCell>
                  <TableCell className="w-1/6 text-center border border-blue-600 py-1">
                    <span>{formatYesNoBoolean(activity.nonIntellectualProperty)}</span>
                  </TableCell>
                  <TableCell className="w-1/6 text-center border border-blue-600 py-1">
                    <span>
                      $
                      {" "}
                      {activity.incomeYear}
                    </span>
                  </TableCell>
                  <TableCell className="w-1/6 text-center border border-blue-600 py-1">
                    <span>
                      $
                      {" "}
                      {activity.incomeJanuaryJune}
                    </span>
                  </TableCell>
                </TableRow>
              ))
            }
          </TableBody>
        </Table>
      )}
    </section>
  )
}
